package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.param.UserPackageParam;
import cn.iocoder.yudao.aiBase.dto.response.AiAppResponse;
import cn.iocoder.yudao.aiBase.dto.response.AiAppUserResponse;
import cn.iocoder.yudao.aiBase.dto.response.AiUserPackageResponse;
import cn.iocoder.yudao.aiBase.dto.response.FeeTypeVo;
import cn.iocoder.yudao.aiBase.entity.AiAppLangs;
import cn.iocoder.yudao.aiBase.entity.AiAppUserPackage;
import cn.iocoder.yudao.aiBase.entity.AiAppUsers;
import cn.iocoder.yudao.aiBase.mapper.AiAppUserPackageMapper;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.dal.dataobject.config.ConfigDO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiAppUserPackageServiceImpl extends ServiceImpl<AiAppUserPackageMapper, AiAppUserPackage> implements AiAppUserPackageService {

    @Autowired
    private MedsciUsersService medsciUsersService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Autowired
    private RedisManage redisManage;

    @Autowired
    private OauthService oauthService;

    @Autowired
    private AiAppUsageRecordsService aiAppUsageRecordsService;

    @Autowired
    @Lazy
    private AiAppLangsService aiAppLangsService;

    @Autowired
    private AiAppUsersService aiAppUsersService;

    @Override
    public PageResult<AiAppUserPackage> selectPage(UserPackageParam reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    @Override
    public List<AiAppUserPackage> selectList(UserPackageParam param) {
        return baseMapper.selectList(param);
    }

    @Override
    public AiAppUserPackage getUserPackage(Long socialUserId, Integer socialType, String packageKey, String packageType) {
        UserPackageParam param = UserPackageParam.builder().socialUserId(socialUserId).socialType(socialType).packageKey(packageKey).packageType(packageType).build();
        List<AiAppUserPackage> list = selectList(param);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public AiAppUserPackage getUserPackage(Long socialUserId, Integer socialType) {
        return getUserPackage(socialUserId, socialType, PACKAGE_ALL_APPS, FeeTypeVo.FREE_TYPE);
    }

    /**
     * 获取用户订阅中的套餐，同packageKey下只有一个订阅中（或取消中）
     *
     * @param socialUserId
     * @param socialType
     * @param packageKey
     * @return
     */
    @Override
    public AiAppUserPackage getUserPackageOnSub(Long socialUserId, Integer socialType, String packageKey) {
        UserPackageParam param = UserPackageParam.builder().socialUserId(socialUserId).socialType(socialType).packageKey(packageKey).subStatus(BaseConstant.ONE).build();
        List<AiAppUserPackage> list = selectList(param);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    /**
     * 获取用户取消中的套餐，同packageKey下只有一个取消中（或订阅中）。
     * @param socialUserId
     * @param socialType
     * @param packageKey
     * @return
     */
    @Override
    public AiAppUserPackage getUserPackageOnCancel(Long socialUserId, Integer socialType, String packageKey) {
        UserPackageParam param = UserPackageParam.builder().socialUserId(socialUserId).socialType(socialType).packageKey(packageKey).subStatus(BaseConstant.THREE).build();
        List<AiAppUserPackage> list = selectList(param);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public AiAppUserPackage getSubOrCancel(Long socialUserId, Integer socialType, String packageKey) {
        AiAppUserPackage userPackage = getUserPackageOnSub(socialUserId, socialType, packageKey);
        if (userPackage == null) {
            userPackage = getUserPackageOnCancel(socialUserId, socialType, packageKey);
        }
        return userPackage;
    }

    @Override
    public AiAppUserPackage getByCheckoutSessionId(String checkoutSessionId) {
        List<AiAppUserPackage> list = selectList(UserPackageParam.builder().checkoutSessionId(checkoutSessionId).build());
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public AiAppUserPackage getOrCreateUserPackage(Long socialUserId, Integer socialType, String packageKey, String packageType) {
        AiAppUserPackage userPackage = getUserPackage(socialUserId, socialType, packageKey, packageType);
        if (userPackage == null) {
            LocalDateTime now = LocalDateTime.now();
            userPackage = new AiAppUserPackage();
            userPackage.setSocialUserId(socialUserId);
            userPackage.setSocialType(socialType);
            userPackage.setPackageKey(packageKey);
            userPackage.setPackageType(packageType);
            userPackage.setCheckoutSessionId(BaseConstant.EMPTY_STR);
            userPackage.setSubStatus(BaseConstant.ZERO); // 默认为取消状态
            userPackage.setCreatedAt(now);
            userPackage.setUpdatedAt(now);
            baseMapper.insert(userPackage);
        }
        return userPackage;
    }

    @Override
    public void handleTask(LocalDateTime end) {
        //  订阅中 -> 已过期
        UserPackageParam reqVO = UserPackageParam.builder().subStatus(BaseConstant.ONE)
                .expiredAt(new LocalDateTime[]{end, end.plusDays(BaseConstant.ONE)}).build();
        List<AiAppUserPackage> list = selectList(reqVO);
        list.forEach(item -> {
            item.setSubStatus(BaseConstant.TWO);
            item.setCheckoutSessionId(BaseConstant.EMPTY_STR);
            item.setUpdatedAt(LocalDateTime.now());
            updateById(item);
        });

        // 退订中 -> 已退订
        reqVO.setSubStatus(BaseConstant.THREE);
        list = selectList(reqVO);
        list.forEach(item -> {
            item.setUnSubAt(null);
            item.setSubStatus(BaseConstant.ZERO);
            item.setCheckoutSessionId(BaseConstant.EMPTY_STR);
            item.setUpdatedAt(LocalDateTime.now());
            updateById(item);
        });
    }

    @Override
    public AiUserPackageResponse getUserPackage(String auth, String configKey, String locale) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);

        AiUserPackageResponse res = new AiUserPackageResponse();
        res.setSubStatus(BaseConstant.ZERO);

        if (authUser != null) {
            if (PACKAGE_ALL_APPS.equals(configKey) || BaseConstant.ZH.equals(locale)) {
                AiAppUserPackage userPackage = getSubOrCancel(authUser.getUserId(), authUser.getUserType(), configKey);
                if (userPackage != null) {
                    res = BeanUtils.toBean(userPackage, AiUserPackageResponse.class);
                    res.setSubAt(res.getSubAt()==null?null:userPackage.getSubAt().format(CommonUtil.DateTimeFormat2));
                    res.setUnSubAt(res.getUnSubAt()==null?null:userPackage.getUnSubAt().format(CommonUtil.DateTimeFormat2));
                    res.setStartAt(res.getStartAt()==null?null:userPackage.getStartAt().format(CommonUtil.DateTimeFormat1));
                    res.setExpireAt(res.getExpireAt()==null?null:userPackage.getExpireAt().format(CommonUtil.DateTimeFormat1));
                }
                res.setFeeTypes(getFeeTypes(configKey));
            } else {
                // 非中文应用
                AiAppResponse appLang = aiAppLangsService.toBean(aiAppLangsService.getByNameEn(configKey, locale));
                if (appLang != null) {
                    AiAppUsers appUser = aiAppUsersService.getAppUser(authUser.getUserId(), authUser.getUserType(), appLang.getAppUuid());
                    if (appUser != null && appUser.getExpireAt() != null) {
                        appLang.setAppUser(BeanUtils.toBean(appUser, AiAppUserResponse.class,
                                item1 -> {
                                    item1.setExpireAt(appUser.getExpireAt().format(CommonUtil.DateTimeFormat1));
                                }));
                    }
                    res.setSubStatus(appLang.getAppUser()==null?BaseConstant.ZERO:Integer.valueOf(appLang.getAppUser().getStatus()));
                    res.setFeeTypes(appLang.getFeeTypes());
                    // todo 补全
                }
            }
        } else {
            // 目前只有 小智 未登录可以获取套餐
            if (PACKAGE_ALL_APPS.equals(configKey) || BaseConstant.ZH.equals(locale)) {
                res.setFeeTypes(getFeeTypes(configKey));
            }
        }

        return res;
    }

    @Override
    public Map<String, AiUserPackageResponse> getPackageByDomain(String auth, String configKey, String locale) {
        List<String> nameEns = new ArrayList<>();
        if (StringUtils.isNotBlank(configKey)) {
            // 查找配置的活动apps
            String apps = yudaoSystemService.getConfigByKeyFromCache("apps", configKey+".apps");
            if (apps != null) {
                nameEns.addAll(JSONObject.parseArray(apps, String.class));
            }
        }

        Map<String, AiUserPackageResponse> res = new HashMap<>();
        if (nameEns.isEmpty()) {
            return res;
        }

        for (String nameEn : nameEns) {
            AiUserPackageResponse item = getUserPackage(auth, nameEn, locale);
            res.put(nameEn, item);
        }

        return res;
    }

    @Override
    public FeeTypeVo getFeeType(String packageKey, String packageType) {
        FeeTypeVo feeType = null;
        if (!StringUtils.isBlank(packageKey) && !StringUtils.isBlank(packageType)) {
            feeType = getFeeTypes(packageKey).stream()
                    .filter(fee -> packageType.equals(fee.getType()))
                    .findFirst()
                    .orElse(null);
        }
        return feeType;
    }

    /**
     * 只用于中文应用
     * @param configKey 套餐键名
     * @return
     */
    @Override
    public List<FeeTypeVo> getFeeTypes(String configKey) {
        try {
            String value = redisManage.getFeeTypes(configKey);
            if (value == null) {
                ConfigDO configDO = yudaoSystemService.getConfigByKey(configKey);
                if (configDO == null) {
                    value = JsonUtils.toJsonString(new ArrayList<>());
                } else {
                    value = configDO.getValue();
                }
                redisManage.setFeeTypes(configKey, value);
            }

            return JSONArray.parseArray(value).toJavaList(FeeTypeVo.class)
                    .stream().filter(feeType -> BaseConstant.ONE.equals(feeType.getOnline()))
                    .map(feeTypeVo -> feeTypeVo.setPackageKey(configKey))
                    .toList();
        } catch (Exception e) {
            log.error("[getFeeTypes][获取失败]", e);
            return new ArrayList<>();
        }
    }

    @Override
    public String getPeriodType(String packageKey, String packageType) {
        FeeTypeVo feeType = getFeeType(packageKey, packageType);
        return feeType != null ? feeType.getPeriodType() : null;
    }

    @Override
    public ErrorCode preCheck(OAuth2AccessTokenCheckRespDTO authUser, String appUuid, String requestId) {
        if (requestId == null || redisManage.getRequestId(requestId) != null) {
            log.info("[preCheck][1.请求ID校验（防止重复请求）]:{}", requestId);
            return ErrorCodeConstants.ERROR_5048;
        }

        String isInternalUser = medsciUsersService.getIsInternalUser(authUser.getUserId(), authUser.getUserType());
        if (BaseConstant.ONE.toString().equals(isInternalUser)) {
            log.info("[preCheck][2.获取内部用户：直接使用]:{}", requestId);
            return null;
        }

        Boolean isUXO = aiAppLangsService.checkIsUXOFromCache(authUser.getUserId(), authUser.getUserType(), appUuid);
        if (isUXO) {
            log.info("[preCheck][3.获取体验官权限：直接使用]:{}", requestId);
            return null;
        }

        String userAppPackage = aiAppLangsService.getUserAppPackage(authUser.getUserId(), authUser.getUserType(), appUuid);
        String[] userAppPackageArr = userAppPackage.split(BaseConstant.COLON_STR);
        if (BaseConstant.PLACE_HOLDER.equals(userAppPackageArr[0])) {
            log.info("[preCheck][4.非中文应用，绑定即可以使用]:{}", requestId);
            return null;
        }

        // 5. 按配置类型执行不同校验逻辑
        String packageType = BaseConstant.ONE.equals(userAppPackageArr.length) ? BaseConstant.EMPTY_STR : userAppPackageArr[1];
        if (PACKAGE_ALL_APPS.equals(userAppPackageArr[0])) {
            return checkPackageAllAppsLogic(authUser, appUuid, requestId, packageType);
        } else {
            return checkSpecificAppLogic(authUser, appUuid, requestId, packageType);
        }
    }

    /**
     * 处理"所有应用"类型的预校验逻辑
     */
    private ErrorCode checkPackageAllAppsLogic(OAuth2AccessTokenCheckRespDTO authUser, String appUuid, String requestId, String packageType) {
        if (FeeTypeVo.FREE_TYPE.equals(packageType)) {
            // 只是免费套餐，则记录使用情况，判断使用次数
            int usageRecordStatus = aiAppUsageRecordsService.recordAppUsage(authUser.getUserId(), authUser.getUserType(), appUuid, requestId);
            if (usageRecordStatus == -1) {
                log.info("[preCheck][5.1.使用次数超限]:{}-{}", requestId, packageType);
                return ErrorCodeConstants.ERROR_5052;
            }
            return null;
        }

        // 付费套餐
        return null;
    }

    /**
     * 处理特定应用的预校验逻辑
     */
    private ErrorCode checkSpecificAppLogic(OAuth2AccessTokenCheckRespDTO authUser, String appUuid, String requestId, String packageType) {
        if (StringUtils.isBlank(packageType)) {
            // 用户对此应用无特定套餐订阅
            // 检查活动 免费使用限制
            Integer freeNum = aiAppLangsService.getFreeNum(appUuid);
            int useNum = aiAppUsageRecordsService.countAll(authUser.getUserId(), authUser.getUserType(), appUuid);
            if (useNum < freeNum) {
                aiAppUsageRecordsService.addUsageRecord(authUser.getUserId(), authUser.getUserType(), appUuid, requestId);
                return null;
            }
            log.info("[preCheck][5.2.使用次数超限]:{}-{}", requestId, packageType);
            return ErrorCodeConstants.ERROR_5052;
        }

        // 用户对此应用有免费套餐订阅
        if (FeeTypeVo.FREE_TYPE.equals(packageType)) {
            // base版本不处理
            return null;
        }

        // pro版已付费
        return null;
    }

    @Override
    public void initUserPackage(String locale, Long socialUserId, Integer socialType, String packageKey, String packageType,
                                LocalDateTime startAt, LocalDateTime expireAt) {
        LocalDateTime now = LocalDateTime.now();

        if (BaseConstant.ZH.equals(locale)) {
            List<FeeTypeVo> feeTypes = getFeeTypes(packageKey);
            if (feeTypes.stream().anyMatch(feeType -> packageType.equals(feeType.getType()))) {
                //如果packageKey存在，则调用
                AiAppUserPackage userPackage = getOrCreateUserPackage(socialUserId, socialType, packageKey, packageType);

                // 1. 根据时间设置package状态，订阅状态
                userPackage.setStartAt(startAt);
                userPackage.setExpireAt(expireAt);
                userPackage.setSubStatus(BaseConstant.ONE);
                userPackage.setSubAt(now);
                userPackage.setUpdatedAt(now);

                // 更新package信息
                updateById(userPackage);
            }
        }

        AiAppLangs appLangs = aiAppLangsService.getByNameEn(packageKey, locale);
        if (appLangs != null) {
            AiAppUsers appUser = aiAppUsersService.getOrCreateAppUser(socialUserId, socialType, BaseConstant.EMPTY_STR, appLangs.getAppUuid());

            // 2. 根据时间设置appUser状态
            appUser.setStartAt(startAt);
            appUser.setExpireAt(expireAt);
            appUser.setStatus(BaseConstant.ONE);
            appUser.setUpdatedAt(now);

            // 更新appUser信息
            aiAppUsersService.updateById(appUser);
        }
    }

}
