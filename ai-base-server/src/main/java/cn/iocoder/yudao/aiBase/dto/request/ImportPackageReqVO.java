package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ImportPackageReqVO {

    @Schema(description = "语言")
    private String locale;

    @Schema(description = "社交用户ID")
    private String socialUserIdStr;

    @Schema(description = "社交用户ID")
    private Long socialUserId;

    @Schema(description = "社交类型")
    private Integer socialType;

    @Schema(description = "套餐键")
    private String packageKey;

    @Schema(description = "套餐类型")
    private String packageType;

    @Schema(description = "开始时间")
    private String startAt;

    @Schema(description = "过期时间")
    private String expireAt;
}
