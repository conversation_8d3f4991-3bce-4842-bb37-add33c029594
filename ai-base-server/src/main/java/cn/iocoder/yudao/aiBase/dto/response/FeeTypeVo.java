package cn.iocoder.yudao.aiBase.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FeeTypeVo {
    public static final String FREE_TYPE = "免费";
    public static final String FREE_PRICE_ID = "免费订阅";
    public static final String WEEK_TYPE = "周连续订阅";
    public static final String MONTH_TYPE = "连续包月";
    public static final String QUARTER_TYPE = "连续包季";
    public static final String YEAR_TYPE = "连续包年";
    public static final String PERIOD_DAY = "DAY";

    @Schema(description = "1开启")
    private Integer online;

    @Schema(description = "订阅套餐")
    private String packageKey;

    @Schema(description = "订阅方式")
    private String type;

    @Schema(description = "周期类型，默认月")
    private String periodType = "MONTH";

    @Schema(description = "时长")
    private Integer monthNum = 1;

    @Schema(description = "原价格")
    private BigDecimal oldPrice;

    @Schema(description = "价格")
    private BigDecimal feePrice;

    @Schema(description = "币种")
    private String coinType;

    @Schema(description = "价格ID，币种是人民币的是支付宝场景值，美元的是stripe")
    private String priceId;

    @Schema(description = "可使用次数，0不限制")
    private Integer num;
}
