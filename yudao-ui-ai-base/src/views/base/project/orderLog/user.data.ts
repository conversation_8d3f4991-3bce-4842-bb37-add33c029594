import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'
import { getUsersPage } from '@/api/medsciUsers'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '用户ID',
    dataIndex: 'socialUserId',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 140,
  },
  {
    title: '订单ID',
    dataIndex: 'piId',
    width: 200,
  },
  {
    title: '支付金额',
    dataIndex: 'payAmount',
    width: 150
  },
  {
    title: '收费模式',
    dataIndex: 'needPay',
    width: 150,
    customRender: ({ record }) => {
      return record.needPay == 0 ?'免费':"付费"
    }
  },
  {
    title: '支付状态',
    dataIndex: 'paymentStatus',
    width: 150,
  },
  {
    title: '本期开始时间',
    dataIndex: 'startAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '本期截止时间',
    dataIndex: 'expireAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text)
    }
  },
  {
    title: '修改时间',
    dataIndex: 'updatedAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    colProps: { span: 8 },
  },
  {
    label: '用户ID',
    field: 'socialUserId',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '搜索用户名、真实名、邮箱、手机号',
      api: getUsersPage,
      params: {
        pageNum: 1,
        pageSize: 10
      },
      resultField: 'list',
      labelField: 'userName',
      valueField: 'socialUserId',
      searchField: 'keyword',
      showSearch: true,
      filterOption: false,
    },
    colProps: { span: 8 },
  },
  {
    label: '订阅链接ID',
    field: 'checkoutSessionId',
    component: 'Input',
    colProps: { span: 8 }
  },

  {
    label: '创建时间',
    field: 'createdAt',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD 00:00:00',
    },
  },
  {
    label: '本期截止时间',
    field: 'expireAt',
    component: 'RangePicker',
    colProps: { span: 8 },
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD 00:00:00',
    },
  },
]
