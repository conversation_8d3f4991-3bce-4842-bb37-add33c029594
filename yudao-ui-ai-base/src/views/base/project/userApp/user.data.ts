import type {BasicColumn, FormSchema} from '@/components/Table'
import {useRender} from '@/components/Table'
import {DICT_TYPE, getDictOptions} from '@/utils/dict'
import { getUsersPage } from '@/api/medsciUsers'

export const columns: BasicColumn[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '三方平台',
    dataIndex: 'socialType',
    width: 60,
    customRender: ({ text }) => {
      return useRender.renderDict(text, DICT_TYPE.AI_BASE_SOCIAL_TYPE)
    }
  },
  {
    title: '用户ID',
    dataIndex: 'socialUserId',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 160,
  },
  {
    title: '应用名',
    dataIndex: 'appName',
    width: 160,
  },
  {
    title: '应用英文名',
    dataIndex: 'appNameEn',
    width: 160,
  },
  {
    title: '应用uuid',
    dataIndex: 'appUuid',
    width: 220,
  },
  {
    title: '订阅状态',
    dataIndex: 'status',
    width: 160,
  },
  {
    title: '开始时间',
    dataIndex: 'startAt',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '过期时间',
    dataIndex: 'expireAt',
    width: 160,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '修改时间',
    dataIndex: 'updatedAt',
    width: 180,
    customRender: ({ text }) => {
      return useRender.renderDate(text, 'YYYY-MM-DD HH:mm:ss')
    }
  },
]

export const searchFormSchema: FormSchema[] = [
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    colProps: { span: 8 },
  },
  {
    label: '用户ID',
    field: 'socialUserId',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '搜索用户名、真实名、邮箱、手机号',
      api: getUsersPage,
      params: {
        pageNum: 1,
        pageSize: 10
      },
      resultField: 'list',
      labelField: 'userName',
      valueField: 'socialUserId',
      searchField: 'keyword',
      showSearch: true,
      filterOption: false,
    },
    colProps: { span: 8 },
  },
  {
    label: '应用uuid',
    field: 'appUuid',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '过期时间',
    field: 'expiredAt',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD 00:00:00',
    },
    colProps: { span: 8 }
  },
]

export const createFormSchema: FormSchema[] = [  
  {
    label: '语言',
    field: 'locale',
    component: 'Select',
    componentProps: {
      options: [{'value': 'zh', 'label': '中文'}, {'value': 'en', 'label': '英文'}],
    },
    required: true,
  },
  {
    label: '三方平台',
    field: 'socialType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DICT_TYPE.AI_BASE_SOCIAL_TYPE),
    },
    required: true,
  },
  {
    label: '用户ID',
    field: 'socialUserIdStr',
    component: 'InputTextArea',
    required: true,
    helpMessage: '格式 [123,456]，中间不要有空格',
    defaultValue: '[123,456]',
  },
  {
    label: '套餐',
    field: 'packageKey',
    component: 'Input',
    required: true,
  },
  {
    label: '类型',
    field: 'packageType',
    component: 'Select',
    componentProps: {
      options: [{'value': '月连续订阅', 'label': '月连续订阅'}, {'value': '周连续订阅', 'label': '周连续订阅'}],
    },
    required: true,
  },
  {
    label: '开始时间',
    field: 'startAt',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    },
  },
  {
    label: '截止时间',
    field: 'expireAt',
    required: true,
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'x',
    },
  },
]

export const updateFormSchema: FormSchema[] = [
  ...createFormSchema,
  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    show: true,
  }
]