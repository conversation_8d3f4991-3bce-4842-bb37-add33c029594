/* empty css                  *//* empty css                 */import{s as e}from"./index-cb2d0648.js";import{r as a,aT as l,d as s,e as t,f as u,g as r,p as i,j as n,m as o,k as v,F as m,z as p,X as d,h as c,aV as f,i as x,a8 as y,D as j,E as h,I as g}from"./index-426ff91e.js";import{s as k}from"./index-2a1d440d.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},V=["innerHTML"],b={class:"flex justify-center mt-10"},z={__name:"index",setup(z){const E=a(""),S=a([]),T=a([]),A=a(5),C=()=>{if(a=E.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!E.value)return y.warning("请输入英文内容");var a;e({text:E.value}).then((e=>{e&&e&&e.data&&(S.value=e.data,S.value=k(S.value),A.value=5,H())}))},H=()=>{let e=[];5==A.value?(e=[0,5],A.value=3):3==A.value?(e=[5,8],A.value=2):2==A.value&&(e=[8,10],A.value=5),T.value=JSON.parse(JSON.stringify(S.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,z=g,S=l("copy");return s(),t("div",w,[u(y,{modelValue:r(E),"onUpdate:modelValue":a[0]||(a[0]=e=>i(E)?E.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:C},{default:o((()=>a[1]||(a[1]=[v("改 写")]))),_:1})]),r(T)&&r(T).length?(s(),t("div",_,[(s(!0),t(m,null,p(r(T),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,V),d((s(),c(z,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(r(f))])),_:2},1024)),[[S,e.text]])])))),128)),n("div",b,[u(k,{type:"primary",link:"",onClick:H},{default:o((()=>a[2]||(a[2]=[v("换一换")]))),_:1})])])):x("",!0)])}}};export{z as default};
