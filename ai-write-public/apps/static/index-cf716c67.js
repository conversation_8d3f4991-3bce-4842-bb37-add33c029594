/* empty css                  */import{g as e,i as a}from"./index-81a2d0cf.js";import{r as t,x as l,y as o,w as n,d as i,e as s,j as r,t as u,g as d,h as c,m as v,F as p,z as m,f,A as g,k as h,i as y,B as b,C as w,D as x,G as _,H as k,I,E as S,J as T,K as C,L as $,M as N,N as B,O as z,P as U,Q as q,R as j,o as A,S as V,T as O,U as E,V as R,q as M,W as L,X as D,Y as H,Z as P,a as W,u as F,b as J,$ as X,a0 as Z,a1 as Y,a2 as G,a3 as K,a4 as Q,c as ee,a5 as ae,p as te,a6 as le,a7 as oe,a8 as ne,a9 as ie,aa as se,ab as re,ac as ue,v as de}from"./index-adee5396.js";import{r as ce,a as ve,t as pe,f as me}from"./lang-1feecc31.js";/* empty css                  *//* empty css                  *//* empty css                 */import{c as fe,r as ge,g as he,s as ye,i as be,o as we,a as xe,n as _e,m as ke,b as Ie,u as Se,d as Te,e as Ce,f as $e,h as Ne,j as Be,k as ze,w as Ue,l as qe,p as je,t as Ae,q as Ve,v as Oe,x as Ee,y as Re,z as Me,A as Le,B as De,C as He,D as Pe,E as We,F as Fe,G as Je,H as Xe,I as Ze,J as Ye,K as Ge,L as Ke,M as Qe,N as ea,O as aa,P as ta,Q as la}from"./index-38ac6e4d.js";/* empty css                   */const oa={class:"p-3 flex-1 rounded-md"},na={class:"text-[14px] font-bold mb-2 text-gray-600"},ia={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]},currentItem:{type:Object,required:!1}},emits:["update:value"],setup(e,{expose:a,emit:C}){const $=t(l.get("userInfo")?JSON.parse(l.get("userInfo")):{}),N=o(),B=t([]),z=t({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),U=t(""),q=e,j=q.type,A=q.fileVerify,V=q.label,O=q.required,E=q.max_length,R=q.options;"file"==j&&(U.value=null),"file-list"==j&&(U.value=[]);const M={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},L=()=>{let e="";return A.forEach(((a,t)=>{t<A.length-1?e+=M[a].join(",")+",":e+=M[a].join(",")})),e},D=C,H=async()=>{var e,a,t,o,n,i;if(!l.get("userInfo")){localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const t=b();return t&&"zh-CN"!=t?N.push("/login"):window.addLoginDom(),null==(e=document.getElementsByTagName("input")[0])||e.blur(),null==(a=document.getElementsByTagName("textarea")[0])||a.blur(),!1}return!!(null==(o=null==(t=q.currentItem)?void 0:t.appUser)?void 0:o.status)||(D("payShowStatus",!0),null==(n=document.getElementsByTagName("input")[0])||n.blur(),null==(i=document.getElementsByTagName("textarea")[0])||i.blur(),!1)},P=(e,a,t)=>{},W=()=>{U.value=""},F=async e=>{var a;if(!(await H()))return!1;const{file:t,onSuccess:l,onError:o}=e,n=new FormData;n.append("file",t),n.append("appId",null==(a=q.currentItem)?void 0:a.dAppUuid),n.append("user",$.value.userName);try{const e=await w(n);"file-list"==j?U.value.push({type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):U.value={type:z.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},l(e,t)}catch(i){o(i)}return!1};R&&R.length>0&&(U.value=R[0]);return a({updateMessage:()=>{R&&R.length>0?U.value=R[0]:"file"==j?(U.value=null,B.value=[]):"file-list"==j?(U.value=[],B.value=[]):U.value=""}}),n(U,(e=>{D("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=x,l=_,o=k,n=I,b=S,w=T;return i(),s("div",oa,[r("div",na,u(d(V)),1),"paragraph"===d(j)||"text-input"===d(j)?(i(),c(t,{key:0,onFocus:H,modelValue:U.value,"onUpdate:modelValue":a[0]||(a[0]=e=>U.value=e),type:"paragraph"===d(j)?"textarea":"text",rows:5,required:d(O),placeholder:`${d(V)}`,"show-word-limit":"",resize:"none",maxlength:d(E)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===d(j)?(i(),c(t,{key:1,modelValue:U.value,"onUpdate:modelValue":a[1]||(a[1]=e=>U.value=e),modelModifiers:{number:!0},onFocus:H,type:"number",required:d(O),placeholder:`${d(V)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===d(j)?(i(),c(o,{key:2,onChange:H,modelValue:U.value,"onUpdate:modelValue":a[2]||(a[2]=e=>U.value=e),required:d(O),placeholder:`${d(V)}`},{default:v((()=>[(i(!0),s(p,null,m(d(R),(e=>(i(),c(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===d(j)||"file-list"===d(j)?(i(),c(w,{key:3,"file-list":B.value,"onUpdate:fileList":a[3]||(a[3]=e=>B.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":W,"before-remove":e.beforeRemove,limit:d(E),accept:L(),"auto-upload":!0,"on-Success":P,"http-request":F,"on-exceed":e.handleExceed},{default:v((()=>[f(b,{disabled:"file"===d(j)?1==B.value.length:B.value.length==d(E)},{default:v((()=>[f(n,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:v((()=>[f(d(g))])),_:1}),a[4]||(a[4]=h("从本地上传"))])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):y("",!0)])}}};let sa=0;function ra(){const e=C(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++sa}`}function ua(e,a){if(!be||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};N(l),B(l),we((()=>{e.value&&t.observe(e.value)}))}const[da,ca]=xe("sticky");const va=je(z({name:da,props:{zIndex:_e,position:ke("top"),container:Object,offsetTop:Ie(0),offsetBottom:Ie(0)},emits:["scroll","change"],setup(e,{emit:a,slots:l}){const o=t(),i=Se(o),s=U({fixed:!1,width:0,height:0,transform:0}),r=t(!1),u=q((()=>Te("top"===e.position?e.offsetTop:e.offsetBottom))),d=q((()=>{if(r.value)return;const{fixed:e,height:a,width:t}=s;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=q((()=>{if(!s.fixed||r.value)return;const a=Ce($e(e.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[e.position]:`${u.value}px`});return s.transform&&(a.transform=`translate3d(0, ${s.transform}px, 0)`),a})),v=()=>{if(!o.value||Be(o))return;const{container:t,position:l}=e,n=ze(o),i=he(window);if(s.width=n.width,s.height=n.height,"top"===l)if(t){const e=ze(t),a=e.bottom-u.value-s.height;s.fixed=u.value>n.top&&e.bottom>0,s.transform=a<0?a:0}else s.fixed=u.value>n.top;else{const{clientHeight:e}=document.documentElement;if(t){const a=ze(t),l=e-a.top-u.value-s.height;s.fixed=e-u.value<n.bottom&&e>a.top,s.transform=l<0?-l:0}else s.fixed=e-u.value<n.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:s.fixed})})(i)};return n((()=>s.fixed),(e=>a("change",e))),Ne("scroll",v,{target:i,passive:!0}),ua(o,v),n([Ue,qe],(()=>{o.value&&!Be(o)&&s.fixed&&(r.value=!0,j((()=>{const e=ze(o);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return f("div",{ref:o,style:d.value},[f("div",{class:ca({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=l.default)?void 0:e.call(l)])])}}})),[pa,ma]=xe("swipe"),fa={loop:Ae,width:_e,height:_e,vertical:Boolean,autoplay:Ie(0),duration:Ie(500),touchable:Ae,lazyRender:Boolean,initialSwipe:Ie(0),indicatorColor:String,showIndicators:Ae,stopPropagation:Ae},ga=Symbol(pa);const ha=je(z({name:pa,props:fa,emits:["change","dragStart","dragEnd"],setup(e,{emit:a,slots:l}){const o=t(),i=t(),s=U({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=Ve(),{children:d,linkChildren:c}=Oe(ga),v=q((()=>d.length)),p=q((()=>s[e.vertical?"height":"width"])),m=q((()=>e.vertical?u.deltaY.value:u.deltaX.value)),g=q((()=>{if(s.rect){return(e.vertical?s.rect.height:s.rect.width)-p.value*v.value}return 0})),h=q((()=>p.value?Math.ceil(Math.abs(g.value)/p.value):v.value)),y=q((()=>v.value*p.value)),b=q((()=>(s.active+v.value)%v.value)),w=q((()=>{const a=e.vertical?"vertical":"horizontal";return u.direction.value===a})),x=q((()=>{const a={transitionDuration:`${s.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(p.value){const t=e.vertical?"height":"width",l=e.vertical?"width":"height";a[t]=`${y.value}px`,a[l]=e[l]?`${e[l]}px`:""}return a})),_=(a,t=0)=>{let l=a*p.value;e.loop||(l=Math.min(l,-g.value));let o=t-l;return e.loop||(o=He(o,g.value,0)),o},k=({pace:t=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:n}=s,i=(a=>{const{active:t}=s;return a?e.loop?He(t+a,-1,v.value):He(t+a,0,h.value):t})(t),r=_(i,l);if(e.loop){if(d[0]&&r!==g.value){const e=r<g.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=i,s.offset=r,o&&i!==n&&a("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Le((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let T;const C=()=>clearTimeout(T),$=()=>{C(),+e.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),$()}),+e.autoplay))},z=(a=+e.initialSwipe)=>{if(!o.value)return;const t=()=>{var t,l;if(!Be(o)){const a={width:o.value.offsetWidth,height:o.value.offsetHeight};s.rect=a,s.width=+(null!=(t=e.width)?t:a.width),s.height=+(null!=(l=e.height)?l:a.height)}v.value&&-1===(a=Math.min(v.value-1,a))&&(a=v.value-1),s.active=a,s.swiping=!0,s.offset=_(a),d.forEach((e=>{e.setOffset(0)})),$()};Be(o)?j().then(t):t()},O=()=>z(s.active);let E;const R=a=>{!e.touchable||a.touches.length>1||(u.start(a),r=!1,E=Date.now(),C(),I())},M=()=>{if(!e.touchable||!s.swiping)return;const t=Date.now()-E,l=m.value/t;if((Math.abs(l)>.25||Math.abs(m.value)>p.value/2)&&w.value){const a=e.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=e.loop?a>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/p.value),k({pace:t,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,a("dragEnd",{index:b.value}),$()},L=(a,t)=>{const l=t===b.value,o=l?{backgroundColor:e.indicatorColor}:void 0;return f("i",{style:o,class:ma("indicator",{active:l})},null)};return Ee({prev:()=>{I(),u.reset(),Le((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:O,swipeTo:(a,t={})=>{I(),u.reset(),Le((()=>{let l;l=e.loop&&a===v.value?0===s.active?0:a:a%v.value,t.immediate?Le((()=>{s.swiping=!1})):s.swiping=!1,k({pace:l-s.active,emitChange:!0})}))}}),c({size:p,props:e,count:v,activeIndicator:b}),n((()=>e.initialSwipe),(e=>z(+e))),n(v,(()=>z(s.active))),n((()=>e.autoplay),$),n([Ue,qe,()=>e.width,()=>e.height],O),n(Re(),(e=>{"visible"===e?$():C()})),A(z),V((()=>z(s.active))),Me((()=>z(s.active))),N(C),B(C),Ne("touchmove",(t=>{if(e.touchable&&s.swiping&&(u.move(t),w.value)){!e.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(De(t,e.stopPropagation),k({offset:m.value}),r||(a("dragStart",{index:b.value}),r=!0))}}),{target:i}),()=>{var a;return f("div",{ref:o,class:ma()},[f("div",{ref:i,style:x.value,class:ma("track",{vertical:e.vertical}),onTouchstartPassive:R,onTouchend:M,onTouchcancel:M},[null==(a=l.default)?void 0:a.call(l)]),l.indicator?l.indicator({active:b.value,total:v.value}):e.showIndicators&&v.value>1?f("div",{class:ma("indicators",{vertical:e.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ya,ba]=xe("tabs");var wa=z({name:ya,props:{count:Pe(Number),inited:Boolean,animated:Boolean,duration:Pe(_e),swipeable:Boolean,lazyRender:Boolean,currentIndex:Pe(Number)},emits:["change"],setup(e,{emit:a,slots:l}){const o=t(),i=e=>a("change",e),s=()=>{var a;const t=null==(a=l.default)?void 0:a.call(l);return e.animated||e.swipeable?f(ha,{ref:o,loop:!1,class:ba("track"),duration:1e3*+e.duration,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:i},{default:()=>[t]}):t},r=a=>{const t=o.value;t&&t.state.active!==a&&t.swipeTo(a,{immediate:!e.inited})};return n((()=>e.currentIndex),r),A((()=>{r(e.currentIndex)})),Ee({swipeRef:o}),()=>f("div",{class:ba("content",{animated:e.animated||e.swipeable})},[s()])}});const[xa,_a]=xe("tabs"),ka={type:ke("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:Ie(0),duration:Ie(.3),animated:Boolean,ellipsis:Ae,swipeable:Boolean,scrollspy:Boolean,offsetTop:Ie(0),background:String,lazyRender:Ae,showHeader:Ae,lineWidth:_e,lineHeight:_e,beforeChange:Function,swipeThreshold:Ie(5),titleActiveColor:String,titleInactiveColor:String},Ia=Symbol(xa);var Sa=z({name:xa,props:ka,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:a,slots:l}){let o,i,s,r,u;const d=t(),c=t(),v=t(),p=t(),m=ra(),g=Se(d),[h,y]=function(){const e=t([]),a=[];return $((()=>{e.value=[]})),[e,t=>(a[t]||(a[t]=a=>{e.value[t]=a}),a[t])]}(),{children:b,linkChildren:w}=Oe(Ia),x=U({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=q((()=>b.length>+e.swipeThreshold||!e.ellipsis||e.shrink)),k=q((()=>({borderColor:e.color,background:e.background}))),I=(e,a)=>{var t;return null!=(t=e.name)?t:a},S=q((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),T=q((()=>Te(e.offsetTop))),C=q((()=>e.sticky?T.value+o:0)),N=a=>{const t=c.value,l=h.value;if(!(_.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,n=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,t){let l,o=0;const n=e.scrollLeft,i=0===t?1:Math.round(1e3*t/16);let s=n;return function t(){s+=(a-n)/i,e.scrollLeft=s,++o<i&&(l=ge(t))}(),function(){fe(l)}}(t,n,a?0:+e.duration)},B=()=>{const a=x.inited;j((()=>{const t=h.value;if(!t||!t[x.currentIndex]||"line"!==e.type||Be(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:n}=e,i=l.offsetLeft+l.offsetWidth/2,s={width:We(o),backgroundColor:e.color,transform:`translateX(${i}px) translateX(-50%)`};if(a&&(s.transitionDuration=`${e.duration}s`),Fe(n)){const e=We(n);s.height=e,s.borderRadius=e}x.lineStyle=s}))},z=(t,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(t);if(!Fe(o))return;const n=b[o],i=I(n,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||N(),B()),i!==e.active&&(a("update:active",i),r&&a("change",i,n.title)),s&&!e.scrollspy&&Xe(Math.ceil(Ze(d.value)-T.value))},A=(e,a)=>{const t=b.find(((a,t)=>I(a,t)===e)),l=t?b.indexOf(t):0;z(l,a)},O=(a=!1)=>{if(e.scrollspy){const t=b[x.currentIndex].$el;if(t&&g.value){const l=Ze(t,g.value)-C.value;i=!0,u&&u(),u=function(e,a,t,l){let o,n=he(e);const i=n<a,s=0===t?1:Math.round(1e3*t/16),r=(a-n)/s;return function t(){n+=r,(i&&n>a||!i&&n<a)&&(n=a),ye(e,n),i&&n<a||!i&&n>a?o=ge(t):l&&(o=ge(l))}(),function(){fe(o)}}(g.value,l,a?0:+e.duration,(()=>{i=!1}))}}},E=(t,l,o)=>{const{title:n,disabled:i}=b[l],s=I(b[l],l);i||(Ye(e.beforeChange,{args:[s],done:()=>{z(l),O()}}),ce(t)),a("clickTab",{name:s,title:n,event:o,disabled:i})},R=e=>{s=e.isFixed,a("scroll",e)},M=()=>{if("line"===e.type&&b.length)return f("div",{class:_a("line"),style:x.lineStyle},null)},L=()=>{var a,t,o;const{type:n,border:i,sticky:s}=e,r=[f("div",{ref:s?void 0:v,class:[_a("wrap"),{[Je]:"line"===n&&i}]},[f("div",{ref:c,role:"tablist",class:_a("nav",[n,{shrink:e.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(a=l["nav-left"])?void 0:a.call(l),b.map((e=>e.renderTitle(E))),M(),null==(t=l["nav-right"])?void 0:t.call(l)])]),null==(o=l["nav-bottom"])?void 0:o.call(l)];return s?f("div",{ref:v},[r]):r},D=()=>{B(),j((()=>{var e,a;N(!0),null==(a=null==(e=p.value)?void 0:e.swipeRef.value)||a.resize()}))};n((()=>[e.color,e.duration,e.lineWidth,e.lineHeight]),B),n(Ue,D),n((()=>e.active),(e=>{e!==S.value&&A(e)})),n((()=>b.length),(()=>{x.inited&&(A(e.active),B(),j((()=>{N(!0)})))}));return Ee({resize:D,scrollTo:e=>{j((()=>{A(e),O(!0)}))}}),V(B),Me(B),we((()=>{A(e.active,!0),j((()=>{x.inited=!0,v.value&&(o=ze(v.value).height),N(!0)}))})),ua(d,B),Ne("scroll",(()=>{if(e.scrollspy&&!i){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=ze(b[e].$el);if(a>C.value)return 0===e?0:e-1}return b.length-1})();z(e)}}),{target:g,passive:!0}),w({id:m,props:e,setLine:B,scrollable:_,onRendered:(e,t)=>a("rendered",e,t),currentName:S,setTitleRefs:y,scrollIntoView:N}),()=>f("div",{ref:d,class:_a([e.type])},[e.showHeader?e.sticky?f(va,{container:d.value,offsetTop:T.value,onScroll:R},{default:()=>[L()]}):L():null,f(wa,{ref:p,count:b.length,inited:x.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:x.currentIndex,onChange:z},{default:()=>{var e;return[null==(e=l.default)?void 0:e.call(l)]}})])}});const Ta=Symbol(),[Ca,$a]=xe("tab"),Na=z({name:Ca,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:_e,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:Ae},setup(e,{slots:a}){const t=q((()=>{const a={},{type:t,color:l,disabled:o,isActive:n,activeColor:i,inactiveColor:s}=e;l&&"card"===t&&(a.borderColor=l,o||(n?a.backgroundColor=l:a.color=l));const r=n?i:s;return r&&(a.color=r),a})),l=()=>{const t=f("span",{class:$a("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||Fe(e.badge)&&""!==e.badge?f(Ge,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>f("div",{id:e.id,role:"tab",class:[$a([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[Ba,za]=xe("swipe-item");const Ua=je(z({name:Ba,setup(e,{slots:a}){let t;const l=U({offset:0,inited:!1,mounted:!1}),{parent:o,index:n}=Ke(ga);if(!o)return;const i=q((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),s=q((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const i=o.activeIndicator.value,s=o.count.value-1,r=0===i&&e?s:i-1,u=i===s&&e?0:i+1;return t=n.value===i||n.value===r||n.value===u,t}));return A((()=>{j((()=>{l.mounted=!0}))})),Ee({setOffset:e=>{l.offset=e}}),()=>{var e;return f("div",{class:za(),style:i.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[qa,ja]=xe("tab");const Aa=je(z({name:qa,props:Ce({},ve,{dot:Boolean,name:_e,badge:_e,title:String,disabled:Boolean,titleClass:Qe,titleStyle:[String,Object],showZeroBadge:Ae}),setup(e,{slots:a}){const l=ra(),o=t(!1),i=C(),{parent:s,index:r}=Ke(Ia);if(!s)return;const u=()=>{var a;return null!=(a=e.name)?a:r.value},d=q((()=>{const a=u()===s.currentName.value;return a&&!o.value&&(o.value=!0,s.props.lazyRender&&j((()=>{s.onRendered(u(),e.title)}))),a})),c=t(""),v=t("");O((()=>{const{titleClass:a,titleStyle:t}=e;c.value=a?E(a):"",v.value=t&&"string"!=typeof t?R(M(t)):t}));const p=t(!d.value);return n(d,(e=>{e?p.value=!1:Le((()=>{p.value=!0}))})),n((()=>e.title),(()=>{s.setLine(),s.scrollIntoView()})),L(Ta,d),Ee({id:l,renderTitle:t=>f(Na,P({key:l,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:l,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:e=>t(i.proxy,r.value,e)},ea(s.props,["type","color","shrink"]),ea(e,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const t=`${s.id}-${r.value}`,{animated:n,swipeable:i,scrollspy:u,lazyRender:c}=s.props;if(!a.default&&!n)return;const v=u||d.value;if(n||i)return f(Ua,{id:l,role:"tabpanel",class:ja("panel-wrapper",{inactive:p.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":t,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[f("div",{class:ja("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=o.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return D(f("div",{id:l,role:"tabpanel",class:ja("panel"),tabindex:v?0:-1,"aria-labelledby":t,"data-allow-mismatch":"attribute"},[m]),[[H,v]])}}})),Va=je(Sa),Oa={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Ea={class:"pc_container",style:{display:"flex"}},Ra={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ma={class:"p-3",style:{display:"flex","justify-content":"space-between"}},La={class:"pc_right bg-[#fff]"},Da={id:"typing-area"},Ha={key:0,class:"decContaniner nop bg-[#fff]"},Pa={key:0,class:"img_box"},Wa=["src"],Fa={key:1,class:"icon"},Ja={class:"process_text label_width"},Xa={key:0,class:"process"},Za={key:0,class:"img_box"},Ya=["src"],Ga={key:1,class:"icon"},Ka={class:"process"},Qa={class:"process_text"},et={key:2},at=["src"],tt=["src"],lt={class:"mobile_container"},ot={class:"p-3",style:{display:"flex","justify-content":"space-between"}},nt={class:"mobile_right"},it={id:"typing-area"},st={key:0,class:"decContaniner nop bg-[#fff]"},rt={key:0,class:"img_box"},ut=["src"],dt={key:1,class:"icon"},ct={class:"process_text label_width"},vt={key:0,class:"img_box"},pt=["src"],mt={key:1,class:"icon"},ft={class:"process"},gt={class:"process_text"},ht={key:2},yt=W({__name:"index",props:{currentItem:{type:Object,default:()=>{}}},setup(g){const w=g,x=e("loading.png"),_=e("copy.png"),k=U({}),T=F(),C={},$=t([]),N=t(l.get("userInfo")?JSON.parse(l.get("userInfo")):{}),B=t(null),{t:z,locale:j}=J(),V=o(),O=t(!1);let R=t("a"),M=t(!0),L=t("");const D=t(""),H=t(null),P=t(null),W=t(["1","2"]),ce=t(!1),ve=t(!1),fe=t(!1);let ge;const he=t(""),ye=t(""),be=async()=>{var e;await le({appId:he.value,user:N.value.userName,mode:null==(e=B.value)?void 0:e.mode,task_id:D.value}),setTimeout((()=>{Je.abort(),Xe=!0,Ve.value=[],ce.value=!1,Oe.value.length&&Oe.value.forEach((e=>{e.status=!0})),ea()}),0)},we=()=>{fe.value=!1},xe=async(e,a)=>{var t;let l=b();if(null==(t=N.value)?void 0:t.userId){const t={appUuid:a,priceId:e.priceId,monthNum:e.monthNum};let l=await oe(t);l&&(ne({type:"success",message:z("tool.sS")}),setTimeout((()=>{location.href=l}),1e3))}else l&&"zh-CN"!=l?V.push((isUp,"/login")):window.addLoginDom()};A((async()=>{var e,t;const o=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${o}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=T.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=T.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),j.value=X(),qe();const n=await Z(T.params.appUuid);he.value=n.dAppUuid,ye.value=n.appName,l.get("userInfo")&&await da(),Se(),a(n.customCss,n.customJs)}));const _e=q((()=>`<${ye.value}>${pe[localStorage.getItem("ai_apps_lang")]}`));Y({title:_e});const ke=e=>{R.value=e.name},Ie=async()=>{fe.value=!0},Se=()=>{he.value&&G({appId:he.value,user:N.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&($.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;C[t]={label:e[a].label},k[t]=""})))}))},Te=q((()=>!!$.value.length)),Ce=q((()=>$.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),$e=()=>{he.value&&ie({appId:he.value,user:N.value.userName}).then((e=>{B.value={...e}}))},Ne=t(!1),Be=t(!1),ze=t(!1),Ue=t(!1),qe=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=N.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:he.value,userUuid:null==(a=N.value)?void 0:a.openid}];await K.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},je=e=>{fe.value=e},Ae=async()=>{var e,a,t,o;if(l.get("userInfo")){if(await da(),!(null==(a=null==(e=w.currentItem)?void 0:e.appUser)?void 0:a.status))return fe.value=!0,!1;if(0!=Ce.value.length||(n=k,Object.values(n).some((e=>e)))){var n;for(let e in k)if(Ce.value.includes(e)&&!k[e])return void ne({message:`${C[e].label}${z("tool.requiredfield")}`,type:"error"});(null==(t=B.value)?void 0:t.mode)&&(["advanced-chat","chat"].includes(null==(o=B.value)?void 0:o.mode)?ne({type:"success",message:z("tool.planning")}):"completion"==B.value.mode?(M.value=!1,setTimeout((()=>{R.value="b"}),1e3),Qe()):(M.value=!1,setTimeout((()=>{R.value="b"}),1e3),Ke()))}else ne({message:`${z("tool.enterquestion")}`,type:"error"})}else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=b();e&&"zh-CN"!=e?V.push("/login"):window.addLoginDom()}},Ve=t([]);var Oe=t([]),Ee=t([]);const Re=t(""),Me=t(0),Le=t(""),De=t(!1),He=t(!1);let Pe=t(0);const We=t(!1),Fe=t(!1);let Je,Xe=!1,Ze=!1;n(Oe,(()=>{Ye()}),{deep:!0});const Ye=()=>{Pe.value<Oe.value.length&&(Ee.value.push(Oe.value[Pe.value]),Pe.value++,setTimeout(Ye,1e3))},Ge=()=>{Me.value<Re.value.length?(De.value=!0,Le.value+=Re.value.charAt(Me.value),Me.value++,setTimeout(Ge,20)):(Fe.value=!1,De.value=!1,ze.value=!0,ea())},Ke=async()=>{ve.value=!0,ce.value=!0,L.value="",Oe.value=[],Ee.value=[],Pe.value=0,Le.value="",Re.value="",Ve.value=[],We.value=!1,Xe=!1,Me.value=0,Je=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,Ne.value=!0,Be.value=!0,Ue.value=!1,await me(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:he.value,user:N.value.userName,inputs:{...k,outputLanguage:k.outputLanguage?k.outputLanguage:"中文"==se()?"简体中文":se()},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:T.params.appUuid}),onmessage(e){var a,t,l,o,n,i,s,r,u,d;if(e.data.trim())try{const c=JSON.parse(e.data);if(D.value=c.task_id,c.error)throw new Error(c.error);"智能体推理思维链"==(null==(a=null==c?void 0:c.data)?void 0:a.title)&&"node_finished"===c.event&&(Fe.value=!0,Re.value=null==(o=JSON.parse(null==(l=null==(t=null==c?void 0:c.data)?void 0:t.outputs)?void 0:l.text))?void 0:o.text,Ge()),"node_started"!==c.event||We.value||"开始"==(null==(n=null==c?void 0:c.data)?void 0:n.title)||Oe.value.push({node_id:null==(i=null==c?void 0:c.data)?void 0:i.node_id,title:null==(s=null==c?void 0:c.data)?void 0:s.title,status:!1}),"error"===c.event&&(5047==c.code&&(He.value=!0),We.value=!0,ce.value=!1,Xe=!0,Be.value=!1,L.value=null==c?void 0:c.message),"node_finished"===c.event&&Oe.value.forEach((e=>{var a;e.node_id==(null==(a=null==c?void 0:c.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===c.event&&(O.value=!0,Ve.value.push(null==(r=null==c?void 0:c.data)?void 0:r.text),Fe.value||ea()),"workflow_started"===c.event&&(Ne.value=!1),"workflow_finished"===c.event&&(Xe=!0,We.value=!0,ce.value=!1,Be.value=!1,ze.value=!1,O.value||(Ve.value.push(null==(d=null==(u=null==c?void 0:c.data)?void 0:u.outputs)?void 0:d.text),Fe.value||ea()))}catch(c){sa(c)}},onerror(e){sa(e)},signal:Je.signal,openWhenHidden:!0})}catch(e){sa()}},Qe=async()=>{L.value="",Ve.value=[],Fe.value=!1,Xe=!1,Je=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,Ne.value=!0,Be.value=!0,Ue.value=!1,await me(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:he.value,user:N.value.userName,inputs:{...k,outputLanguage:se()},files:[],response_mode:"streaming",appUuid:T.params.appUuid,requestId:crypto.randomUUID()}),onmessage(e){if(Ne.value=!1,ze.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(D.value=a.task_id,a.error)throw new Error(a.error);"error"===a.event&&(5047==a.code&&(He.value=!0),Xe=!0,L.value=null==a?void 0:a.message,ze.value=!1,Be.value=!1),"message"===a.event&&(Ve.value.push(null==a?void 0:a.answer),Fe.value||ea()),"message_end"===a.event&&(Xe=!0,Be.value=!1,ze.value=!1)}catch(a){sa(a)}},onerror(e){sa(e)},signal:Je.signal,openWhenHidden:!0})}catch(e){sa()}},ea=()=>{if(0===Ve.value.length)return Fe.value=!1,Ze=!0,void oa();Fe.value=!0;const e=Ve.value.shift();na(e).then((()=>{ea()}))},oa=()=>{Ze&&Xe&&(Be.value=!1,ze.value=!1,Ue.value=!0)},na=e=>new Promise((a=>{let t=0;ge=setInterval((()=>{if(t<(null==e?void 0:e.length)){L.value+=e[t++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const l=document.getElementsByClassName("mobile_right");l[0].scrollTop=l[0].scrollHeight}else clearInterval(ge),a()}),0)})),sa=()=>{setTimeout((()=>{Je.abort()}),0),ce.value=!1,Ne.value=!1,ze.value=!1,Be.value=!1,Fe.value=!1,ne.error(z("tool.accessbusy")),L.value=z("tool.accessbusy")},ra=async()=>{try{await navigator.clipboard.writeText(L.value),ne({type:"success",message:z("tool.copysuccess")})}catch(e){ne(e)}},ua=()=>{for(let e in k)k[e]="";H.value.forEach((e=>{e.updateMessage()})),P.value.forEach((e=>{e.updateMessage()}))},da=async()=>{if(localStorage.getItem("yudaoToken"))$e();else try{await Q({userId:N.value.userId,userName:N.value.userName,realName:N.value.realName,avatar:N.value.avatar,plaintextUserId:N.value.plaintextUserId,mobile:N.value.mobile,email:N.value.email}).then((async e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),$e())}))}catch(e){}};return(e,a)=>{const t=S,l=I,o=re,n=ue,g=ee("v-md-preview"),b=de;return i(),s("div",Oa,[r("div",Ea,[d(Te)?(i(),s(p,{key:0},[r("div",Ra,[(i(!0),s(p,null,m(d($),((a,t)=>(i(),s("div",{class:"flex",key:t},[(i(!0),s(p,null,m(a,((a,t)=>(i(),c(ia,{key:a.variable,type:t,onPayShowStatus:je,label:e.$t(null==a?void 0:a.label),value:d(k)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,currentItem:w.currentItem,"onUpdate:value":e=>d(k)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:H},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","currentItem","onUpdate:value"])))),128))])))),128)),r("div",Ma,[f(t,{onClick:ua},{default:v((()=>[h(u(e.$t("tool.clear")),1)])),_:1}),f(t,{onClick:Ae,loading:d(Be),type:"primary"},{default:v((()=>[h(u(e.$t("tool.execute")),1)])),_:1},8,["loading"])])]),r("div",La,[r("div",Da,[d(Ee).length>0||d(Le)||d(ve)?(i(),s("div",Ha,[f(n,{modelValue:d(W),"onUpdate:modelValue":a[0]||(a[0]=e=>te(W)?W.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(ce)?(i(),s("div",Pa,[r("img",{src:d(x),alt:"loading"},null,8,Wa)])):(i(),s("div",Fa,[f(l,null,{default:v((()=>[f(d(ae))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(i(!0),s(p,null,m(d(Ee),((t,l)=>(i(),s("div",{key:l,class:"process"},[r("div",Ja,u(t.title),1),a[5]||(a[5]=h("    ")),r("span",{style:{color:"#36b15e"},class:E(t.status?"":"loading-text")},u(t.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),r("div",null,[d(Le)?(i(),s("div",Xa)):y("",!0)]),d(Le)?(i(),c(o,{key:0,name:"2"},{title:v((()=>[d(De)?(i(),s("div",Za,[r("img",{src:d(x),alt:"loading"},null,8,Ya)])):(i(),s("div",Ga,[f(l,null,{default:v((()=>[f(d(ae))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",Ka,[r("div",Qa,u(d(Le)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(L)&&!d(He)?(i(),c(g,{key:1,text:d(L),id:"previewMd"},null,8,["text"])):y("",!0),d(He)?(i(),s("div",et,[h(u(d(L))+" ",1),f(t,{type:"text",onClick:Ie},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0),r("div",null,[d(ze)?(i(),s("img",{key:0,src:d(x),alt:"loading",class:"spinner"},null,8,at)):y("",!0),d(ze)?(i(),s("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:be},u(e.$t("tool.stopGeneration")),1)):y("",!0),d(Ue)?(i(),s("img",{key:2,onClick:ra,src:d(_),alt:"",style:{width:"20px"},class:"copy"},null,8,tt)):y("",!0)])])])],64)):y("",!0)]),r("div",lt,[f(d(Va),{active:d(R),shrink:"","line-width":"20",onClickTab:ke},{default:v((()=>[f(d(Aa),{title:"输入",name:"a"},{default:v((()=>[(i(!0),s(p,null,m(d($),((a,t)=>(i(),s("div",{class:"flex",key:t},[(i(!0),s(p,null,m(a,((a,t)=>(i(),c(ia,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:d(k)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>d(k)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:P},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),r("div",ot,[f(t,{onClick:ua},{default:v((()=>a[6]||(a[6]=[h("Clear")]))),_:1}),f(t,{onClick:a[1]||(a[1]=e=>Ae()),loading:d(Be),type:"primary"},{default:v((()=>a[7]||(a[7]=[h("Execute")]))),_:1},8,["loading"])])])),_:1}),f(d(Aa),{title:"结果",name:"b",disabled:d(M)},{default:v((()=>[r("div",nt,[r("div",it,[d(Oe).length>0||d(Le)?(i(),s("div",st,[f(n,{modelValue:d(W),"onUpdate:modelValue":a[2]||(a[2]=e=>te(W)?W.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(ce)?(i(),s("div",rt,[r("img",{src:d(x),alt:"loading"},null,8,ut)])):(i(),s("div",dt,[f(l,null,{default:v((()=>[f(d(ae))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(i(!0),s(p,null,m(d(Oe),((t,l)=>(i(),s("div",{key:l,class:"process"},[r("div",ct,u(t.title),1),a[8]||(a[8]=h("    ")),r("span",{style:{color:"#36b15e"},class:E(t.status?"":"loading-text")},u(t.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),a[9]||(a[9]=r("div",null,[r("div",{class:"process"})],-1)),d(Le)?(i(),c(o,{key:0,title:"推导过程",name:"2"},{title:v((()=>[d(De)?(i(),s("div",vt,[r("img",{src:d(x),alt:"loading"},null,8,pt)])):(i(),s("div",mt,[f(l,null,{default:v((()=>[f(d(ae))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",ft,[r("div",gt,u(d(Le)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(L)&&!d(He)?(i(),c(g,{key:1,text:d(L),id:"previewMd"},null,8,["text"])):y("",!0),d(He)?(i(),s("div",ht,[h(u(d(L))+" ",1),f(t,{type:"text",onClick:Ie},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0)])])])),_:1},8,["disabled"])])),_:1},8,["active"])]),d(fe)?(i(),c(b,{key:0,modelValue:d(fe),"onUpdate:modelValue":a[3]||(a[3]=e=>te(fe)?fe.value=e:null),class:"payPC","show-close":!1},{default:v((()=>[f(aa,{userInfo:d(N),appTypes:e.appTypes,currentItem:w.currentItem,onToAgreement:e.toAgreement,onClose:we,onSubscribe:xe},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):y("",!0),d(fe)?(i(),c(d(la),{key:1,show:d(fe),"onUpdate:show":a[4]||(a[4]=e=>te(fe)?fe.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:v((()=>[f(ta,{userInfo:d(N),appTypes:e.appTypes,currentItem:w.currentItem,onToAgreement:e.toAgreement,onClose:we},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])):y("",!0)])}}},[["__scopeId","data-v-afa0c0f2"]]);export{yt as default};
