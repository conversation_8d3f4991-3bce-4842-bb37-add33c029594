/* empty css                  */import{g as e,i as a}from"./index-13fb7a4a.js";import{r as l,x as t,u as o,w as i,d as n,e as s,j as r,t as u,g as d,h as c,m as v,F as p,y as m,f,z as g,k as h,i as y,A as b,B as w,C as x,D as _,G as k,E as I,H as S,I as T,J as C,K as $,L as z,M as N,N as B,O as q,P as U,o as j,Q as A,R as V,S as O,T as R,q as E,U as M,V as L,W as H,X as D,a as P,b as W,Y as J,Z as F,$ as X,a0 as Z,a1 as Y,a2 as G,a3 as K,c as Q,a4 as ee,p as ae,a5 as le,a6 as te,a7 as oe,a8 as ie,a9 as ne,aa as se,ab as re,ac as ue,v as de}from"./index-ab8bb51a.js";import{r as ce,a as ve,f as pe}from"./use-route-a82b77e8.js";/* empty css                  *//* empty css                  *//* empty css                 */import{c as me,r as fe,g as ge,s as he,i as ye,o as be,a as we,n as xe,m as _e,b as ke,u as Ie,d as Se,e as Te,f as Ce,h as $e,j as ze,k as Ne,w as Be,l as qe,p as Ue,t as je,q as Ae,v as Ve,x as Oe,y as Re,z as Ee,A as Me,B as Le,C as He,D as De,E as Pe,F as We,G as Je,H as Fe,I as Xe,J as Ze,K as Ye,L as Ge,M as Ke,N as Qe,O as ea,P as aa,Q as la}from"./index-97e4f66f.js";/* empty css                   */const ta={class:"p-3 flex-1 rounded-md"},oa={class:"text-[14px] font-bold mb-2 text-gray-600"},ia={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]}},emits:["update:value"],setup(e,{expose:a,emit:T}){const C=l(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),$=o(),z=l([]),N=l({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),B=l(""),q=e,U=q.type,j=q.fileVerify,A=q.label,V=q.required,O=q.max_length,R=q.options;"file"==U&&(B.value=null),"file-list"==U&&(B.value=[]);const E={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},M=()=>{let e="";return j.forEach(((a,l)=>{l<j.length-1?e+=E[a].join(",")+",":e+=E[a].join(",")})),e},L=T,H=(e,a,l)=>{},D=()=>{B.value=""},P=async e=>{const{file:a,onSuccess:l,onError:t}=e,o=new FormData;o.append("file",a),o.append("appId",$.params.uuid),o.append("user",C.value.userName);try{const e=await b(o);"file-list"==U?B.value.push({type:N.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):B.value={type:N.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},l(e,a)}catch(i){t(i)}return!1};R&&R.length>0&&(B.value=R[0]);return a({updateMessage:()=>{R&&R.length>0?B.value=R[0]:"file"==U?(B.value=null,z.value=[]):"file-list"==U?(B.value=[],z.value=[]):B.value=""}}),i(B,(e=>{L("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const l=w,t=x,o=_,i=k,b=I,T=S;return n(),s("div",ta,[r("div",oa,u(d(A)),1),"paragraph"===d(U)||"text-input"===d(U)?(n(),c(l,{key:0,modelValue:B.value,"onUpdate:modelValue":a[0]||(a[0]=e=>B.value=e),type:"paragraph"===d(U)?"textarea":"text",rows:5,required:d(V),placeholder:`${d(A)}`,"show-word-limit":"",resize:"none",maxlength:d(O)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===d(U)?(n(),c(l,{key:1,modelValue:B.value,"onUpdate:modelValue":a[1]||(a[1]=e=>B.value=e),modelModifiers:{number:!0},type:"number",required:d(V),placeholder:`${d(A)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===d(U)?(n(),c(o,{key:2,modelValue:B.value,"onUpdate:modelValue":a[2]||(a[2]=e=>B.value=e),required:d(V),placeholder:`${d(A)}`},{default:v((()=>[(n(!0),s(p,null,m(d(R),(e=>(n(),c(t,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===d(U)||"file-list"===d(U)?(n(),c(T,{key:3,"file-list":z.value,"onUpdate:fileList":a[3]||(a[3]=e=>z.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":D,"before-remove":e.beforeRemove,limit:d(O),accept:M(),"auto-upload":!0,"on-Success":H,"http-request":P,"on-exceed":e.handleExceed},{default:v((()=>[f(b,{disabled:"file"===d(U)?1==z.value.length:z.value.length==d(O)},{default:v((()=>[f(i,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:v((()=>[f(d(g))])),_:1}),a[4]||(a[4]=h("从本地上传"))])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):y("",!0)])}}};let na=0;function sa(){const e=T(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++na}`}function ra(e,a){if(!ye||!window.IntersectionObserver)return;const l=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),t=()=>{e.value&&l.unobserve(e.value)};$(t),z(t),be((()=>{e.value&&l.observe(e.value)}))}const[ua,da]=we("sticky");const ca=Ue(N({name:ua,props:{zIndex:xe,position:_e("top"),container:Object,offsetTop:ke(0),offsetBottom:ke(0)},emits:["scroll","change"],setup(e,{emit:a,slots:t}){const o=l(),n=Ie(o),s=B({fixed:!1,width:0,height:0,transform:0}),r=l(!1),u=q((()=>Se("top"===e.position?e.offsetTop:e.offsetBottom))),d=q((()=>{if(r.value)return;const{fixed:e,height:a,width:l}=s;return e?{width:`${l}px`,height:`${a}px`}:void 0})),c=q((()=>{if(!s.fixed||r.value)return;const a=Te(Ce(e.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[e.position]:`${u.value}px`});return s.transform&&(a.transform=`translate3d(0, ${s.transform}px, 0)`),a})),v=()=>{if(!o.value||ze(o))return;const{container:l,position:t}=e,i=Ne(o),n=ge(window);if(s.width=i.width,s.height=i.height,"top"===t)if(l){const e=Ne(l),a=e.bottom-u.value-s.height;s.fixed=u.value>i.top&&e.bottom>0,s.transform=a<0?a:0}else s.fixed=u.value>i.top;else{const{clientHeight:e}=document.documentElement;if(l){const a=Ne(l),t=e-a.top-u.value-s.height;s.fixed=e-u.value<i.bottom&&e>a.top,s.transform=t<0?-t:0}else s.fixed=e-u.value<i.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:s.fixed})})(n)};return i((()=>s.fixed),(e=>a("change",e))),$e("scroll",v,{target:n,passive:!0}),ra(o,v),i([Be,qe],(()=>{o.value&&!ze(o)&&s.fixed&&(r.value=!0,U((()=>{const e=Ne(o);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return f("div",{ref:o,style:d.value},[f("div",{class:da({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=t.default)?void 0:e.call(t)])])}}})),[va,pa]=we("swipe"),ma={loop:je,width:xe,height:xe,vertical:Boolean,autoplay:ke(0),duration:ke(500),touchable:je,lazyRender:Boolean,initialSwipe:ke(0),indicatorColor:String,showIndicators:je,stopPropagation:je},fa=Symbol(va);const ga=Ue(N({name:va,props:ma,emits:["change","dragStart","dragEnd"],setup(e,{emit:a,slots:t}){const o=l(),n=l(),s=B({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=Ae(),{children:d,linkChildren:c}=Ve(fa),v=q((()=>d.length)),p=q((()=>s[e.vertical?"height":"width"])),m=q((()=>e.vertical?u.deltaY.value:u.deltaX.value)),g=q((()=>{if(s.rect){return(e.vertical?s.rect.height:s.rect.width)-p.value*v.value}return 0})),h=q((()=>p.value?Math.ceil(Math.abs(g.value)/p.value):v.value)),y=q((()=>v.value*p.value)),b=q((()=>(s.active+v.value)%v.value)),w=q((()=>{const a=e.vertical?"vertical":"horizontal";return u.direction.value===a})),x=q((()=>{const a={transitionDuration:`${s.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(p.value){const l=e.vertical?"height":"width",t=e.vertical?"width":"height";a[l]=`${y.value}px`,a[t]=e[t]?`${e[t]}px`:""}return a})),_=(a,l=0)=>{let t=a*p.value;e.loop||(t=Math.min(t,-g.value));let o=l-t;return e.loop||(o=He(o,g.value,0)),o},k=({pace:l=0,offset:t=0,emitChange:o})=>{if(v.value<=1)return;const{active:i}=s,n=(a=>{const{active:l}=s;return a?e.loop?He(l+a,-1,v.value):He(l+a,0,h.value):l})(l),r=_(n,t);if(e.loop){if(d[0]&&r!==g.value){const e=r<g.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=n,s.offset=r,o&&n!==i&&a("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Me((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let T;const C=()=>clearTimeout(T),N=()=>{C(),+e.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),N()}),+e.autoplay))},V=(a=+e.initialSwipe)=>{if(!o.value)return;const l=()=>{var l,t;if(!ze(o)){const a={width:o.value.offsetWidth,height:o.value.offsetHeight};s.rect=a,s.width=+(null!=(l=e.width)?l:a.width),s.height=+(null!=(t=e.height)?t:a.height)}v.value&&-1===(a=Math.min(v.value-1,a))&&(a=v.value-1),s.active=a,s.swiping=!0,s.offset=_(a),d.forEach((e=>{e.setOffset(0)})),N()};ze(o)?U().then(l):l()},O=()=>V(s.active);let R;const E=a=>{!e.touchable||a.touches.length>1||(u.start(a),r=!1,R=Date.now(),C(),I())},M=()=>{if(!e.touchable||!s.swiping)return;const l=Date.now()-R,t=m.value/l;if((Math.abs(t)>.25||Math.abs(m.value)>p.value/2)&&w.value){const a=e.vertical?u.offsetY.value:u.offsetX.value;let l=0;l=e.loop?a>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/p.value),k({pace:l,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,a("dragEnd",{index:b.value}),N()},L=(a,l)=>{const t=l===b.value,o=t?{backgroundColor:e.indicatorColor}:void 0;return f("i",{style:o,class:pa("indicator",{active:t})},null)};return Oe({prev:()=>{I(),u.reset(),Me((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:O,swipeTo:(a,l={})=>{I(),u.reset(),Me((()=>{let t;t=e.loop&&a===v.value?0===s.active?0:a:a%v.value,l.immediate?Me((()=>{s.swiping=!1})):s.swiping=!1,k({pace:t-s.active,emitChange:!0})}))}}),c({size:p,props:e,count:v,activeIndicator:b}),i((()=>e.initialSwipe),(e=>V(+e))),i(v,(()=>V(s.active))),i((()=>e.autoplay),N),i([Be,qe,()=>e.width,()=>e.height],O),i(Re(),(e=>{"visible"===e?N():C()})),j(V),A((()=>V(s.active))),Ee((()=>V(s.active))),$(C),z(C),$e("touchmove",(l=>{if(e.touchable&&s.swiping&&(u.move(l),w.value)){!e.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(Le(l,e.stopPropagation),k({offset:m.value}),r||(a("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var a;return f("div",{ref:o,class:pa()},[f("div",{ref:n,style:x.value,class:pa("track",{vertical:e.vertical}),onTouchstartPassive:E,onTouchend:M,onTouchcancel:M},[null==(a=t.default)?void 0:a.call(t)]),t.indicator?t.indicator({active:b.value,total:v.value}):e.showIndicators&&v.value>1?f("div",{class:pa("indicators",{vertical:e.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ha,ya]=we("tabs");var ba=N({name:ha,props:{count:De(Number),inited:Boolean,animated:Boolean,duration:De(xe),swipeable:Boolean,lazyRender:Boolean,currentIndex:De(Number)},emits:["change"],setup(e,{emit:a,slots:t}){const o=l(),n=e=>a("change",e),s=()=>{var a;const l=null==(a=t.default)?void 0:a.call(t);return e.animated||e.swipeable?f(ga,{ref:o,loop:!1,class:ya("track"),duration:1e3*+e.duration,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:n},{default:()=>[l]}):l},r=a=>{const l=o.value;l&&l.state.active!==a&&l.swipeTo(a,{immediate:!e.inited})};return i((()=>e.currentIndex),r),j((()=>{r(e.currentIndex)})),Oe({swipeRef:o}),()=>f("div",{class:ya("content",{animated:e.animated||e.swipeable})},[s()])}});const[wa,xa]=we("tabs"),_a={type:_e("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ke(0),duration:ke(.3),animated:Boolean,ellipsis:je,swipeable:Boolean,scrollspy:Boolean,offsetTop:ke(0),background:String,lazyRender:je,showHeader:je,lineWidth:xe,lineHeight:xe,beforeChange:Function,swipeThreshold:ke(5),titleActiveColor:String,titleInactiveColor:String},ka=Symbol(wa);var Ia=N({name:wa,props:_a,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:a,slots:t}){let o,n,s,r,u;const d=l(),c=l(),v=l(),p=l(),m=sa(),g=Ie(d),[h,y]=function(){const e=l([]),a=[];return C((()=>{e.value=[]})),[e,l=>(a[l]||(a[l]=a=>{e.value[l]=a}),a[l])]}(),{children:b,linkChildren:w}=Ve(ka),x=B({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=q((()=>b.length>+e.swipeThreshold||!e.ellipsis||e.shrink)),k=q((()=>({borderColor:e.color,background:e.background}))),I=(e,a)=>{var l;return null!=(l=e.name)?l:a},S=q((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),T=q((()=>Se(e.offsetTop))),$=q((()=>e.sticky?T.value+o:0)),z=a=>{const l=c.value,t=h.value;if(!(_.value&&l&&t&&t[x.currentIndex]))return;const o=t[x.currentIndex].$el,i=o.offsetLeft-(l.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,l){let t,o=0;const i=e.scrollLeft,n=0===l?1:Math.round(1e3*l/16);let s=i;return function l(){s+=(a-i)/n,e.scrollLeft=s,++o<n&&(t=fe(l))}(),function(){me(t)}}(l,i,a?0:+e.duration)},N=()=>{const a=x.inited;U((()=>{const l=h.value;if(!l||!l[x.currentIndex]||"line"!==e.type||ze(d.value))return;const t=l[x.currentIndex].$el,{lineWidth:o,lineHeight:i}=e,n=t.offsetLeft+t.offsetWidth/2,s={width:Pe(o),backgroundColor:e.color,transform:`translateX(${n}px) translateX(-50%)`};if(a&&(s.transitionDuration=`${e.duration}s`),We(i)){const e=Pe(i);s.height=e,s.borderRadius=e}x.lineStyle=s}))},j=(l,t)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(l);if(!We(o))return;const i=b[o],n=I(i,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,t||z(),N()),n!==e.active&&(a("update:active",n),r&&a("change",n,i.title)),s&&!e.scrollspy&&Fe(Math.ceil(Xe(d.value)-T.value))},V=(e,a)=>{const l=b.find(((a,l)=>I(a,l)===e)),t=l?b.indexOf(l):0;j(t,a)},O=(a=!1)=>{if(e.scrollspy){const l=b[x.currentIndex].$el;if(l&&g.value){const t=Xe(l,g.value)-$.value;n=!0,u&&u(),u=function(e,a,l,t){let o,i=ge(e);const n=i<a,s=0===l?1:Math.round(1e3*l/16),r=(a-i)/s;return function l(){i+=r,(n&&i>a||!n&&i<a)&&(i=a),he(e,i),n&&i<a||!n&&i>a?o=fe(l):t&&(o=fe(t))}(),function(){me(o)}}(g.value,t,a?0:+e.duration,(()=>{n=!1}))}}},R=(l,t,o)=>{const{title:i,disabled:n}=b[t],s=I(b[t],t);n||(Ze(e.beforeChange,{args:[s],done:()=>{j(t),O()}}),ce(l)),a("clickTab",{name:s,title:i,event:o,disabled:n})},E=e=>{s=e.isFixed,a("scroll",e)},M=()=>{if("line"===e.type&&b.length)return f("div",{class:xa("line"),style:x.lineStyle},null)},L=()=>{var a,l,o;const{type:i,border:n,sticky:s}=e,r=[f("div",{ref:s?void 0:v,class:[xa("wrap"),{[Je]:"line"===i&&n}]},[f("div",{ref:c,role:"tablist",class:xa("nav",[i,{shrink:e.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(a=t["nav-left"])?void 0:a.call(t),b.map((e=>e.renderTitle(R))),M(),null==(l=t["nav-right"])?void 0:l.call(t)])]),null==(o=t["nav-bottom"])?void 0:o.call(t)];return s?f("div",{ref:v},[r]):r},H=()=>{N(),U((()=>{var e,a;z(!0),null==(a=null==(e=p.value)?void 0:e.swipeRef.value)||a.resize()}))};i((()=>[e.color,e.duration,e.lineWidth,e.lineHeight]),N),i(Be,H),i((()=>e.active),(e=>{e!==S.value&&V(e)})),i((()=>b.length),(()=>{x.inited&&(V(e.active),N(),U((()=>{z(!0)})))}));return Oe({resize:H,scrollTo:e=>{U((()=>{V(e),O(!0)}))}}),A(N),Ee(N),be((()=>{V(e.active,!0),U((()=>{x.inited=!0,v.value&&(o=Ne(v.value).height),z(!0)}))})),ra(d,N),$e("scroll",(()=>{if(e.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=Ne(b[e].$el);if(a>$.value)return 0===e?0:e-1}return b.length-1})();j(e)}}),{target:g,passive:!0}),w({id:m,props:e,setLine:N,scrollable:_,onRendered:(e,l)=>a("rendered",e,l),currentName:S,setTitleRefs:y,scrollIntoView:z}),()=>f("div",{ref:d,class:xa([e.type])},[e.showHeader?e.sticky?f(ca,{container:d.value,offsetTop:T.value,onScroll:E},{default:()=>[L()]}):L():null,f(ba,{ref:p,count:b.length,inited:x.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:x.currentIndex,onChange:j},{default:()=>{var e;return[null==(e=t.default)?void 0:e.call(t)]}})])}});const Sa=Symbol(),[Ta,Ca]=we("tab"),$a=N({name:Ta,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:xe,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:je},setup(e,{slots:a}){const l=q((()=>{const a={},{type:l,color:t,disabled:o,isActive:i,activeColor:n,inactiveColor:s}=e;t&&"card"===l&&(a.borderColor=t,o||(i?a.backgroundColor=t:a.color=t));const r=i?n:s;return r&&(a.color=r),a})),t=()=>{const l=f("span",{class:Ca("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||We(e.badge)&&""!==e.badge?f(Ye,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[l]}):l};return()=>f("div",{id:e.id,role:"tab",class:[Ca([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:l.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[t()])}}),[za,Na]=we("swipe-item");const Ba=Ue(N({name:za,setup(e,{slots:a}){let l;const t=B({offset:0,inited:!1,mounted:!1}),{parent:o,index:i}=Ge(fa);if(!o)return;const n=q((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),t.offset&&(e.transform=`translate${a?"Y":"X"}(${t.offset}px)`),e})),s=q((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||l)return!0;if(!t.mounted)return!1;const n=o.activeIndicator.value,s=o.count.value-1,r=0===n&&e?s:n-1,u=n===s&&e?0:n+1;return l=i.value===n||i.value===r||i.value===u,l}));return j((()=>{U((()=>{t.mounted=!0}))})),Oe({setOffset:e=>{t.offset=e}}),()=>{var e;return f("div",{class:Na(),style:n.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[qa,Ua]=we("tab");const ja=Ue(N({name:qa,props:Te({},ve,{dot:Boolean,name:xe,badge:xe,title:String,disabled:Boolean,titleClass:Ke,titleStyle:[String,Object],showZeroBadge:je}),setup(e,{slots:a}){const t=sa(),o=l(!1),n=T(),{parent:s,index:r}=Ge(ka);if(!s)return;const u=()=>{var a;return null!=(a=e.name)?a:r.value},d=q((()=>{const a=u()===s.currentName.value;return a&&!o.value&&(o.value=!0,s.props.lazyRender&&U((()=>{s.onRendered(u(),e.title)}))),a})),c=l(""),v=l("");V((()=>{const{titleClass:a,titleStyle:l}=e;c.value=a?O(a):"",v.value=l&&"string"!=typeof l?R(E(l)):l}));const p=l(!d.value);return i(d,(e=>{e?p.value=!1:Me((()=>{p.value=!0}))})),i((()=>e.title),(()=>{s.setLine(),s.scrollIntoView()})),M(Sa,d),Oe({id:t,renderTitle:l=>f($a,D({key:t,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:t,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:e=>l(n.proxy,r.value,e)},Qe(s.props,["type","color","shrink"]),Qe(e,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const l=`${s.id}-${r.value}`,{animated:i,swipeable:n,scrollspy:u,lazyRender:c}=s.props;if(!a.default&&!i)return;const v=u||d.value;if(i||n)return f(Ba,{id:t,role:"tabpanel",class:Ua("panel-wrapper",{inactive:p.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":l,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[f("div",{class:Ua("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=o.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return L(f("div",{id:t,role:"tabpanel",class:Ua("panel"),tabindex:v?0:-1,"aria-labelledby":l,"data-allow-mismatch":"attribute"},[m]),[[H,v]])}}})),Aa=Ue(Ia),Va={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Oa={class:"pc_container",style:{display:"flex"}},Ra={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ea={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Ma={class:"pc_right bg-[#fff]"},La={id:"typing-area"},Ha={key:0,class:"decContaniner nop bg-[#fff]"},Da={key:0,class:"img_box"},Pa=["src"],Wa={key:1,class:"icon"},Ja={class:"process_text label_width"},Fa={key:0,class:"process"},Xa={key:0,class:"img_box"},Za=["src"],Ya={key:1,class:"icon"},Ga={class:"process"},Ka={class:"process_text"},Qa={key:2},el=["src"],al=["src"],ll={class:"mobile_container"},tl={class:"p-3",style:{display:"flex","justify-content":"space-between"}},ol={class:"mobile_right"},il={id:"typing-area"},nl={key:0,class:"decContaniner nop bg-[#fff]"},sl={key:0,class:"img_box"},rl=["src"],ul={key:1,class:"icon"},dl={class:"process_text label_width"},cl={key:0,class:"img_box"},vl=["src"],pl={key:1,class:"icon"},ml={class:"process"},fl={class:"process_text"},gl={key:2},hl=P({__name:"index",props:{currentItem:{type:Object,default:()=>{}}},setup(g){const b=g,w=e("loading.png"),x=e("copy.png"),_=B({}),S=o(),T={},C=l([]),$=l(t.get("userInfo")?JSON.parse(t.get("userInfo")):{}),z=l(null),{t:N,locale:U}=W(),A=J(),V=l(!1);let R=l("a"),E=l(!0),M=l("");const L=l(""),H=l(null),D=l(null),P=l(["1","2"]),ce=l(!1),ve=l(!1),me=l(!1);let fe;const ge=l(""),he=l("");F({title:he,meta:[]});const ye=async()=>{var e;await le({appId:ge.value,user:$.value.userName,mode:null==(e=z.value)?void 0:e.mode,task_id:L.value}),setTimeout((()=>{Pe.abort(),We=!0,Ue.value=[],ce.value=!1,je.value.length&&je.value.forEach((e=>{e.status=!0})),Ge()}),0)},be=()=>{me.value=!1},we=async(e,a)=>{var l;let t=te();if(null==(l=$.value)?void 0:l.userId){const l={appUuid:a,priceId:e.priceId,monthNum:e.monthNum};let t=await oe(l);t&&(ie({type:"success",message:N("tool.sS")}),setTimeout((()=>{location.href=t}),1e3))}else t&&"zh-CN"!=t?A.push((isUp,"/login")):window.addLoginDom()};j((async()=>{var e,l;const o=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${o}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=S.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(l=S.params)?void 0:l.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),U.value=X(),Be();const i=await Z(S.params.appUuid);ge.value=i.dAppUuid,he.value=i.appName,t.get("userInfo")&&await sa(),ke(),a(i.customCss,i.customJs)}));const xe=e=>{R.value=e.name},_e=async()=>{me.value=!0},ke=()=>{ge.value&&Y({appId:ge.value,user:$.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(C.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],l=e[a].variable;T[l]={label:e[a].label},_[l]=""})))}))},Ie=q((()=>!!C.value.length)),Se=q((()=>C.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),Te=()=>{ge.value&&ne({appId:ge.value,user:$.value.userName}).then((e=>{z.value={...e}}))},Ce=l(!1),$e=l(!1),ze=l(!1),Ne=l(!1),Be=async()=>{var e,a;let l=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=$.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:ge.value,userUuid:null==(a=$.value)?void 0:a.openid}];await G.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",l)},qe=async()=>{var e,a,l,o;if(t.get("userInfo")){if(await sa(),!(null==(a=null==(e=b.currentItem)?void 0:e.appUser)?void 0:a.status))return me.value=!0,!1;if(0!=Se.value.length||(i=_,Object.values(i).some((e=>e)))){var i;for(let e in _)if(Se.value.includes(e)&&!_[e])return void ie({message:`${T[e].label}${N("tool.requiredfield")}`,type:"error"});(null==(l=z.value)?void 0:l.mode)&&(["advanced-chat","chat"].includes(null==(o=z.value)?void 0:o.mode)?ie({type:"success",message:N("tool.planning")}):"completion"==z.value.mode?(E.value=!1,setTimeout((()=>{R.value="b"}),1e3),Ye()):(E.value=!1,setTimeout((()=>{R.value="b"}),1e3),Ze()))}else ie({message:`${N("tool.enterquestion")}`,type:"error"})}else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=te();e&&"zh-CN"!=e?A.push("/login"):window.addLoginDom()}},Ue=l([]);var je=l([]),Ae=l([]);const Ve=l(""),Oe=l(0),Re=l(""),Ee=l(!1),Me=l(!1);let Le=l(0);const He=l(!1),De=l(!1);let Pe,We=!1,Je=!1;i(je,(()=>{Fe()}),{deep:!0});const Fe=()=>{Le.value<je.value.length&&(Ae.value.push(je.value[Le.value]),Le.value++,setTimeout(Fe,1e3))},Xe=()=>{Oe.value<Ve.value.length?(Ee.value=!0,Re.value+=Ve.value.charAt(Oe.value),Oe.value++,setTimeout(Xe,20)):(De.value=!1,Ee.value=!1,ze.value=!0,Ge())},Ze=async()=>{ve.value=!0,ce.value=!0,M.value="",je.value=[],Ae.value=[],Le.value=0,Re.value="",Ve.value="",Ue.value=[],He.value=!1,We=!1,Oe.value=0,Pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,Ce.value=!0,$e.value=!0,Ne.value=!1,await pe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:ge.value,user:$.value.userName,inputs:{..._,outputLanguage:_.outputLanguage?_.outputLanguage:"中文"==se()?"简体中文":se()},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:S.params.appUuid}),onmessage(e){var a,l,t,o,i,n,s,r,u,d;if(e.data.trim())try{const c=JSON.parse(e.data);if(L.value=c.task_id,c.error)throw new Error(c.error);"智能体推理思维链"==(null==(a=null==c?void 0:c.data)?void 0:a.title)&&"node_finished"===c.event&&(De.value=!0,Ve.value=null==(o=JSON.parse(null==(t=null==(l=null==c?void 0:c.data)?void 0:l.outputs)?void 0:t.text))?void 0:o.text,Xe()),"node_started"!==c.event||He.value||"开始"==(null==(i=null==c?void 0:c.data)?void 0:i.title)||je.value.push({node_id:null==(n=null==c?void 0:c.data)?void 0:n.node_id,title:null==(s=null==c?void 0:c.data)?void 0:s.title,status:!1}),"error"===c.event&&(5047==c.code&&(Me.value=!0),He.value=!0,ce.value=!1,We=!0,$e.value=!1,M.value=null==c?void 0:c.message),"node_finished"===c.event&&je.value.forEach((e=>{var a;e.node_id==(null==(a=null==c?void 0:c.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===c.event&&(V.value=!0,Ue.value.push(null==(r=null==c?void 0:c.data)?void 0:r.text),De.value||Ge()),"workflow_started"===c.event&&(Ce.value=!1),"workflow_finished"===c.event&&(We=!0,He.value=!0,ce.value=!1,$e.value=!1,ze.value=!1,V.value||(Ue.value.push(null==(d=null==(u=null==c?void 0:c.data)?void 0:u.outputs)?void 0:d.text),De.value||Ge()))}catch(c){ta(c)}},onerror(e){ta(e)},signal:Pe.signal,openWhenHidden:!0})}catch(e){ta()}},Ye=async()=>{M.value="",Ue.value=[],De.value=!1,We=!1,Pe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,Ce.value=!0,$e.value=!0,Ne.value=!1,await pe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:ge.value,user:$.value.userName,inputs:{..._,outputLanguage:se()},files:[],response_mode:"streaming",appUuid:S.params.appUuid,requestId:crypto.randomUUID()}),onmessage(e){if(Ce.value=!1,ze.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(L.value=a.task_id,a.error)throw new Error(a.error);"error"===a.event&&(5047==a.code&&(Me.value=!0),We=!0,M.value=null==a?void 0:a.message,ze.value=!1,$e.value=!1),"message"===a.event&&(Ue.value.push(null==a?void 0:a.answer),De.value||Ge()),"message_end"===a.event&&(We=!0,$e.value=!1,ze.value=!1)}catch(a){ta(a)}},onerror(e){ta(e)},signal:Pe.signal,openWhenHidden:!0})}catch(e){ta()}},Ge=()=>{if(0===Ue.value.length)return De.value=!1,Je=!0,void Ke();De.value=!0;const e=Ue.value.shift();Qe(e).then((()=>{Ge()}))},Ke=()=>{Je&&We&&($e.value=!1,ze.value=!1,Ne.value=!0)},Qe=e=>new Promise((a=>{let l=0;fe=setInterval((()=>{if(l<(null==e?void 0:e.length)){M.value+=e[l++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const t=document.getElementsByClassName("mobile_right");t[0].scrollTop=t[0].scrollHeight}else clearInterval(fe),a()}),0)})),ta=()=>{setTimeout((()=>{Pe.abort()}),0),ce.value=!1,Ce.value=!1,ze.value=!1,$e.value=!1,De.value=!1,ie.error(N("tool.accessbusy")),M.value=N("tool.accessbusy")},oa=async()=>{try{await navigator.clipboard.writeText(M.value),ie({type:"success",message:N("tool.copysuccess")})}catch(e){ie(e)}},na=()=>{for(let e in _)_[e]="";H.value.forEach((e=>{e.updateMessage()})),D.value.forEach((e=>{e.updateMessage()}))},sa=async()=>{if(localStorage.getItem("yudaoToken"))Te();else try{await K({userId:$.value.userId,userName:$.value.userName,realName:$.value.realName,avatar:$.value.avatar,plaintextUserId:$.value.plaintextUserId,mobile:$.value.mobile,email:$.value.email}).then((async e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),Te())}))}catch(e){}};return(e,a)=>{const l=I,t=k,o=re,i=ue,g=Q("v-md-preview"),S=de;return n(),s("div",Va,[r("div",Oa,[d(Ie)?(n(),s(p,{key:0},[r("div",Ra,[(n(!0),s(p,null,m(d(C),((a,l)=>(n(),s("div",{class:"flex",key:l},[(n(!0),s(p,null,m(a,((a,l)=>(n(),c(ia,{key:a.variable,type:l,label:e.$t(null==a?void 0:a.label),value:d(_)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>d(_)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:H},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),r("div",Ea,[f(l,{onClick:na},{default:v((()=>[h(u(e.$t("tool.clear")),1)])),_:1}),f(l,{onClick:qe,loading:d($e),type:"primary"},{default:v((()=>[h(u(e.$t("tool.execute")),1)])),_:1},8,["loading"])])]),r("div",Ma,[r("div",La,[d(Ae).length>0||d(Re)||d(ve)?(n(),s("div",Ha,[f(i,{modelValue:d(P),"onUpdate:modelValue":a[0]||(a[0]=e=>ae(P)?P.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(ce)?(n(),s("div",Da,[r("img",{src:d(w),alt:"loading"},null,8,Pa)])):(n(),s("div",Wa,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(n(!0),s(p,null,m(d(Ae),((l,t)=>(n(),s("div",{key:t,class:"process"},[r("div",Ja,u(l.title),1),a[5]||(a[5]=h("    ")),r("span",{style:{color:"#36b15e"},class:O(l.status?"":"loading-text")},u(l.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),r("div",null,[d(Re)?(n(),s("div",Fa)):y("",!0)]),d(Re)?(n(),c(o,{key:0,name:"2"},{title:v((()=>[d(Ee)?(n(),s("div",Xa,[r("img",{src:d(w),alt:"loading"},null,8,Za)])):(n(),s("div",Ya,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",Ga,[r("div",Ka,u(d(Re)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(M)&&!d(Me)?(n(),c(g,{key:1,text:d(M),id:"previewMd"},null,8,["text"])):y("",!0),d(Me)?(n(),s("div",Qa,[h(u(d(M))+" ",1),f(l,{type:"text",onClick:_e},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0),r("div",null,[d(ze)?(n(),s("img",{key:0,src:d(w),alt:"loading",class:"spinner"},null,8,el)):y("",!0),d(ze)?(n(),s("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:ye},u(e.$t("tool.stopGeneration")),1)):y("",!0),d(Ne)?(n(),s("img",{key:2,onClick:oa,src:d(x),alt:"",style:{width:"20px"},class:"copy"},null,8,al)):y("",!0)])])])],64)):y("",!0)]),r("div",ll,[f(d(Aa),{active:d(R),shrink:"","line-width":"20",onClickTab:xe},{default:v((()=>[f(d(ja),{title:"输入",name:"a"},{default:v((()=>[(n(!0),s(p,null,m(d(C),((a,l)=>(n(),s("div",{class:"flex",key:l},[(n(!0),s(p,null,m(a,((a,l)=>(n(),c(ia,{key:a.variable,type:l,label:e.$t(null==a?void 0:a.label),value:d(_)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>d(_)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:D},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),r("div",tl,[f(l,{onClick:na},{default:v((()=>a[6]||(a[6]=[h("Clear")]))),_:1}),f(l,{onClick:a[1]||(a[1]=e=>qe()),loading:d($e),type:"primary"},{default:v((()=>a[7]||(a[7]=[h("Execute")]))),_:1},8,["loading"])])])),_:1}),f(d(ja),{title:"结果",name:"b",disabled:d(E)},{default:v((()=>[r("div",ol,[r("div",il,[d(je).length>0||d(Re)?(n(),s("div",nl,[f(i,{modelValue:d(P),"onUpdate:modelValue":a[2]||(a[2]=e=>ae(P)?P.value=e:null)},{default:v((()=>[f(o,{name:"1"},{title:v((()=>[d(ce)?(n(),s("div",sl,[r("img",{src:d(w),alt:"loading"},null,8,rl)])):(n(),s("div",ul,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.execution_progress")),1)])),default:v((()=>[(n(!0),s(p,null,m(d(je),((l,t)=>(n(),s("div",{key:t,class:"process"},[r("div",dl,u(l.title),1),a[8]||(a[8]=h("    ")),r("span",{style:{color:"#36b15e"},class:O(l.status?"":"loading-text")},u(l.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),a[9]||(a[9]=r("div",null,[r("div",{class:"process"})],-1)),d(Re)?(n(),c(o,{key:0,title:"推导过程",name:"2"},{title:v((()=>[d(Ee)?(n(),s("div",cl,[r("img",{src:d(w),alt:"loading"},null,8,vl)])):(n(),s("div",pl,[f(t,null,{default:v((()=>[f(d(ee))])),_:1})])),h(" "+u(e.$t("tool.reasoning_process")),1)])),default:v((()=>[r("div",ml,[r("div",fl,u(d(Re)),1)])])),_:1})):y("",!0)])),_:1},8,["modelValue"])])):y("",!0),d(M)&&!d(Me)?(n(),c(g,{key:1,text:d(M),id:"previewMd"},null,8,["text"])):y("",!0),d(Me)?(n(),s("div",gl,[h(u(d(M))+" ",1),f(l,{type:"text",onClick:_e},{default:v((()=>[h(u(e.$t("market.subscribe")),1)])),_:1})])):y("",!0)])])])),_:1},8,["disabled"])])),_:1},8,["active"])]),d(me)?(n(),c(S,{key:0,modelValue:d(me),"onUpdate:modelValue":a[3]||(a[3]=e=>ae(me)?me.value=e:null),class:"payPC","show-close":!1},{default:v((()=>[f(ea,{userInfo:d($),appTypes:e.appTypes,currentItem:b.currentItem,onToAgreement:e.toAgreement,onClose:be,onSubscribe:we},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):y("",!0),d(me)?(n(),c(d(la),{key:1,show:d(me),"onUpdate:show":a[4]||(a[4]=e=>ae(me)?me.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:v((()=>[f(aa,{userInfo:d($),appTypes:e.appTypes,currentItem:b.currentItem,onToAgreement:e.toAgreement,onClose:be},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])):y("",!0)])}}},[["__scopeId","data-v-19f2d35a"]]);export{hl as default};
