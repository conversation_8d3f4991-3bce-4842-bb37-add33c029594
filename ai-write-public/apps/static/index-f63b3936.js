import{_ as e,a as l}from"./Editor-8c9acb45.js";/* empty css                  *//* empty css                 */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                    *//* empty css                  */import{a}from"./index-5f8dfc52.js";import{s as t}from"./index-dc0a3c98.js";/* empty css                   */import{a as s,r as o,N as n,o as r,d as i,e as d,j as u,F as p,y as m,S as c,g as v,i as f,t as b,f as x,p as g,m as y,k as h,h as k,a8 as V,B as j,E as w,ak as S,aN as _,aO as C,aP as N}from"./index-4aed3b59.js";/* empty css                  */const T={class:"h-full flex overflow-auto"},M={class:"flex flex-col w-[160px] mr-4"},U=["onClick"],z={key:0,class:"w-[4px] h-full"},H={class:"flex-1"},$={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},L={class:"flex items-center my-8 overflow-hidden"},A={class:"flex items-center mr-4"},E={class:"flex items-center mr-2"},I={class:"flex items-center"},P={class:"relative flex-1"},R={class:"text-[#419eff] absolute font-bold -top-1.5 left-[30%]"},Y={class:"mr-2"},B=["innerHTML","onClick"],D=["innerHTML"],F=s({__name:"index",setup(s){const F=o("类风湿关节炎铁死亡特征基因图的构建"),G=o(""),K=o(1),O=o(!0),W=o([]),q=o([1990,2023]),J=o({}),Q=n({pageNo:1,pageSize:20}),X=o([{label:"Title",value:"title"},{label:"Keywords",value:"keyword"},{label:"Abstract",value:"abstract"},{label:"Introduction",value:"introduction"},{label:"Methods",value:"methods"},{label:"Results",value:"results"},{label:"Discussion",value:"discussion"},{label:"Acknowledge",value:"acknowleg"},{label:"全库检索(CNS)",value:"all"}]),Z=o("title"),ee=e=>{if(!F.value)return V.warning("请输入关键词或短句");1==e&&(Q.pageNo=1);let l=W.value.map((e=>({field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]})));a(Z.value,{key:F.value,page:Q.pageNo-1,size:Q.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l,beginYear:q.value[0],endYear:q.value[1],mySentenceRange:0,sorts:[]}).then((e=>{e&&e.data&&(J.value=e.data,J.value.content=t(J.value.content))}))},le=()=>{K.value++},ae=()=>{ee()};return r((()=>{ee()})),(a,t)=>{const s=j,o=w,n=e,r=S,V=_,te=C,se=N,oe=l;return i(),d("div",T,[u("div",M,[(i(!0),d(p,null,m(v(X),((e,l)=>(i(),d("div",{class:c(["nav-item flex items-center font-bold text-[#2d3858] cursor-pointer border border-solid border-top-0 border-left-0 border-right-0 border-gray-200 h-[46px]",v(Z)==e.value?"bg-[#7e90b8]":"bg-[#f8f8f8]"]),key:l,onClick:l=>(e=>{Z.value=e.value,F.value="",J.value={},W.value=[]})(e)},[v(Z)!=e.value?(i(),d("div",z)):f("",!0),u("div",{class:c(["pl-10 h-full flex items-center",v(Z)==e.value?"border border-solid border-left-4 border-[#2b3858] border-top-0 border-bottom-0 border-right-0":""])},b(e.label),3)],10,U)))),128))]),u("div",H,[u("div",$,[x(s,{class:"h-full !text-[24px]",modelValue:v(F),"onUpdate:modelValue":t[0]||(t[0]=e=>g(F)?F.value=e:null),placeholder:"这里输入关键词或短句，中英文均可",clearable:""},null,8,["modelValue"]),x(o,{type:"primary",onClick:t[1]||(t[1]=e=>ee(1))},{default:y((()=>t[8]||(t[8]=[h("查 询")]))),_:1})]),(i(),k(n,{class:"h-[380px] mb-4",modelValue:v(G),"onUpdate:modelValue":t[2]||(t[2]=e=>g(G)?G.value=e:null),onClear:le,key:v(K)},null,8,["modelValue"])),u("div",L,[u("div",A,[u("div",{class:c(["mr-2",v(O)?"text-[#409eff]":""])},"翻译",2),x(r,{modelValue:v(O),"onUpdate:modelValue":t[3]||(t[3]=e=>g(O)?O.value=e:null)},null,8,["modelValue"])]),u("div",E,[t[12]||(t[12]=u("span",{class:"mr-2"},"影响因子：",-1)),x(te,{modelValue:v(W),"onUpdate:modelValue":t[4]||(t[4]=e=>g(W)?W.value=e:null)},{default:y((()=>[x(V,{label:"3"},{default:y((()=>t[9]||(t[9]=[h(b("<3分"))]))),_:1}),x(V,{label:"5"},{default:y((()=>t[10]||(t[10]=[h("3-10分")]))),_:1}),x(V,{label:"10"},{default:y((()=>t[11]||(t[11]=[h(b(">10分"))]))),_:1})])),_:1},8,["modelValue"])]),u("div",I,[t[13]||(t[13]=u("div",{class:"w-[60px]"},"年份范围",-1)),u("div",P,[x(se,{class:"ml-6 !w-[132px]",modelValue:v(q),"onUpdate:modelValue":t[5]||(t[5]=e=>g(q)?q.value=e:null),range:"",max:2023,min:1990,onChange:ae},null,8,["modelValue"]),u("span",R,b(`${v(q)[0]}-${v(q)[1]}`),1)])])]),u("div",null,[(i(!0),d(p,null,m(v(J).content,((e,l)=>(i(),d("div",{class:"flex mb-8",key:l},[u("div",Y,b(l+1)+".",1),u("div",null,[u("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");G.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,B),v(O)?(i(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,D)):f("",!0)])])))),128))]),v(J)&&v(J).eleTotal?(i(),k(oe,{key:0,class:"pb-10",total:v(J).eleTotal,page:v(Q).pageNo,"onUpdate:page":t[6]||(t[6]=e=>v(Q).pageNo=e),limit:v(Q).pageSize,"onUpdate:limit":t[7]||(t[7]=e=>v(Q).pageSize=e),onPagination:ee},null,8,["total","page","limit"])):f("",!0)])])}}},[["__scopeId","data-v-1e15cf5a"]]);export{F as default};
