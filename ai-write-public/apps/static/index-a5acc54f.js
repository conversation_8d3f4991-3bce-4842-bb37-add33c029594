import{M as a,b as e,u as n,r as s,o,a8 as t,aH as i,aC as l,d as c,e as r,j as p,a as u}from"./index-3808520e.js";const d=u(a({__name:"index",setup(a){const{t:u}=e(),d=n(),v=s(),I=JSON.parse(decodeURIComponent(d.params.payInfo));o((async()=>{g();const a=navigator.userAgent;if(null!=a)if(a.includes("MicroMessenger"))t.warning(u("tool.pleasescanwithalipay"));else if(a.includes("AlipayClient")){const a=await i(I);location.replace(a)}}));const f=()=>{location.replace(location.origin)},g=()=>{v.value=setInterval((async()=>{"PAID"===(await l(I.piId)).payStatus&&(location.replace(location.origin),clearInterval(v.value))}),2e3)};return(a,e)=>(c(),r("div",null,[p("button",{onClick:f},"返回首页")]))}}),[["__scopeId","data-v-ba830091"]]);export{d as default};
