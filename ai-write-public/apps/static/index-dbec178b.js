import{a as e,x as a,ax as s,a6 as o,ay as n,d as t,e as l,j as r,t as i,i as c,F as d,y as u,ak as m,S as h}from"./index-d3526360.js";const g={class:"header ms-header-media"},v={class:"ms-header"},p={class:"wrapper"},w={class:"main-menu-placeholder wrapper clearfix",style:{height:"56px",display:"block !important"}},I={class:"ms-header-img"},f=["href"],k={id:"main-menu",class:"ms-header-nav"},y={class:"header-top header-user",id:"user-info-header"},$={key:0,class:"change_lang"},S={class:"current_lang"},M={class:"ms-link"},_={class:"ms-dropdown-menu",ref:"menu"},C={class:"new-header-avator-pop",id:"new-header-avator"},b={class:"new-header-bottom",style:{padding:"0"}},T={class:"langUl"},x=["onClick"],U={class:"index-user-img_right"},L={href:"#"},N={class:"img-area"},A=["src"],j={class:"ms-dropdown-menu",ref:"menu1"},E={class:"new-header-avator-pop",id:"new-header-avator"},D={class:"new-header-top"},O={class:"new-header-info"},z=["src"],H={class:"new-header-name"};const J=e({data:()=>({avatar:"",userInfo:null,isIncludeTool:!1,selectedLanguage:"",langs:[],hrefUrl:"",intervalId:""}),watch:{userInfoCookie(e,a){}},computed:{baseUrl:()=>"https://www.medsci.cn/",userInfoCookie:()=>a.get("userInfo")},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},getCookieUserInfo:()=>a.get("userInfo"),showMenu(){this.$refs.menu.style.display="block"},hideMenu(){this.$refs.menu.style.display="none"},showMenu1(){this.$refs.menu1.style.display="block"},hideMenu1(){this.$refs.menu1.style.display="none"},toggle(e){const a=this.$route.path,s=this.$route.params.lang;if(s){const o=a.replace(`/${s}`,"");this.$router.push(`/${e}${o}`)}else this.$router.push(`/${e}${a}`);window.localStorage.setItem("ai_apps_lang",e),this.selectedLanguage=e,this.$i18n.locale=e,this.$emit("getAppLang",e),"zh-CN"==localStorage.getItem("ai_apps_lang")?this.$emit("isZHChange",!0):this.$emit("isZHChange",!1),this.$refs.menu.style.display="none"},async logout(){clearInterval(this.intervalId),a.remove("userInfo",{domain:".medon.com.cn"}),a.remove("userInfo",{domain:".medsci.cn"}),a.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("conversation"),Object.keys(localStorage).forEach((e=>{e.includes("writeContent")&&localStorage.removeItem(e)}));let e=localStorage.getItem("socialType");e&&35==e||e&&36==e?s().then((()=>{localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),location.reload()})).catch((e=>{})):(localStorage.removeItem("hasuraToken"),localStorage.removeItem("yudaoToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid"),window.location.origin.includes("medsci.cn")?window.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.location.href:window.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.location.href)},async loginAccount(){let e=o();e&&"zh-CN"!=e?this.$router.push("/login"):window.addLoginDom()},medsciSearch(){let e=document.getElementById("medsciSearchKeyword").value;e?window.open(`https://www.medsci.cn/search?q=${e}`,"_blank"):window.open("https://search.medsci.cn/","_blank")},getAppLangsData(){n().then((e=>{e.map((e=>{e.status||this.langs.push({name:e.value,value:e.remark})}))}))},checkCookie(){const e=a.get("userInfo");e!==this.userInfo&&(this.userInfo=e?JSON.parse(e):"")}},beforeUnmount(){clearInterval(this.intervalId)},mounted(){var e,s;this.intervalId=setInterval(this.checkCookie,1e3),this.hrefUrl="https://ai.medon.com.cn",this.getAppLangsData(),this.userInfo=a.get("userInfo")?JSON.parse(a.get("userInfo")):null,this.isIncludeTool=window.location.href.includes("/tool"),setTimeout((()=>{this.selectedLanguage=o()}),0),this.avatar=(null==(e=this.userInfo)?void 0:e.avatar)?null==(s=this.userInfo)?void 0:s.avatar:"https://img.medsci.cn/web/img/user_icon.png"}},[["render",function(e,a,s,o,n,J){var Z,q,B,F,G;return t(),l("div",g,[r("div",v,[r("div",p,[r("div",w,[r("div",I,[r("a",{href:n.hrefUrl},a[9]||(a[9]=[r("img",{src:"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",alt:""},null,-1)]),8,f)]),r("div",k,[r("div",y,[r("ul",null,[r("li",{class:"index-user-img index-user-img_left",onMouseover:a[0]||(a[0]=(...e)=>J.showMenu&&J.showMenu(...e)),onMouseout:a[1]||(a[1]=(...e)=>J.hideMenu&&J.hideMenu(...e)),onClick:a[2]||(a[2]=(...e)=>J.showMenu&&J.showMenu(...e))},[n.isIncludeTool?c("",!0):(t(),l("div",$,[r("span",S,i(null==(Z=n.langs.filter((e=>e.value==n.selectedLanguage))[0])?void 0:Z.name),1),r("span",M,i(e.$t("market.switch")),1)])),r("div",_,[r("div",C,[r("div",b,[r("div",T,[(t(!0),l(d,null,u(n.langs,(e=>(t(),l("p",{key:e,onClick:m((a=>J.toggle(e.value)),["stop"]),class:h({langItemSelected:e.value===n.selectedLanguage})},i(e.name),11,x)))),128))])])])],512)],32),(null==(q=n.userInfo)?void 0:q.userId)?(t(),l("li",{key:1,class:"index-user-img",onMouseover:a[7]||(a[7]=(...e)=>J.showMenu1&&J.showMenu1(...e)),onMouseout:a[8]||(a[8]=(...e)=>J.hideMenu1&&J.hideMenu1(...e))},[r("a",L,[r("div",N,[r("img",{src:n.avatar,onError:a[4]||(a[4]=(...e)=>J.changeImg&&J.changeImg(...e)),alt:""},null,40,A)])]),r("div",j,[r("div",E,[r("a",{class:"new-header-exit ms-statis","ms-statis":"logout",href:"#",onClick:a[5]||(a[5]=e=>J.logout())},i(e.$t("market.logout")),1),r("div",D,[r("div",O,[r("img",{class:"new-header-avatar",src:n.avatar,onError:a[6]||(a[6]=(...e)=>J.changeImg&&J.changeImg(...e)),alt:""},null,40,z),r("div",H,[r("span",null,i((null==(B=n.userInfo)?void 0:B.realName)?null==(F=n.userInfo)?void 0:F.realName:null==(G=n.userInfo)?void 0:G.userName),1)])])])])],512)],32)):(t(),l(d,{key:0},[r("li",U,[r("a",{href:"javascript: void(0)",class:"ms-link",onClick:a[3]||(a[3]=(...e)=>J.loginAccount&&J.loginAccount(...e))},i(e.$t("market.login")),1)]),a[10]||(a[10]=r("li",null,null,-1))],64))])])])])])])])}],["__scopeId","data-v-7f49563c"]]);export{J as _};
