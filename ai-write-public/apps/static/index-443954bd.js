/* empty css                  */import{g as e}from"./index-cc9c43fe.js";import{a as s,d as a,e as t,j as i,i as l,t as n,b as c,a1 as o,u as r,y as m,r as d,N as p,o as u,x as g,aa as f,aq as w,ar as v,s as h,l as b,a4 as k,f as _,m as y,g as x,p as I,q as S,F as T,z as U,U as j,h as $,k as N,as as F,ak as M,a3 as O,B as C,a7 as A,a8 as z,at as L,I as B,D as H,E as D,au as J,av as E,aw as q,v as V}from"./index-6663023d.js";/* empty css                 */import{_ as P}from"./index-910c5d84.js";import{c as Q}from"./index-ff207b48.js";import{O as G,P as Z,Q as R}from"./index-4df45627.js";/* empty css                   */const W={class:"bg-[#F7F7F7] bg"},K={key:0,id:"footer"},X={class:"footer-copyright1 ms-footer-copy w-footer-copy"},Y={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},ee={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};const se=s({name:"FooterNavZH",data:()=>({showFooter:!1}),head(){},computed:{},mounted(){jQuery(document).ready((function(){jQuery("#footOwl").owlCarousel({items:3,margin:17,responsiveClass:!0,dots:!0,loop:!0,autoplay:!0,autoplayTimeout:1e4,autoplayHoverPause:!0,responsive:{}})}))},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},[["render",function(e,s,c,o,r,m){return a(),t("footer",W,[r.showFooter?(a(),t("div",K,s[0]||(s[0]=[i("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[i("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[i("div",{class:"widget-split item phone-hidden"},[i("div",{class:"widget ms-footer-img"},[i("div",null,[i("p",null,[i("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),i("p",{class:"bold w-footer-bold"}," 梅斯医学MedSci-临床医生发展平台 "),i("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"关于我们"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的业务"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的产品"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),i("div",{class:"w-footer-right phone-hidden"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"新媒体矩阵"),i("div",{id:"footOwl",class:"owl-carousel"},[i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),i("span",null,"梅斯医学")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),i("span",null,"肿瘤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),i("span",null,"血液新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),i("span",null,"风湿新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),i("span",null,"呼吸新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),i("span",null,"皮肤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),i("span",null,"神经新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),i("span",null,"消化新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),i("span",null,"心血管新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),i("span",null,"生物谷")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),i("span",null,"MedSci App")])])])])])],-1)]))):l("",!0),i("div",X,[i("p",null,[i("a",Y,n(e.$t("market.privacyPolicy")),1),s[1]||(s[1]=i("span",{style:{margin:"0px 20px"}},"|",-1)),i("a",ee,n(e.$t("market.termService")),1)])])])}],["__scopeId","data-v-3f35ee11"]]),ae={class:"bg-[#F7F7F7] bg"},te={key:0,id:"footer"},ie={class:"footer-copyright1 ms-footer-copy w-footer-copy"},le={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},ne={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};const ce=s({name:"FooterNavZH",data:()=>({showFooter:!1}),head(){},computed:{},mounted(){jQuery(document).ready((function(){jQuery("#footOwl").owlCarousel({items:3,margin:17,responsiveClass:!0,dots:!0,loop:!0,autoplay:!0,autoplayTimeout:1e4,autoplayHoverPause:!0,responsive:{}})}))},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},[["render",function(e,s,c,o,r,m){return a(),t("footer",ae,[r.showFooter?(a(),t("div",te,s[0]||(s[0]=[i("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[i("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[i("div",{class:"widget-split item phone-hidden"},[i("div",{class:"widget ms-footer-img"},[i("div",null,[i("p",null,[i("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),i("p",{class:"bold w-footer-bold"}," 梅斯医学MedSci-临床医生发展平台 "),i("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"关于我们"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的业务"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的产品"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),i("div",{class:"w-footer-right phone-hidden"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"新媒体矩阵"),i("div",{id:"footOwl",class:"owl-carousel"},[i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),i("span",null,"梅斯医学")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),i("span",null,"肿瘤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),i("span",null,"血液新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),i("span",null,"风湿新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),i("span",null,"呼吸新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),i("span",null,"皮肤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),i("span",null,"神经新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),i("span",null,"消化新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),i("span",null,"心血管新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),i("span",null,"生物谷")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),i("span",null,"MedSci App")])])])])])],-1)]))):l("",!0),i("div",ie,[i("p",null,[i("a",le,n(e.$t("market.privacyPolicy")),1),s[1]||(s[1]=i("span",{style:{margin:"0px 20px"}},"|",-1)),i("a",ne,n(e.$t("market.termService")),1),s[2]||(s[2]=i("span",{style:{margin:"0px 20px"}},"|",-1)),s[3]||(s[3]=i("a",{href:"https://beian.miit.gov.cn/#/Integrated/index",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},"备案号 沪ICP备14018916号-1",-1))])])])}],["__scopeId","data-v-b12245ce"]]);function oe(e,s){const a=Date.now();localStorage.setItem(e+"_value",s),localStorage.setItem(e+"_timestamp",a)}function re(e,s){const a=e+"_value",t=e+"_timestamp",i=localStorage.getItem(a),l=localStorage.getItem(t);if(null!==i&&null!==l){const e=new Date(l);return(new Date-e)/864e5>s?(localStorage.removeItem(a),localStorage.removeItem(t),null):i}return null}function me(){let e=re("current_langs_pack",7),s=re("current_langs_pack_umo",7);if(!e||!s){fetch("https://ai.medon.com.cn/dev-api/ai-base/index/getConfigPage").then((e=>{if(!e.ok)throw new Error("Network response was not ok");return e.json()})).then((s=>{if(0!==s.data.list.length){e=JSON.stringify(function(e){const s={};return e.forEach((e=>{const[a]=e.key.split("."),t=JSON.parse(e.value);s[a]||(s[a]={}),s[a]={...s[a],...t}})),s}(s.data.list)),oe("current_langs_pack",e);let a=s.data.list.filter((e=>"{}"!=e.value&&e.key.includes(".dify"))).reduce(((e,s)=>(e[s.key.substr(0,s.key.indexOf("."))]||(e[s.key.substr(0,s.key.indexOf("."))]={}),e[s.key.substr(0,s.key.indexOf("."))]=JSON.parse(s.value),e)),{});oe("current_langs_pack_umo",JSON.stringify(a))}})).catch((e=>{}))}}const de={class:"bg-[#F9F9F9] overflow-auto"},pe={class:"pt-[75px] text-white mb-[30px] font-bold"},ue={class:"flex justify-center"},ge={class:"content"},fe={class:"flex justify-center my-8 bg-[#F9F9F9]"},we={class:"flex items-center"},ve=["onClick"],he={class:"mr-2 px-4 py-1 cursor-pointer m_font"},be={style:{color:"#757575"},href:"https://aisite.medsci.cn/",target:"_blank"},ke={key:0,class:"menu-box flex flex-wrap justify-between"},_e={class:"flex mb-1 card-item"},ye={class:"flex",style:{width:"75%","align-items":"center"}},xe=["src"],Ie=["title","innerHTML"],Se={style:{width:"30%","text-align":"right","font-size":"14px"}},Te=["title","innerHTML"],Ue={class:"flex justify-between items-center"},je={class:"text-[#B0B0B0]"},$e={key:0,class:"during_order"},Ne={key:1,class:"delay_order"},Fe={key:1,class:"tab_box"},Me={class:"menu-box flex flex-wrap justify-between"},Oe={class:"flex mb-1 card-item"},Ce={class:"flex",style:{width:"75%","align-items":"center"}},Ae=["src"],ze=["title","innerHTML"],Le={style:{width:"30%","text-align":"right"}},Be=["innerHTML"],He={class:"flex justify-between items-center"},De={class:"text-[#B0B0B0]"},Je={key:0,class:"during_order"},Ee={key:1,class:"delay_order"},qe=s({__name:"index",setup(s){const{t:W}=c();o({title:()=>"梅斯小智--梅斯医学AI智能体",meta:[{name:"keywords",content:"AI研究助手 - 您的智能研究伙伴"},{name:"description",content:"MdSci(梅斯医学)旗下梅斯小智是医药领域专属的AI智能体，包括医药写作，翻译，患者诊疗，疑难疾病诊断治疗，医药策略，在线智能医疗，中医大师，药物相互作用，心理咨询，体检报告解读等智能体。"}]});(null==location?void 0:location.origin.includes("medon.com.cn"))||null==location||location.origin.includes("medsci.cn");const K=e("基于AI的写作文本加工.png"),X=e("基于AI的写作文本加工In.png"),Y=r(),ee=m(),ae=d(""),te=d([]),ie=d([{value:"我的应用",remark:"我的应用"},{value:"",remark:"全部"}]),le=d(!1),ne=d(1),oe=d(null),re=d(null),qe=d("first"),Ve=d(null),Pe=d(!1),Qe=d({appType:"",socialUserId:"",appLang:"",order:2,isMine:2}),Ge=()=>{Pe.value=!1},Ze=async(e,s)=>{var a;let t=C();if(null==(a=re.value)?void 0:a.userId){const a={appUuid:s,priceId:e.priceId,monthNum:e.monthNum};let t=await A(a);t&&(z({type:"success",message:W("tool.sS")}),setTimeout((()=>{location.href=t}),1e3))}else t&&"zh-CN"!=t?ee.push("/login"):window.addLoginDom()},Re=e=>{let s=[],a=[];1==ne.value?s=JSON.parse(JSON.stringify(Xe)):0!=ne.value?s=JSON.parse(JSON.stringify(Xe)).filter((e=>e.appType===ie.value[ne.value].value)):0==ne.value&&(s=JSON.parse(JSON.stringify(te.value))),a=s.filter((s=>{if(s.appName.includes(e)||s.appDescription.includes(e)||s.mapType.includes(e))return s})),te.value.forEach((e=>{e.appName=e.appName.replace(/<[^>]+>/g,""),e.appDescription=e.appDescription.replace(/<[^>]+>/g,""),e.mapType=e.mapType.replace(/<[^>]+>/g,"")}));let t=new RegExp(e,"gi");te.value=a.map((s=>(e&&(s.appName=s.appName.replace(t,`<span style="color: #409eff">${e}</span>`),s.appDescription=s.appDescription.replace(t,`<span style="color: #409eff">${e}</span>`),s.mapType=s.mapType.replace(t,`<span style="color: #409eff">${e}</span>`)),s)))},We=setInterval((()=>{const e=g.get("userInfo");e!==re.value&&(re.value=e?JSON.parse(e):""),re.value&&(localStorage.getItem("yudaoToken")&&clearInterval(We),is())}),1e3);p((()=>clearInterval(We)));const Ke=async e=>{if(!(null==e?void 0:e.dAppUuid))return void z({message:"请先至后台绑定应用实例",type:"warning"});L(e.appUuid,localStorage.getItem("openid")),sessionStorage.setItem("nodeInfo",JSON.stringify(e));const s=window.location.origin.includes("medsci.cn")||window.location.origin.includes("medon.com.cn")?window.location.origin+"/apps":window.location.origin;"工具"!==e.appType?"问答"!==e.appType?"写作"===e.appType&&(await me(),localStorage.setItem("appWrite-"+e.appUuid,JSON.stringify({appUuid:e.appUuid,directoryMd:e.directoryMd})),window.open(`${window.location.origin}/ai-write/write/${e.appUuid}`)):window.open(`${s}/chat/${null==e?void 0:e.appUuid}`,"_blank"):window.open(`${s}/tool/${null==e?void 0:e.appUuid}`,"_blank")};let Xe=[];const Ye=d(["https://img.medsci.cn/medsci-ai/bg1.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg2.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg3.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg4.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg5.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg6.png?imageMogr2/format/webp"]);u((async()=>{var e,s,a;const t=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${t}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=Y.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(s=Y.params)?void 0:s.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(le.value=!0),re.value=g.get("userInfo")?JSON.parse(g.get("userInfo")):null,re.value||(localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken")),Y.query.lang?Qe.value.appLang=w[Y.query.lang]:Qe.value.appLang=f();let i=Math.floor(6*Math.random());oe.value=Ye.value[i],(null==(a=re.value)?void 0:a.userId)?(Qe.value.socialUserId=re.value.plaintextUserId,Qe.value.appLang=f()||location.pathname.replaceAll("/",""),ts(),is()):(Qe.value.socialUserId=0,f()?Qe.value.appLang=f():as(location.pathname.replaceAll("/","")),ts()),await es(),(async()=>{var e,s;let a=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=re.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:"",userUuid:null==(s=re.value)?void 0:s.openid}];await O.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",a)})()}));const es=()=>{v().then((e=>{ie.value.push(...e)})).catch()},ss=e=>{le.value=e},as=e=>{Qe.value.appLang=w[e],ts()},ts=()=>{h(Qe.value).then((e=>{var s,a;te.value=null==e?void 0:e.map((e=>({...e,mapType:b[e.appType]}))),""==Qe.value.appType&&(Xe=[...te.value]),1==Qe.value.isMine&&("first"==qe.value&&(te.value=null==(s=te.value)?void 0:s.filter((e=>{var s;return 1==(null==(s=e.appUser)?void 0:s.status)}))),"second"==qe.value&&(te.value=null==(a=te.value)?void 0:a.filter((e=>{var s;return 2==(null==(s=e.appUser)?void 0:s.status)}))))})).catch((e=>{}))},is=()=>{if(localStorage.getItem("yudaoToken"))return void ts();const e=g.get("userInfo");if(e){const a=JSON.parse(e);try{k({userId:a.userId,userName:a.userName,realName:a.realName,avatar:a.avatar,plaintextUserId:a.plaintextUserId,mobile:a.mobile,email:a.email}).then((e=>{(null==e?void 0:e.token)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),ts())}))}catch(s){}}},ls=async e=>{Ve.value=e,Pe.value=!0},ns=()=>{ts()};return(e,s)=>{const c=P,o=B,r=H,m=D,d=J,p=E,u=q,g=ce,f=se,w=V;return a(),t("div",de,[_(c,{onGetAppLang:as,onIsZHChange:ss}),i("div",{class:"flex flex-col items-center h-[246px] relative min-w-[980px]",style:S({background:`url(${x(oe)}) no-repeat center`,backgroundSize:"cover"})},[i("h1",pe,n(e.$t("faq.xAI")),1),i("div",ue,[_(r,{class:"!w-[888px] !h-[54px]",modelValue:x(ae),"onUpdate:modelValue":s[0]||(s[0]=e=>I(ae)?ae.value=e:null),placeholder:e.$t("market.keywords"),clearable:"",onInput:Re},{prefix:y((()=>[_(o,{size:"24",class:"cursor-pointer mt-[2px]"},{default:y((()=>s[4]||(s[4]=[i("img",{class:"w-[24px] h-[24px]",src:"/apps/static/搜索-8381f2c3.svg",alt:""},null,-1)]))),_:1})])),_:1},8,["modelValue","placeholder"])])],4),i("main",null,[i("div",ge,[i("div",fe,[i("div",we,[(a(!0),t(T,null,U(x(ie),((s,i)=>(a(),t("div",{class:j(["mr-2 px-4 py-1 cursor-pointer m_font",x(ne)==i?"bg-[#409eff] text-white rounded-4xl":""]),key:i,onClick:e=>(async(e,s)=>{var a;let t=await C();if(ne.value=e,ae.value="",ae.value&&Re(ae.value),!(null==(a=re.value)?void 0:a.userId)&&0==ne.value)return te.value=[],void(t&&"zh-CN"!=t?ee.push("/login"):window.addLoginDom());0!=ne.value?(Qe.value.isMine=2,Qe.value.order=2,"全部"==s.remark?Qe.value.appType="":Qe.value.appType=s.value,Qe.value.socialUserId=re.value.plaintextUserId):(qe.value="first",Qe.value.appType="",Qe.value.isMine=1,Qe.value.order=1,Qe.value.socialUserId=re.value.plaintextUserId),ts()})(i,s)},n(e.$t(`${x(b)[s.remark]}`)),11,ve)))),128)),i("div",he,[i("a",be,n(e.$t("tool.AINavigationSite")),1)])])]),0!=x(ne)?(a(),t("div",ke,[(a(!0),t(T,null,U(x(te),((s,c)=>(a(),$(d,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:S({background:`url(${1==s.isInternalUser?x(X):x(K)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)"}),key:c,onClick:e=>Ke(s)},{default:y((()=>{var c,r,d,p;return[i("div",_e,[i("div",ye,[i("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:s.appIcon,alt:"icon"},null,8,xe),i("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:s.appName,innerHTML:s.appName},null,8,Ie)]),i("div",Se,[_(m,{style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:M((e=>Ke(s)),["stop"])},{default:y((()=>[N(n(e.$t("market.open")),1),_(o,null,{default:y((()=>[_(x(F),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])])]),i("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",title:s.appDescription,innerHTML:s.appDescription},null,8,Te),i("div",Ue,[i("div",je,n(e.$t(`${x(b)[s.appType]}`)),1),1==(null==(c=s.appUser)?void 0:c.status)?(a(),t("div",$e,n(e.$t("market.subUntil"))+n(null==(r=s.appUser)?void 0:r.expireAt)+n(e.$t("market.expiredOn")),1)):l("",!0),2==(null==(d=s.appUser)?void 0:d.status)?(a(),t("div",Ne,n(e.$t("market.haveBeen"))+n(null==(p=s.appUser)?void 0:p.expireAt)+n(e.$t("market.expiredOn")),1)):l("",!0)])]})),_:2},1032,["style","onClick"])))),128))])):(a(),t("div",Fe,[_(u,{modelValue:x(qe),"onUpdate:modelValue":s[1]||(s[1]=e=>I(qe)?qe.value=e:null),class:"demo-tabs",onTabChange:ns},{default:y((()=>[_(p,{label:e.$t("market.subscribed"),name:"first"},null,8,["label"]),_(p,{label:e.$t("market.expired"),name:"second"},null,8,["label"])])),_:1},8,["modelValue"]),i("div",Me,[(a(!0),t(T,null,U(x(te),((s,c)=>(a(),$(d,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:S({background:`url(${x(K)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)",maxHeight:"189.5px"}),key:c,onClick:e=>(e=>{var s;1==(null==(s=e.appUser)?void 0:s.status)?Ke(e):ls(e)})(s)},{default:y((()=>{var c,r,d,p,u,g;return[i("div",Oe,[i("div",Ce,[i("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:s.appIcon,alt:"icon"},null,8,Ae),i("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:s.appName,innerHTML:s.appName},null,8,ze)]),i("div",Le,[1==(null==(c=s.appUser)?void 0:c.status)?(a(),$(m,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:M((e=>Ke(s)),["stop"])},{default:y((()=>[N(n(e.$t("market.open")),1),_(o,null,{default:y((()=>[_(x(F),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):l("",!0),2==(null==(r=s.appUser)?void 0:r.status)?(a(),$(m,{key:1,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:M((e=>ls(s)),["stop"])},{default:y((()=>[N(n(e.$t("market.renew")),1),_(o,null,{default:y((()=>[_(x(F),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):l("",!0),s.appUser?l("",!0):(a(),$(m,{key:2,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:M((e=>ls(s)),["stop"])},{default:y((()=>[N(n(e.$t("market.subscribe")),1),_(o,null,{default:y((()=>[_(x(F),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"]))])]),i("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",innerHTML:s.appDescription},null,8,Be),i("div",He,[i("div",De,n(e.$t(`${x(b)[s.appType]}`)),1),1==(null==(d=s.appUser)?void 0:d.status)?(a(),t("div",Je,n(e.$t("market.subUntil"))+n(null==(p=s.appUser)?void 0:p.expireAt)+n(e.$t("market.expiredOn")),1)):l("",!0),2==(null==(u=s.appUser)?void 0:u.status)?(a(),t("div",Ee,n(e.$t("market.haveBeen"))+n(null==(g=s.appUser)?void 0:g.expireAt)+n(e.$t("market.expiredOn")),1)):l("",!0)])]})),_:2},1032,["style","onClick"])))),128))])]))])]),x(le)?(a(),$(Q,{key:0})):l("",!0),x(le)?(a(),$(g,{key:1,class:"mobile_footer"})):l("",!0),x(le)?l("",!0):(a(),$(f,{key:2,class:"mobile_footer"})),x(Pe)?(a(),$(w,{key:3,modelValue:x(Pe),"onUpdate:modelValue":s[2]||(s[2]=e=>I(Pe)?Pe.value=e:null),class:"payPC","show-close":!1},{default:y((()=>[_(G,{userInfo:x(re),appTypes:x(b),currentItem:x(Ve),onToAgreement:e.toAgreement,onClose:Ge,onSubscribe:Ze},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):l("",!0),_(x(R),{show:x(Pe),"onUpdate:show":s[3]||(s[3]=e=>I(Pe)?Pe.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:y((()=>[_(Z,{userInfo:x(re),appTypes:x(b),currentItem:x(Ve),onToAgreement:e.toAgreement,onClose:Ge},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])])}}},[["__scopeId","data-v-45777684"]]);export{qe as default};
