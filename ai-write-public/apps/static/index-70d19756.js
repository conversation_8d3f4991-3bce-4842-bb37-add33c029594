/* empty css                  */import{a as e,d as t,e as o,i,b as n,r,Y as a,L as s,az as l,o as c,j as u,g as d,t as h,q as p,F as f,y as g,S as m,k as v,V as y,W as w,f as b,m as C,a8 as k,a6 as x,a7 as I,aA as T,E as S,aB as P,aC as A,aD as B,I as _,O as D,N as E,P as O,Q as L,K as M,p as R,w as N,U as $,aE as z,M as U,X as j,aF as F,aG as G,x as H,aH as q,c as W,h as V}from"./index-ba9fb483.js";/* empty css                   */function X(e){return""===e?e:"true"===e||"1"==e}function Q(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function Y(e,t){for(var o,i="",n=0,r=-1,a=0,s=0;s<=e.length;++s){if(s<e.length)o=e.charCodeAt(s);else{if(47===o)break;o=47}if(47===o){if(r===s-1||1===a);else if(r!==s-1&&2===a){if(i.length<2||2!==n||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2))if(i.length>2){var l=i.lastIndexOf("/");if(l!==i.length-1){-1===l?(i="",n=0):n=(i=i.slice(0,l)).length-1-i.lastIndexOf("/"),r=s,a=0;continue}}else if(2===i.length||1===i.length){i="",n=0,r=s,a=0;continue}t&&(i.length>0?i+="/..":i="..",n=2)}else i.length>0?i+="/"+e.slice(r+1,s):i=e.slice(r+1,s),n=s-r-1;r=s,a=0}else 46===o&&-1!==a?++a:a=-1}return i}var J={resolve:function(){for(var e,t="",o=!1,i=arguments.length-1;i>=-1&&!o;i--){var n;i>=0?n=arguments[i]:(void 0===e&&(e=process.cwd()),n=e),Q(n),0!==n.length&&(t=n+"/"+t,o=47===n.charCodeAt(0))}return t=Y(t,!o),o?t.length>0?"/"+t:"/":t.length>0?t:"."},normalize:function(e){if(Q(e),0===e.length)return".";var t=47===e.charCodeAt(0),o=47===e.charCodeAt(e.length-1);return 0!==(e=Y(e,!t)).length||t||(e="."),e.length>0&&o&&(e+="/"),t?"/"+e:e},isAbsolute:function(e){return Q(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,t=0;t<arguments.length;++t){var o=arguments[t];Q(o),o.length>0&&(void 0===e?e=o:e+="/"+o)}return void 0===e?".":J.normalize(e)},relative:function(e,t){if(Q(e),Q(t),e===t)return"";if((e=J.resolve(e))===(t=J.resolve(t)))return"";for(var o=1;o<e.length&&47===e.charCodeAt(o);++o);for(var i=e.length,n=i-o,r=1;r<t.length&&47===t.charCodeAt(r);++r);for(var a=t.length-r,s=n<a?n:a,l=-1,c=0;c<=s;++c){if(c===s){if(a>s){if(47===t.charCodeAt(r+c))return t.slice(r+c+1);if(0===c)return t.slice(r+c)}else n>s&&(47===e.charCodeAt(o+c)?l=c:0===c&&(l=0));break}var u=e.charCodeAt(o+c);if(u!==t.charCodeAt(r+c))break;47===u&&(l=c)}var d="";for(c=o+l+1;c<=i;++c)c!==i&&47!==e.charCodeAt(c)||(0===d.length?d+="..":d+="/..");return d.length>0?d+t.slice(r+l):(r+=l,47===t.charCodeAt(r)&&++r,t.slice(r))},_makeLong:function(e){return e},dirname:function(e){if(Q(e),0===e.length)return".";for(var t=e.charCodeAt(0),o=47===t,i=-1,n=!0,r=e.length-1;r>=1;--r)if(47===(t=e.charCodeAt(r))){if(!n){i=r;break}}else n=!1;return-1===i?o?"/":".":o&&1===i?"//":e.slice(0,i)},basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');Q(e);var o,i=0,n=-1,r=!0;if(void 0!==t&&t.length>0&&t.length<=e.length){if(t.length===e.length&&t===e)return"";var a=t.length-1,s=-1;for(o=e.length-1;o>=0;--o){var l=e.charCodeAt(o);if(47===l){if(!r){i=o+1;break}}else-1===s&&(r=!1,s=o+1),a>=0&&(l===t.charCodeAt(a)?-1==--a&&(n=o):(a=-1,n=s))}return i===n?n=s:-1===n&&(n=e.length),e.slice(i,n)}for(o=e.length-1;o>=0;--o)if(47===e.charCodeAt(o)){if(!r){i=o+1;break}}else-1===n&&(r=!1,n=o+1);return-1===n?"":e.slice(i,n)},extname:function(e){Q(e);for(var t=-1,o=0,i=-1,n=!0,r=0,a=e.length-1;a>=0;--a){var s=e.charCodeAt(a);if(47!==s)-1===i&&(n=!1,i=a+1),46===s?-1===t?t=a:1!==r&&(r=1):-1!==t&&(r=-1);else if(!n){o=a+1;break}}return-1===t||-1===i||0===r||1===r&&t===i-1&&t===o+1?"":e.slice(t,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var o=t.dir||t.root,i=t.base||(t.name||"")+(t.ext||"");return o?o===t.root?o+i:o+e+i:i}("/",e)},parse:function(e){Q(e);var t={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return t;var o,i=e.charCodeAt(0),n=47===i;n?(t.root="/",o=1):o=0;for(var r=-1,a=0,s=-1,l=!0,c=e.length-1,u=0;c>=o;--c)if(47!==(i=e.charCodeAt(c)))-1===s&&(l=!1,s=c+1),46===i?-1===r?r=c:1!==u&&(u=1):-1!==r&&(u=-1);else if(!l){a=c+1;break}return-1===r||-1===s||0===u||1===u&&r===s-1&&r===a+1?-1!==s&&(t.base=t.name=0===a&&n?e.slice(1,s):e.slice(a,s)):(0===a&&n?(t.name=e.slice(1,r),t.base=e.slice(1,s)):(t.name=e.slice(a,r),t.base=e.slice(a,s)),t.ext=e.slice(r,s)),a>0?t.dir=e.slice(0,a-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};J.posix=J;const K=J.extname,Z=J.basename;class ee{constructor(){let e="undefined"==typeof global,t="image/png",o="image/jpeg",i="image/jpeg",n="image/webp",r="application/pdf",a="image/svg+xml";Object.assign(this,{toMime:this.toMime.bind(this),fromMime:this.fromMime.bind(this),expected:e?'"png", "jpg", or "webp"':'"png", "jpg", "pdf", or "svg"',formats:e?{png:t,jpg:o,jpeg:i,webp:n}:{png:t,jpg:o,jpeg:i,pdf:r,svg:a},mimes:e?{[t]:"png",[o]:"jpg",[n]:"webp"}:{[t]:"png",[o]:"jpg",[r]:"pdf",[a]:"svg"}})}toMime(e){return this.formats[(e||"").replace(/^\./,"").toLowerCase()]}fromMime(e){return this.mimes[e]}}class te{static for(e){return(new te).append(e).get()}constructor(){this.crc=-1}get(){return~this.crc}append(e){for(var t=0|this.crc,o=this.table,i=0,n=0|e.length;i<n;i++)t=t>>>8^o[255&(t^e[i])];return this.crc=t,this}}function oe(e){let t=new Uint8Array(e),o=new DataView(t.buffer),i={array:t,view:o,size:e,set8:(e,t)=>(o.setUint8(e,t),i),set16:(e,t)=>(o.setUint16(e,t,!0),i),set32:(e,t)=>(o.setUint32(e,t,!0),i),bytes:(e,o)=>(t.set(o,e),i)};return i}te.prototype.table=(()=>{var e,t,o,i=[];for(e=0;e<256;e++){for(o=e,t=0;t<8;t++)o=1&o?o>>>1^3988292384:o>>>1;i[e]=o}return i})();class ie{constructor(e){let t=new Date;Object.assign(this,{directory:e,offset:0,files:[],time:(t.getHours()<<6|t.getMinutes())<<5|t.getSeconds()/2,date:(t.getFullYear()-1980<<4|t.getMonth()+1)<<5|t.getDate()}),this.add(e)}async add(e,t){let o=!t,i=ie.encoder.encode(`${this.directory}/${o?"":e}`),n=new Uint8Array(o?0:await t.arrayBuffer()),r=30+i.length,a=r+n.length,{offset:s}=this,l=oe(26).set32(0,134742036).set16(6,this.time).set16(8,this.date).set32(10,te.for(n)).set32(14,n.length).set32(18,n.length).set16(22,i.length);s+=r;let c=oe(r+n.length+16).set32(0,67324752).bytes(4,l.array).bytes(30,i).bytes(r,n);s+=n.length,c.set32(a,134695760).bytes(a+4,l.array.slice(10,22)),s+=16,this.files.push({offset:s,folder:o,name:i,header:l,payload:c}),this.offset=s}toBuffer(){let e=this.files.reduce(((e,{name:t})=>46+t.length+e),0),t=oe(e+22),o=0;for(var{offset:i,name:n,header:r,folder:a}of this.files)t.set32(o,33639248).set16(o+4,20).bytes(o+6,r.array).set8(o+38,a?16:0).set32(o+42,i).bytes(o+46,n),o+=46+n.length;t.set32(o,101010256).set16(o+8,this.files.length).set16(o+10,this.files.length).set32(o+12,e).set32(o+16,this.offset);let s=new Uint8Array(this.offset+t.size),l=0;for(var{payload:c}of this.files)s.set(c.array,l),l+=c.size;return s.set(t.array,l),s}get blob(){return new Blob([this.toBuffer()],{type:"application/zip"})}}ie.encoder=new TextEncoder;const ne=(e,t,o,i)=>{if(i){let{width:t,height:o}=e,n=Object.assign(document.createElement("canvas"),{width:t,height:o}),r=n.getContext("2d");r.fillStyle=i,r.fillRect(0,0,t,o),r.drawImage(e,0,0),e=n}return new Promise(((i,n)=>e.toBlob(i,t,o)))},re=(e,t)=>{const o=window.URL.createObjectURL(t),i=document.createElement("a");i.style.display="none",i.href=o,i.setAttribute("download",e),void 0===i.download&&i.setAttribute("target","_blank"),document.body.appendChild(i),i.click(),document.body.removeChild(i),setTimeout((()=>window.URL.revokeObjectURL(o)),100)},ae={asBuffer:(...e)=>ne(...e).then((e=>e.arrayBuffer())),asDownload:async(e,t,o,i,n)=>{re(n,await ne(e,t,o,i))},asZipDownload:async(e,t,o,i,n,r,a)=>{let s=Z(n,".zip")||"archive",l=new ie(s);await Promise.all(e.map((async(e,n)=>{let s=(e=>r.replace("{}",String(e+1).padStart(a,"0")))(n);await l.add(s,await ne(e,t,o,i))}))),re(`${s}.zip`,l.blob)},atScale:(e,t,o)=>e.map((e=>{if(1==t&&!o)return e.canvas;let i=document.createElement("canvas"),n=i.getContext("2d"),r=e.canvas?e.canvas:e;return i.width=r.width*t,i.height=r.height*t,o&&(n.fillStyle=o,n.fillRect(0,0,i.width,i.height)),n.scale(t,t),n.drawImage(r,0,0),i})),options:function(e,{filename:t="",extension:o="",format:i,page:n,quality:r,matte:a,density:s,outline:l,archive:c}={}){var{fromMime:u,toMime:d,expected:h}=new ee,p=(c=c||"canvas",i||o.replace(/@\d+x$/i,"")||K(t)),f=(i=u(d(p)||p),d(i)),g=e.length;if(!p)throw new Error("Cannot determine image format (use a filename extension or 'format' argument)");if(!i)throw new Error(`Unsupported file format "${p}" (expected ${h})`);if(!g)throw new RangeError("Canvas has no associated contexts (try calling getContext or newPage first)");let m,v,y=t.replace(/{(\d*)}/g,((e,t)=>(v=!0,t=parseInt(t,10),m=isFinite(t)?t:isFinite(m)?m:-1,"{}"))),w=n>0?n-1:n<0?g+n:void 0;if(isFinite(w)&&w<0||w>=g)throw new RangeError(1==g?`Canvas only has a ‘page 1’ (${w} is out of bounds)`:`Canvas has pages 1–${g} (${w} is out of bounds)`);if(e=isFinite(w)?[e[w]]:v||"pdf"==i?e:e.slice(-1),void 0===r)r=.92;else if("number"!=typeof r||!isFinite(r)||r<0||r>1)throw new TypeError("The quality option must be an number in the 0.0–1.0 range");if(void 0===s){let e=(o||Z(t,p)).match(/@(\d+)x$/i);s=e?parseInt(e[1],10):1}else if("number"!=typeof s||!Number.isInteger(s)||s<1)throw new TypeError("The density option must be a non-negative integer");return void 0===l?l=!0:"svg"==i&&(l=!!l),{filename:t,pattern:y,format:i,mime:f,pages:e,padding:m,quality:r,matte:a,density:s,outline:l,archive:c}}},{asBuffer:se,asDownload:le,asZipDownload:ce,atScale:ue,options:de}=ae,he=Symbol.for("toDataURL");const{CanvasRenderingContext2D:pe,CanvasGradient:fe,CanvasPattern:ge,Image:me,ImageData:ve,Path2D:ye,DOMMatrix:we,DOMRect:be,DOMPoint:Ce}=window,ke={Canvas:class{constructor(e,t){let o=document.createElement("canvas"),i=[];for(var[n,r]of(Object.defineProperty(o,"async",{value:!0,writable:!1,enumerable:!0}),Object.entries({png:()=>se(o,"image/png"),jpg:()=>se(o,"image/jpeg"),pages:()=>i.concat(o).map((e=>e.getContext("2d")))})))Object.defineProperty(o,n,{get:r});return Object.assign(o,{width:e,height:t,newPage(...e){var{width:t,height:n}=o,r=Object.assign(document.createElement("canvas"),{width:t,height:n});r.getContext("2d").drawImage(o,0,0),i.push(r);var[t,n]=e.length?e:[t,n];return Object.assign(o,{width:t,height:n}).getContext("2d")},saveAs(e,t){t="number"==typeof t?{quality:t}:t;let o=de(this.pages,{filename:e,...t}),{pattern:i,padding:n,mime:r,quality:a,matte:s,density:l,archive:c}=o,u=ue(o.pages,l);return null==n?le(u[0],r,a,s,e):ce(u,r,a,s,c,i,n)},toBuffer(e="png",t={}){t="number"==typeof t?{quality:t}:t;let o=de(this.pages,{extension:e,...t}),{mime:i,quality:n,matte:r,pages:a,density:s}=o,l=ue(a,s,r)[0];return se(l,i,n,r)},[he]:o.toDataURL.bind(o),toDataURL(e="png",t={}){t="number"==typeof t?{quality:t}:t;let i=de(this.pages,{extension:e,...t}),{mime:n,quality:r,matte:a,pages:s,density:l}=i,c=ue(s,l,a)[0],u=c[c===o?he:"toDataURL"](n,r);return Promise.resolve(u)}})}},loadImage:e=>new Promise(((t,o)=>Object.assign(new me,{crossOrigin:"Anonymous",onload:t,onerror:o,src:e}))),CanvasRenderingContext2D:pe,CanvasGradient:fe,CanvasPattern:ge,Image:me,ImageData:ve,Path2D:ye,DOMMatrix:we,DOMRect:be,DOMPoint:Ce},xe=(e,t,o={},i=o)=>{if(Array.isArray(t))t.forEach((t=>xe(e,t,o,i)));else if("function"==typeof t)t(e,o,i,xe);else{const n=Object.keys(t)[0];Array.isArray(t[n])?(i[n]={},xe(e,t[n],o,i[n])):i[n]=t[n](e,o,i,xe)}return o},Ie=(e,t)=>(o,i,n,r)=>{t(o,i,n)&&r(o,e,i,n)},Te=(e=0)=>t=>t.data[t.pos+e],Se=e=>t=>t.data.subarray(t.pos,t.pos+=e),Pe=e=>t=>t.data.subarray(t.pos,t.pos+e),Ae=e=>t=>Array.from(Se(e)(t)).map((e=>String.fromCharCode(e))).join(""),Be=e=>t=>{const o=Se(2)(t);return e?(o[1]<<8)+o[0]:(o[0]<<8)+o[1]},_e=(e,t)=>(o,i,n)=>{const r="function"==typeof t?t(o,i,n):t,a=Se(e),s=new Array(r);for(var l=0;l<r;l++)s[l]=a(o);return s},De=e=>t=>{const o=(e=>e.data[e.pos++])(t),i=new Array(8);for(var n=0;n<8;n++)i[7-n]=!!(o&1<<n);return Object.keys(e).reduce(((t,o)=>{const n=e[o];return n.length?t[o]=((e,t,o)=>{for(var i=0,n=0;n<o;n++)i+=e[t+n]&&2**(o-n-1);return i})(i,n.index,n.length):t[o]=i[n.index],t}),{})};var Ee={blocks:e=>{const t=[],o=e.data.length;for(var i=0,n=(e=>e.data[e.pos++])(e);0!==n&&n;n=(e=>e.data[e.pos++])(e)){if(e.pos+n>=o){const n=o-e.pos;t.push(Se(n)(e)),i+=n;break}t.push(Se(n)(e)),i+=n}const r=new Uint8Array(i);for(var a=0,s=0;s<t.length;s++)r.set(t[s],a),a+=t[s].length;return r}};const Oe=Ie({gce:[{codes:Se(2)},{byteSize:e=>e.data[e.pos++]},{extras:De({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:Be(!0)},{transparentColorIndex:e=>e.data[e.pos++]},{terminator:e=>e.data[e.pos++]}]},(e=>{var t=Pe(2)(e);return 33===t[0]&&249===t[1]})),Le=Ie({image:[{code:e=>e.data[e.pos++]},{descriptor:[{left:Be(!0)},{top:Be(!0)},{width:Be(!0)},{height:Be(!0)},{lct:De({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},Ie({lct:_e(3,((e,t,o)=>Math.pow(2,o.descriptor.lct.size+1)))},((e,t,o)=>o.descriptor.lct.exists)),{data:[{minCodeSize:e=>e.data[e.pos++]},Ee]}]},(e=>44===Te()(e))),Me=Ie({text:[{codes:Se(2)},{blockSize:e=>e.data[e.pos++]},{preData:(e,t,o)=>Se(o.text.blockSize)(e)},Ee]},(e=>{var t=Pe(2)(e);return 33===t[0]&&1===t[1]})),Re=Ie({application:[{codes:Se(2)},{blockSize:e=>e.data[e.pos++]},{id:(e,t,o)=>Ae(o.blockSize)(e)},Ee]},(e=>{var t=Pe(2)(e);return 33===t[0]&&255===t[1]})),Ne=Ie({comment:[{codes:Se(2)},Ee]},(e=>{var t=Pe(2)(e);return 33===t[0]&&254===t[1]})),$e=[{header:[{signature:Ae(3)},{version:Ae(3)}]},{lsd:[{width:Be(!0)},{height:Be(!0)},{gct:De({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:e=>e.data[e.pos++]},{pixelAspectRatio:e=>e.data[e.pos++]}]},Ie({gct:_e(3,((e,t)=>Math.pow(2,t.lsd.gct.size+1)))},((e,t)=>t.lsd.gct.exists)),{frames:(ze=[Oe,Re,Ne,Le,Me],Ue=e=>{var t=Te()(e);return 33===t||44===t},(e,t,o,i)=>{const n=[];let r=e.pos;for(;Ue(e,t,o);){const o={};if(i(e,ze,t,o),e.pos===r)break;r=e.pos,n.push(o)}return n})}];var ze,Ue;const je=(e,t,o)=>{if(!e.image)return;const{image:i}=e,n=i.descriptor.width*i.descriptor.height;var r=((e,t,o)=>{const i=4096,n=o;var r,a,s,l,c,u,d,h,p,f;const g=new Array(o),m=new Array(i),v=new Array(i),y=new Array(4097);for(c=1+(a=1<<(f=e)),r=a+2,d=-1,s=(1<<(l=f+1))-1,h=0;h<a;h++)m[h]=0,v[h]=h;var w,b,C,k,x,I;for(w=b=C=k=x=I=0,p=0;p<n;){if(0===k){if(b<l){w+=t[I]<<b,b+=8,I++;continue}if(h=w&s,w>>=l,b-=l,h>r||h==c)break;if(h==a){s=(1<<(l=f+1))-1,r=a+2,d=-1;continue}if(-1==d){y[k++]=v[h],d=h,C=h;continue}for(u=h,h==r&&(y[k++]=C,h=d);h>a;)y[k++]=v[h],h=m[h];C=255&v[h],y[k++]=C,r<i&&(m[r]=d,v[r]=C,!(++r&s)&&r<i&&(l++,s+=r)),d=u}k--,g[x++]=y[k],p++}for(p=x;p<n;p++)g[p]=0;return g})(i.data.minCodeSize,i.data.blocks,n);i.descriptor.lct.interlaced&&(r=((e,t)=>{const o=new Array(e.length),i=e.length/t,n=function(i,n){const r=e.slice(n*t,(n+1)*t);o.splice.apply(o,[i*t,t].concat(r))},r=[0,4,2,1],a=[8,8,4,2];for(var s=0,l=0;l<4;l++)for(var c=r[l];c<i;c+=a[l])n(c,s),s++;return o})(r,i.descriptor.width));const a={pixels:r,dims:{top:e.image.descriptor.top,left:e.image.descriptor.left,width:e.image.descriptor.width,height:e.image.descriptor.height}};return i.descriptor.lct&&i.descriptor.lct.exists?a.colorTable=i.lct:a.colorTable=t,e.gce&&(a.delay=10*(e.gce.delay||10),a.disposalType=e.gce.extras.disposal,e.gce.extras.transparentColorGiven&&(a.transparentIndex=e.gce.transparentColorIndex)),o&&(a.patch=(e=>{const t=e.pixels.length,o=new Uint8ClampedArray(4*t);for(var i=0;i<t;i++){const t=4*i,n=e.pixels[i],r=e.colorTable[n];o[t]=r[0],o[t+1]=r[1],o[t+2]=r[2],o[t+3]=n!==e.transparentIndex?255:0}return o})(a)),a};function Fe(e){var t=encodeURI(e).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return t.length+(t.length!=Number(e)?3:0)}class Ge{constructor(e){this.mode=We.MODE_8BIT_BYTE,this.parsedData=[],this.data=e;const t=[];for(let o=0,i=this.data.length;o<i;o++){const e=[],i=this.data.charCodeAt(o);i>65536?(e[0]=240|(1835008&i)>>>18,e[1]=128|(258048&i)>>>12,e[2]=128|(4032&i)>>>6,e[3]=128|63&i):i>2048?(e[0]=224|(61440&i)>>>12,e[1]=128|(4032&i)>>>6,e[2]=128|63&i):i>128?(e[0]=192|(1984&i)>>>6,e[1]=128|63&i):e[0]=i,t.push(e)}this.parsedData=Array.prototype.concat.apply([],t),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}getLength(){return this.parsedData.length}write(e){for(let t=0,o=this.parsedData.length;t<o;t++)e.put(this.parsedData[t],8)}}class He{constructor(e=-1,t=qe.L){this.moduleCount=0,this.dataList=[],this.typeNumber=e,this.errorCorrectLevel=t,this.moduleCount=0,this.dataList=[]}addData(e){if(this.typeNumber<=0)this.typeNumber=function(e,t){for(var o=1,i=Fe(e),n=0,r=Ze.length;n<r;n++){var a=0;switch(t){case qe.L:a=Ze[n][0];break;case qe.M:a=Ze[n][1];break;case qe.Q:a=Ze[n][2];break;case qe.H:a=Ze[n][3]}if(i<=a)break;o++}if(o>Ze.length)throw new Error("Too long data");return o}(e,this.errorCorrectLevel);else{if(this.typeNumber>40)throw new Error(`Invalid QR version: ${this.typeNumber}`);if(!function(e,t,o){const i=Fe(t),n=e-1;let r=0;switch(o){case qe.L:r=Ze[n][0];break;case qe.M:r=Ze[n][1];break;case qe.Q:r=Ze[n][2];break;case qe.H:r=Ze[n][3]}return i<=r}(this.typeNumber,e,this.errorCorrectLevel))throw new Error(`Data is too long for QR version: ${this.typeNumber}`)}const t=new Ge(e);this.dataList.push(t),this.dataCache=void 0}isDark(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(`${e},${t}`);return this.modules[e][t]}getModuleCount(){return this.moduleCount}make(){this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(let o=0;o<this.moduleCount;o++){this.modules[o]=new Array(this.moduleCount);for(let e=0;e<this.moduleCount;e++)this.modules[o][e]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=He.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)}setupPositionProbePattern(e,t){for(let o=-1;o<=7;o++)if(!(e+o<=-1||this.moduleCount<=e+o))for(let i=-1;i<=7;i++)t+i<=-1||this.moduleCount<=t+i||(this.modules[e+o][t+i]=0<=o&&o<=6&&(0==i||6==i)||0<=i&&i<=6&&(0==o||6==o)||2<=o&&o<=4&&2<=i&&i<=4)}getBestMaskPattern(){if(Number.isInteger(this.maskPattern)&&Object.values(Ve).includes(this.maskPattern))return this.maskPattern;let e=0,t=0;for(let o=0;o<8;o++){this.makeImpl(!0,o);const i=Xe.getLostPoint(this);(0==o||e>i)&&(e=i,t=o)}return t}setupTimingPattern(){for(let e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(let e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)}setupPositionAdjustPattern(){const e=Xe.getPatternPosition(this.typeNumber);for(let t=0;t<e.length;t++)for(let o=0;o<e.length;o++){const i=e[t],n=e[o];if(null==this.modules[i][n])for(let e=-2;e<=2;e++)for(let t=-2;t<=2;t++)this.modules[i+e][n+t]=-2==e||2==e||-2==t||2==t||0==e&&0==t}}setupTypeNumber(e){const t=Xe.getBCHTypeNumber(this.typeNumber);for(var o=0;o<18;o++){var i=!e&&1==(t>>o&1);this.modules[Math.floor(o/3)][o%3+this.moduleCount-8-3]=i}for(o=0;o<18;o++){i=!e&&1==(t>>o&1);this.modules[o%3+this.moduleCount-8-3][Math.floor(o/3)]=i}}setupTypeInfo(e,t){const o=this.errorCorrectLevel<<3|t,i=Xe.getBCHTypeInfo(o);for(var n=0;n<15;n++){var r=!e&&1==(i>>n&1);n<6?this.modules[n][8]=r:n<8?this.modules[n+1][8]=r:this.modules[this.moduleCount-15+n][8]=r}for(n=0;n<15;n++){r=!e&&1==(i>>n&1);n<8?this.modules[8][this.moduleCount-n-1]=r:n<9?this.modules[8][15-n-1+1]=r:this.modules[8][15-n-1]=r}this.modules[this.moduleCount-8][8]=!e}mapData(e,t){let o=-1,i=this.moduleCount-1,n=7,r=0;for(let a=this.moduleCount-1;a>0;a-=2)for(6==a&&a--;;){for(let o=0;o<2;o++)if(null==this.modules[i][a-o]){let s=!1;r<e.length&&(s=1==(e[r]>>>n&1));Xe.getMask(t,i,a-o)&&(s=!s),this.modules[i][a-o]=s,n--,-1==n&&(r++,n=7)}if(i+=o,i<0||this.moduleCount<=i){i-=o,o=-o;break}}}static createData(e,t,o){const i=Je.getRSBlocks(e,t),n=new Ke;for(var r=0;r<o.length;r++){const t=o[r];n.put(t.mode,4),n.put(t.getLength(),Xe.getLengthInBits(t.mode,e)),t.write(n)}let a=0;for(r=0;r<i.length;r++)a+=i[r].dataCount;if(n.getLengthInBits()>8*a)throw new Error(`code length overflow. (${n.getLengthInBits()}>${8*a})`);for(n.getLengthInBits()+4<=8*a&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(!1);for(;!(n.getLengthInBits()>=8*a||(n.put(He.PAD0,8),n.getLengthInBits()>=8*a));)n.put(He.PAD1,8);return He.createBytes(n,i)}static createBytes(e,t){let o=0,i=0,n=0;const r=new Array(t.length),a=new Array(t.length);for(var s=0;s<t.length;s++){const c=t[s].dataCount,u=t[s].totalCount-c;i=Math.max(i,c),n=Math.max(n,u),r[s]=new Array(c);for(var l=0;l<r[s].length;l++)r[s][l]=255&e.buffer[l+o];o+=c;const d=Xe.getErrorCorrectPolynomial(u),h=new Ye(r[s],d.getLength()-1).mod(d);a[s]=new Array(d.getLength()-1);for(l=0;l<a[s].length;l++){const e=l+h.getLength()-a[s].length;a[s][l]=e>=0?h.get(e):0}}let c=0;for(l=0;l<t.length;l++)c+=t[l].totalCount;const u=new Array(c);let d=0;for(l=0;l<i;l++)for(s=0;s<t.length;s++)l<r[s].length&&(u[d++]=r[s][l]);for(l=0;l<n;l++)for(s=0;s<t.length;s++)l<a[s].length&&(u[d++]=a[s][l]);return u}}He.PAD0=236,He.PAD1=17;const qe={L:1,M:0,Q:3,H:2},We={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},Ve={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};class Xe{static getBCHTypeInfo(e){let t=e<<10;for(;Xe.getBCHDigit(t)-Xe.getBCHDigit(Xe.G15)>=0;)t^=Xe.G15<<Xe.getBCHDigit(t)-Xe.getBCHDigit(Xe.G15);return(e<<10|t)^Xe.G15_MASK}static getBCHTypeNumber(e){let t=e<<12;for(;Xe.getBCHDigit(t)-Xe.getBCHDigit(Xe.G18)>=0;)t^=Xe.G18<<Xe.getBCHDigit(t)-Xe.getBCHDigit(Xe.G18);return e<<12|t}static getBCHDigit(e){let t=0;for(;0!=e;)t++,e>>>=1;return t}static getPatternPosition(e){return Xe.PATTERN_POSITION_TABLE[e-1]}static getMask(e,t,o){switch(e){case Ve.PATTERN000:return(t+o)%2==0;case Ve.PATTERN001:return t%2==0;case Ve.PATTERN010:return o%3==0;case Ve.PATTERN011:return(t+o)%3==0;case Ve.PATTERN100:return(Math.floor(t/2)+Math.floor(o/3))%2==0;case Ve.PATTERN101:return t*o%2+t*o%3==0;case Ve.PATTERN110:return(t*o%2+t*o%3)%2==0;case Ve.PATTERN111:return(t*o%3+(t+o)%2)%2==0;default:throw new Error(`bad maskPattern:${e}`)}}static getErrorCorrectPolynomial(e){let t=new Ye([1],0);for(let o=0;o<e;o++)t=t.multiply(new Ye([1,Qe.gexp(o)],0));return t}static getLengthInBits(e,t){if(1<=t&&t<10)switch(e){case We.MODE_NUMBER:return 10;case We.MODE_ALPHA_NUM:return 9;case We.MODE_8BIT_BYTE:case We.MODE_KANJI:return 8;default:throw new Error(`mode:${e}`)}else if(t<27)switch(e){case We.MODE_NUMBER:return 12;case We.MODE_ALPHA_NUM:return 11;case We.MODE_8BIT_BYTE:return 16;case We.MODE_KANJI:return 10;default:throw new Error(`mode:${e}`)}else{if(!(t<41))throw new Error(`type:${t}`);switch(e){case We.MODE_NUMBER:return 14;case We.MODE_ALPHA_NUM:return 13;case We.MODE_8BIT_BYTE:return 16;case We.MODE_KANJI:return 12;default:throw new Error(`mode:${e}`)}}}static getLostPoint(e){const t=e.getModuleCount();let o=0;for(var i=0;i<t;i++)for(var n=0;n<t;n++){let r=0;const a=e.isDark(i,n);for(let o=-1;o<=1;o++)if(!(i+o<0||t<=i+o))for(let s=-1;s<=1;s++)n+s<0||t<=n+s||0==o&&0==s||a==e.isDark(i+o,n+s)&&r++;r>5&&(o+=3+r-5)}for(i=0;i<t-1;i++)for(n=0;n<t-1;n++){let t=0;e.isDark(i,n)&&t++,e.isDark(i+1,n)&&t++,e.isDark(i,n+1)&&t++,e.isDark(i+1,n+1)&&t++,0!=t&&4!=t||(o+=3)}for(i=0;i<t;i++)for(n=0;n<t-6;n++)e.isDark(i,n)&&!e.isDark(i,n+1)&&e.isDark(i,n+2)&&e.isDark(i,n+3)&&e.isDark(i,n+4)&&!e.isDark(i,n+5)&&e.isDark(i,n+6)&&(o+=40);for(n=0;n<t;n++)for(i=0;i<t-6;i++)e.isDark(i,n)&&!e.isDark(i+1,n)&&e.isDark(i+2,n)&&e.isDark(i+3,n)&&e.isDark(i+4,n)&&!e.isDark(i+5,n)&&e.isDark(i+6,n)&&(o+=40);let r=0;for(n=0;n<t;n++)for(i=0;i<t;i++)e.isDark(i,n)&&r++;return o+=10*(Math.abs(100*r/t/t-50)/5),o}}Xe.PATTERN_POSITION_TABLE=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],Xe.G15=1335,Xe.G18=7973,Xe.G15_MASK=21522;class Qe{static glog(e){if(e<1)throw new Error(`glog(${e})`);return Qe.LOG_TABLE[e]}static gexp(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return Qe.EXP_TABLE[e]}}Qe.EXP_TABLE=new Array(256),Qe.LOG_TABLE=new Array(256),Qe._constructor=function(){for(var e=0;e<8;e++)Qe.EXP_TABLE[e]=1<<e;for(e=8;e<256;e++)Qe.EXP_TABLE[e]=Qe.EXP_TABLE[e-4]^Qe.EXP_TABLE[e-5]^Qe.EXP_TABLE[e-6]^Qe.EXP_TABLE[e-8];for(e=0;e<255;e++)Qe.LOG_TABLE[Qe.EXP_TABLE[e]]=e}();class Ye{constructor(e,t){if(null==e.length)throw new Error(`${e.length}/${t}`);let o=0;for(;o<e.length&&0==e[o];)o++;this.num=new Array(e.length-o+t);for(let i=0;i<e.length-o;i++)this.num[i]=e[i+o]}get(e){return this.num[e]}getLength(){return this.num.length}multiply(e){const t=new Array(this.getLength()+e.getLength()-1);for(let o=0;o<this.getLength();o++)for(let i=0;i<e.getLength();i++)t[o+i]^=Qe.gexp(Qe.glog(this.get(o))+Qe.glog(e.get(i)));return new Ye(t,0)}mod(e){if(this.getLength()-e.getLength()<0)return this;const t=Qe.glog(this.get(0))-Qe.glog(e.get(0)),o=new Array(this.getLength());for(var i=0;i<this.getLength();i++)o[i]=this.get(i);for(i=0;i<e.getLength();i++)o[i]^=Qe.gexp(Qe.glog(e.get(i))+t);return new Ye(o,0).mod(e)}}class Je{constructor(e,t){this.totalCount=e,this.dataCount=t}static getRSBlocks(e,t){const o=Je.getRsBlockTable(e,t);if(null==o)throw new Error(`bad rs block @ typeNumber:${e}/errorCorrectLevel:${t}`);const i=o.length/3,n=[];for(let r=0;r<i;r++){const e=o[3*r+0],t=o[3*r+1],i=o[3*r+2];for(let o=0;o<e;o++)n.push(new Je(t,i))}return n}static getRsBlockTable(e,t){switch(t){case qe.L:return Je.RS_BLOCK_TABLE[4*(e-1)+0];case qe.M:return Je.RS_BLOCK_TABLE[4*(e-1)+1];case qe.Q:return Je.RS_BLOCK_TABLE[4*(e-1)+2];case qe.H:return Je.RS_BLOCK_TABLE[4*(e-1)+3];default:return}}}Je.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];class Ke{constructor(){this.buffer=[],this.length=0}get(e){const t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)}put(e,t){for(let o=0;o<t;o++)this.putBit(1==(e>>>t-o-1&1))}getLengthInBits(){return this.length}putBit(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}}const Ze=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];var et=256,tt=1024,ot=1<<18;function it(e,t){var o,i,n,r,a;function s(e,t,i,n,r){o[t][0]-=e*(o[t][0]-i)/tt,o[t][1]-=e*(o[t][1]-n)/tt,o[t][2]-=e*(o[t][2]-r)/tt}function l(e,t,i,n,r){for(var s,l,c=Math.abs(t-e),u=Math.min(t+e,et),d=t+1,h=t-1,p=1;d<u||h>c;)l=a[p++],d<u&&((s=o[d++])[0]-=l*(s[0]-i)/ot,s[1]-=l*(s[1]-n)/ot,s[2]-=l*(s[2]-r)/ot),h>c&&((s=o[h--])[0]-=l*(s[0]-i)/ot,s[1]-=l*(s[1]-n)/ot,s[2]-=l*(s[2]-r)/ot)}function c(e,t,i){var a,s,l,c,u,d=~(1<<31),h=d,p=-1,f=p;for(a=0;a<et;a++)s=o[a],(l=Math.abs(s[0]-e)+Math.abs(s[1]-t)+Math.abs(s[2]-i))<d&&(d=l,p=a),(c=l-(n[a]>>12))<h&&(h=c,f=a),u=r[a]>>10,r[a]-=u,n[a]+=u<<10;return r[p]+=64,n[p]-=65536,f}this.buildColormap=function(){!function(){var e,t;for(o=[],i=new Int32Array(256),n=new Int32Array(et),r=new Int32Array(et),a=new Int32Array(32),e=0;e<et;e++)t=(e<<12)/et,o[e]=new Float64Array([t,t,t,0]),r[e]=256,n[e]=0}(),function(){var o,i,n,r,u,d,h=e.length,p=30+(t-1)/3,f=h/(3*t),g=~~(f/100),m=tt,v=2048,y=v>>6;for(y<=1&&(y=0),o=0;o<y;o++)a[o]=m*(256*(y*y-o*o)/(y*y));h<1509?(t=1,i=3):i=h%499!=0?1497:h%491!=0?1473:h%487!=0?1461:1509;var w=0;for(o=0;o<f;)if(s(m,d=c(n=(255&e[w])<<4,r=(255&e[w+1])<<4,u=(255&e[w+2])<<4),n,r,u),0!==y&&l(y,d,n,r,u),(w+=i)>=h&&(w-=h),0===g&&(g=1),++o%g==0)for(m-=m/p,(y=(v-=v/30)>>6)<=1&&(y=0),d=0;d<y;d++)a[d]=m*(256*(y*y-d*d)/(y*y))}(),function(){for(var e=0;e<et;e++)o[e][0]>>=4,o[e][1]>>=4,o[e][2]>>=4,o[e][3]=e}(),function(){var e,t,n,r,a,s,l=0,c=0;for(e=0;e<et;e++){for(a=e,s=(n=o[e])[1],t=e+1;t<et;t++)(r=o[t])[1]<s&&(a=t,s=r[1]);if(r=o[a],e!=a&&(t=r[0],r[0]=n[0],n[0]=t,t=r[1],r[1]=n[1],n[1]=t,t=r[2],r[2]=n[2],n[2]=t,t=r[3],r[3]=n[3],n[3]=t),s!=l){for(i[l]=c+e>>1,t=l+1;t<s;t++)i[t]=e;l=s,c=e}}for(i[l]=c+255>>1,t=l+1;t<256;t++)i[t]=255}()},this.getColormap=function(){for(var e=[],t=[],i=0;i<et;i++)t[o[i][3]]=i;for(var n=0,r=0;r<et;r++){var a=t[r];e[n++]=o[a][0],e[n++]=o[a][1],e[n++]=o[a][2]}return e},this.lookupRGB=function(e,t,n){for(var r,a,s,l=1e3,c=-1,u=i[t],d=u-1;u<et||d>=0;)u<et&&((s=(a=o[u])[1]-t)>=l?u=et:(u++,s<0&&(s=-s),(r=a[0]-e)<0&&(r=-r),(s+=r)<l&&((r=a[2]-n)<0&&(r=-r),(s+=r)<l&&(l=s,c=a[3])))),d>=0&&((s=t-(a=o[d])[1])>=l?d=-1:(d--,s<0&&(s=-s),(r=a[0]-e)<0&&(r=-r),(s+=r)<l&&((r=a[2]-n)<0&&(r=-r),(s+=r)<l&&(l=s,c=a[3]))));return c}}var nt=5003,rt=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535];function at(e,t,o,i){var n,r,a,s,l,c,u,d,h,p=Math.max(2,i),f=new Uint8Array(256),g=new Int32Array(nt),m=new Int32Array(nt),v=0,y=0,w=!1;function b(e,t){f[r++]=e,r>=254&&x(t)}function C(e){k(nt),y=l+2,w=!0,S(l,e)}function k(e){for(var t=0;t<e;++t)g[t]=-1}function x(e){r>0&&(e.writeByte(r),e.writeBytes(f,0,r),r=0)}function I(e){return(1<<e)-1}function T(){return 0===u?-1:(--u,255&o[d++])}function S(e,t){for(n&=rt[v],v>0?n|=e<<v:n=e,v+=h;v>=8;)b(255&n,t),n>>=8,v-=8;if((y>a||w)&&(w?(a=I(h=s),w=!1):(++h,a=12==h?4096:I(h))),e==c){for(;v>0;)b(255&n,t),n>>=8,v-=8;x(t)}}this.encode=function(o){o.writeByte(p),u=e*t,d=0,function(e,t){var o,i,n,u,d,p,f;for(w=!1,a=I(h=s=e),c=1+(l=1<<e-1),y=l+2,r=0,u=T(),f=0,o=nt;o<65536;o*=2)++f;f=8-f,k(p=nt),S(l,t);e:for(;-1!=(i=T());)if(o=(i<<12)+u,g[n=i<<f^u]!==o){if(g[n]>=0){d=p-n,0===n&&(d=1);do{if((n-=d)<0&&(n+=p),g[n]===o){u=m[n];continue e}}while(g[n]>=0)}S(u,t),u=i,y<4096?(m[n]=y++,g[n]=o):C(t)}else u=m[n];S(u,t),S(c,t)}(p+1,o),o.writeByte(0)}}function st(){this.page=-1,this.pages=[],this.newPage()}st.pageSize=4096,st.charMap={};for(var lt=0;lt<256;lt++)st.charMap[lt]=String.fromCharCode(lt);function ct(e,t){this.width=~~e,this.height=~~t,this.transparent=null,this.transIndex=0,this.repeat=-1,this.delay=0,this.image=null,this.pixels=null,this.indexedPixels=null,this.colorDepth=null,this.colorTab=null,this.neuQuant=null,this.usedEntry=new Array,this.palSize=7,this.dispose=-1,this.firstFrame=!0,this.sample=10,this.dither=!1,this.globalPalette=!1,this.out=new st}st.prototype.newPage=function(){this.pages[++this.page]=new Uint8Array(st.pageSize),this.cursor=0},st.prototype.getData=function(){for(var e="",t=0;t<this.pages.length;t++)for(var o=0;o<st.pageSize;o++)e+=st.charMap[this.pages[t][o]];return e},st.prototype.toFlattenUint8Array=function(){const e=[];for(var t=0;t<this.pages.length;t++)if(t===this.pages.length-1){const o=Uint8Array.from(this.pages[t].slice(0,this.cursor));e.push(o)}else e.push(this.pages[t]);const o=new Uint8Array(e.reduce(((e,t)=>e+t.length),0));return e.reduce(((e,t)=>(o.set(t,e),e+t.length)),0),o},st.prototype.writeByte=function(e){this.cursor>=st.pageSize&&this.newPage(),this.pages[this.page][this.cursor++]=e},st.prototype.writeUTFBytes=function(e){for(var t=e.length,o=0;o<t;o++)this.writeByte(e.charCodeAt(o))},st.prototype.writeBytes=function(e,t,o){for(var i=o||e.length,n=t||0;n<i;n++)this.writeByte(e[n])},ct.prototype.setDelay=function(e){this.delay=Math.round(e/10)},ct.prototype.setFrameRate=function(e){this.delay=Math.round(100/e)},ct.prototype.setDispose=function(e){e>=0&&(this.dispose=e)},ct.prototype.setRepeat=function(e){this.repeat=e},ct.prototype.setTransparent=function(e){this.transparent=e},ct.prototype.addFrame=function(e){this.image=e,this.colorTab=this.globalPalette&&this.globalPalette.slice?this.globalPalette:null,this.getImagePixels(),this.analyzePixels(),!0===this.globalPalette&&(this.globalPalette=this.colorTab),this.firstFrame&&(this.writeHeader(),this.writeLSD(),this.writePalette(),this.repeat>=0&&this.writeNetscapeExt()),this.writeGraphicCtrlExt(),this.writeImageDesc(),this.firstFrame||this.globalPalette||this.writePalette(),this.writePixels(),this.firstFrame=!1},ct.prototype.finish=function(){this.out.writeByte(59)},ct.prototype.setQuality=function(e){e<1&&(e=1),this.sample=e},ct.prototype.setDither=function(e){!0===e&&(e="FloydSteinberg"),this.dither=e},ct.prototype.setGlobalPalette=function(e){this.globalPalette=e},ct.prototype.getGlobalPalette=function(){return this.globalPalette&&this.globalPalette.slice&&this.globalPalette.slice(0)||this.globalPalette},ct.prototype.writeHeader=function(){this.out.writeUTFBytes("GIF89a")},ct.prototype.analyzePixels=function(){this.colorTab||(this.neuQuant=new it(this.pixels,this.sample),this.neuQuant.buildColormap(),this.colorTab=this.neuQuant.getColormap()),this.dither?this.ditherPixels(this.dither.replace("-serpentine",""),null!==this.dither.match(/-serpentine/)):this.indexPixels(),this.pixels=null,this.colorDepth=8,this.palSize=7,null!==this.transparent&&(this.transIndex=this.findClosest(this.transparent,!0))},ct.prototype.indexPixels=function(e){var t=this.pixels.length/3;this.indexedPixels=new Uint8Array(t);for(var o=0,i=0;i<t;i++){var n=this.findClosestRGB(255&this.pixels[o++],255&this.pixels[o++],255&this.pixels[o++]);this.usedEntry[n]=!0,this.indexedPixels[i]=n}},ct.prototype.ditherPixels=function(e,t){var o={FalseFloydSteinberg:[[3/8,1,0],[3/8,0,1],[2/8,1,1]],FloydSteinberg:[[7/16,1,0],[3/16,-1,1],[5/16,0,1],[1/16,1,1]],Stucki:[[8/42,1,0],[4/42,2,0],[2/42,-2,1],[4/42,-1,1],[8/42,0,1],[4/42,1,1],[2/42,2,1],[1/42,-2,2],[2/42,-1,2],[4/42,0,2],[2/42,1,2],[1/42,2,2]],Atkinson:[[1/8,1,0],[1/8,2,0],[1/8,-1,1],[1/8,0,1],[1/8,1,1],[1/8,0,2]]};if(!e||!o[e])throw"Unknown dithering kernel: "+e;var i=o[e],n=0,r=this.height,a=this.width,s=this.pixels,l=t?-1:1;this.indexedPixels=new Uint8Array(this.pixels.length/3);for(var c=0;c<r;c++){t&&(l*=-1);for(var u=1==l?0:a-1,d=1==l?a:0;u!==d;u+=l){var h=3*(n=c*a+u),p=s[h],f=s[h+1],g=s[h+2];h=this.findClosestRGB(p,f,g),this.usedEntry[h]=!0,this.indexedPixels[n]=h,h*=3;for(var m=p-this.colorTab[h],v=f-this.colorTab[h+1],y=g-this.colorTab[h+2],w=1==l?0:i.length-1,b=1==l?i.length:0;w!==b;w+=l){var C=i[w][1],k=i[w][2];if(C+u>=0&&C+u<a&&k+c>=0&&k+c<r){var x=i[w][0];h=n+C+k*a,s[h*=3]=Math.max(0,Math.min(255,s[h]+m*x)),s[h+1]=Math.max(0,Math.min(255,s[h+1]+v*x)),s[h+2]=Math.max(0,Math.min(255,s[h+2]+y*x))}}}}},ct.prototype.findClosest=function(e,t){return this.findClosestRGB((16711680&e)>>16,(65280&e)>>8,255&e,t)},ct.prototype.findClosestRGB=function(e,t,o,i){if(null===this.colorTab)return-1;if(this.neuQuant&&!i)return this.neuQuant.lookupRGB(e,t,o);for(var n=0,r=16777216,a=this.colorTab.length,s=0,l=0;s<a;l++){var c=e-(255&this.colorTab[s++]),u=t-(255&this.colorTab[s++]),d=o-(255&this.colorTab[s++]),h=c*c+u*u+d*d;(!i||this.usedEntry[l])&&h<r&&(r=h,n=l)}return n},ct.prototype.getImagePixels=function(){var e=this.width,t=this.height;this.pixels=new Uint8Array(e*t*3);for(var o=this.image,i=0,n=0,r=0;r<t;r++)for(var a=0;a<e;a++)this.pixels[n++]=o[i++],this.pixels[n++]=o[i++],this.pixels[n++]=o[i++],i++},ct.prototype.writeGraphicCtrlExt=function(){var e,t;this.out.writeByte(33),this.out.writeByte(249),this.out.writeByte(4),null===this.transparent?(e=0,t=0):(e=1,t=2),this.dispose>=0&&(t=7&this.dispose),t<<=2,this.out.writeByte(t|e),this.writeShort(this.delay),this.out.writeByte(this.transIndex),this.out.writeByte(0)},ct.prototype.writeImageDesc=function(){this.out.writeByte(44),this.writeShort(0),this.writeShort(0),this.writeShort(this.width),this.writeShort(this.height),this.firstFrame||this.globalPalette?this.out.writeByte(0):this.out.writeByte(128|this.palSize)},ct.prototype.writeLSD=function(){this.writeShort(this.width),this.writeShort(this.height),this.out.writeByte(240|this.palSize),this.out.writeByte(0),this.out.writeByte(0)},ct.prototype.writeNetscapeExt=function(){this.out.writeByte(33),this.out.writeByte(255),this.out.writeByte(11),this.out.writeUTFBytes("NETSCAPE2.0"),this.out.writeByte(3),this.out.writeByte(1),this.writeShort(this.repeat),this.out.writeByte(0)},ct.prototype.writePalette=function(){this.out.writeBytes(this.colorTab);for(var e=768-this.colorTab.length,t=0;t<e;t++)this.out.writeByte(0)},ct.prototype.writeShort=function(e){this.out.writeByte(255&e),this.out.writeByte(e>>8&255)},ct.prototype.writePixels=function(){new at(this.width,this.height,this.indexedPixels,this.colorDepth).encode(this.out)},ct.prototype.stream=function(){return this.out};var ut=globalThis&&globalThis.__awaiter||function(e,t,o,i){return new(o||(o=Promise))((function(n,r){function a(e){try{l(i.next(e))}catch(t){r(t)}}function s(e){try{l(i.throw(e))}catch(t){r(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof o?t:new o((function(e){e(t)}))).then(a,s)}l((i=i.apply(e,t||[])).next())}))};const{Canvas:dt}=ke,ht=.4;function pt(e){if(e)return new Promise((function(o,i){if("data"==e.slice(0,4)){let n=new Image;return n.onload=function(){o(n),t(n)},n.onerror=function(){i("Image load error"),t(n)},void(n.src=e)}let n=new Image;n.setAttribute("crossOrigin","Anonymous"),n.onload=function(){o(n)},n.onerror=function(){i("Image load error")},n.src=e}));function t(e){e.onload=null,e.onerror=null}}class ft{constructor(e){const t=Object.assign({},e);if(Object.keys(ft.defaultOptions).forEach((e=>{e in t||Object.defineProperty(t,e,{value:ft.defaultOptions[e],enumerable:!0,writable:!0})})),t.components?"object"==typeof t.components&&Object.keys(ft.defaultComponentOptions).forEach((e=>{e in t.components?Object.defineProperty(t.components,e,{value:Object.assign(Object.assign({},ft.defaultComponentOptions[e]),t.components[e]),enumerable:!0,writable:!0}):Object.defineProperty(t.components,e,{value:ft.defaultComponentOptions[e],enumerable:!0,writable:!0})})):t.components=ft.defaultComponentOptions,null!==t.dotScale&&void 0!==t.dotScale){if(t.dotScale<=0||t.dotScale>1)throw new Error("dotScale should be in range (0, 1].");t.components.data.scale=t.dotScale,t.components.timing.scale=t.dotScale,t.components.alignment.scale=t.dotScale}this.options=t,this.canvas=new dt(e.size,e.size),this.canvasContext=this.canvas.getContext("2d"),this.qrCode=new He(-1,this.options.correctLevel),Number.isInteger(this.options.maskPattern)&&(this.qrCode.maskPattern=this.options.maskPattern),Number.isInteger(this.options.version)&&(this.qrCode.typeNumber=this.options.version),this.qrCode.addData(this.options.text),this.qrCode.make()}draw(){return new Promise((e=>this._draw().then(e)))}_clear(){this.canvasContext.clearRect(0,0,this.canvas.width,this.canvas.height)}static _prepareRoundedCornerClip(e,t,o,i,n,r){e.beginPath(),e.moveTo(t,o),e.arcTo(t+i,o,t+i,o+n,r),e.arcTo(t+i,o+n,t,o+n,r),e.arcTo(t,o+n,t,o,r),e.arcTo(t,o,t+i,o,r),e.closePath()}static _getAverageRGB(e){const t={r:0,g:0,b:0};let o,i,n=-4;const r={r:0,g:0,b:0};let a=0;i=e.naturalHeight||e.height,o=e.naturalWidth||e.width;const s=new dt(o,i).getContext("2d");if(!s)return t;let l;s.drawImage(e,0,0);try{l=s.getImageData(0,0,o,i)}catch(c){return t}for(;(n+=20)<l.data.length;)l.data[n]>200||l.data[n+1]>200||l.data[n+2]>200||(++a,r.r+=l.data[n],r.g+=l.data[n+1],r.b+=l.data[n+2]);return r.r=~~(r.r/a),r.g=~~(r.g/a),r.b=~~(r.b/a),r}static _drawDot(e,t,o,i,n=0,r=1){e.fillRect((t+n)*i,(o+n)*i,r*i,r*i)}static _drawAlignProtector(e,t,o,i){e.clearRect((t-2)*i,(o-2)*i,5*i,5*i),e.fillRect((t-2)*i,(o-2)*i,5*i,5*i)}static _drawAlign(e,t,o,i,n=0,r=1,a,s){const l=e.fillStyle;e.fillStyle=a,new Array(4).fill(0).map(((a,s)=>{ft._drawDot(e,t-2+s,o-2,i,n,r),ft._drawDot(e,t+2,o-2+s,i,n,r),ft._drawDot(e,t+2-s,o+2,i,n,r),ft._drawDot(e,t-2,o+2-s,i,n,r)})),ft._drawDot(e,t,o,i,n,r),s||(e.fillStyle="rgba(255, 255, 255, 0.6)",new Array(2).fill(0).map(((a,s)=>{ft._drawDot(e,t-1+s,o-1,i,n,r),ft._drawDot(e,t+1,o-1+s,i,n,r),ft._drawDot(e,t+1-s,o+1,i,n,r),ft._drawDot(e,t-1,o+1-s,i,n,r)}))),e.fillStyle=l}_draw(){var e,t,o,i,n,r,a,s,l,c,u,d,h,p,f,g,m,v,y;return ut(this,void 0,void 0,(function*(){const w=null===(e=this.qrCode)||void 0===e?void 0:e.moduleCount,b=this.options.size;let C=this.options.margin;(C<0||2*C>=b)&&(C=0);const k=Math.ceil(C),x=b-2*C,I=this.options.whiteMargin,T=this.options.backgroundDimming,S=Math.ceil(x/w),P=S*w,A=P+2*k,B=new dt(A,A),_=B.getContext("2d");this._clear(),_.save(),_.translate(k,k);const D=new dt(A,A),E=D.getContext("2d");let O=null,L=[];if(this.options.gifBackground){const e=(e=>{const t=new Uint8Array(e);return xe({data:t,pos:0},$e)})(this.options.gifBackground);if(O=e,R=!0,L=(M=e).frames.filter((e=>e.image)).map((e=>je(e,M.gct,R))),this.options.autoColor){let e=0,t=0,o=0,i=0;for(let n=0;n<L[0].colorTable.length;n++){const r=L[0].colorTable[n];r[0]>200||r[1]>200||r[2]>200||(0===r[0]&&0===r[1]&&0===r[2]||(i++,e+=r[0],t+=r[1],o+=r[2]))}e=~~(e/i),t=~~(t/i),o=~~(o/i),this.options.colorDark=`rgb(${e},${t},${o})`}}else if(this.options.backgroundImage){const e=yield pt(this.options.backgroundImage);if(this.options.autoColor){const t=ft._getAverageRGB(e);this.options.colorDark=`rgb(${t.r},${t.g},${t.b})`}E.drawImage(e,0,0,e.width,e.height,0,0,A,A),E.rect(0,0,A,A),E.fillStyle=T,E.fill()}else E.rect(0,0,A,A),E.fillStyle=this.options.colorLight,E.fill();var M,R;const N=Xe.getPatternPosition(this.qrCode.typeNumber),$=(null===(o=null===(t=this.options.components)||void 0===t?void 0:t.data)||void 0===o?void 0:o.scale)||ht,z=.5*(1-$);for(let e=0;e<w;e++)for(let t=0;t<w;t++){const o=this.qrCode.isDark(e,t),i=t<8&&(e<8||e>=w-8)||t>=w-8&&e<8;let n=i||(6==e&&t>=8&&t<=w-8||6==t&&e>=8&&e<=w-8);for(let s=1;s<N.length-1;s++)n=n||e>=N[s]-2&&e<=N[s]+2&&t>=N[s]-2&&t<=N[s]+2;const r=t*S+(n?0:z*S),a=e*S+(n?0:z*S);if(_.strokeStyle=o?this.options.colorDark:this.options.colorLight,_.lineWidth=.5,_.fillStyle=o?this.options.colorDark:this.options.colorLight,0===N.length)n||_.fillRect(r,a,(n?1:$)*S,(n?1:$)*S);else{n||t<w-4&&t>=w-4-5&&e<w-4&&e>=w-4-5||_.fillRect(r,a,(n?1:$)*S,(n?1:$)*S)}}const U=N[N.length-1],j=this.options.colorLight;if(_.fillStyle=j,_.fillRect(0,0,8*S,8*S),_.fillRect(0,(w-8)*S,8*S,8*S),_.fillRect((w-8)*S,0,8*S,8*S),(null===(n=null===(i=this.options.components)||void 0===i?void 0:i.timing)||void 0===n?void 0:n.protectors)&&(_.fillRect(8*S,6*S,(w-8-8)*S,S),_.fillRect(6*S,8*S,S,(w-8-8)*S)),(null===(a=null===(r=this.options.components)||void 0===r?void 0:r.cornerAlignment)||void 0===a?void 0:a.protectors)&&ft._drawAlignProtector(_,U,U,S),null===(l=null===(s=this.options.components)||void 0===s?void 0:s.alignment)||void 0===l?void 0:l.protectors)for(let e=0;e<N.length;e++)for(let t=0;t<N.length;t++){const o=N[t],i=N[e];(6!==o||6!==i&&i!==U)&&((6!==i||6!==o&&o!==U)&&(o===U&&i===U||ft._drawAlignProtector(_,o,i,S)))}_.fillStyle=this.options.colorDark,_.fillRect(0,0,7*S,S),_.fillRect((w-7)*S,0,7*S,S),_.fillRect(0,6*S,7*S,S),_.fillRect((w-7)*S,6*S,7*S,S),_.fillRect(0,(w-7)*S,7*S,S),_.fillRect(0,(w-7+6)*S,7*S,S),_.fillRect(0,0,S,7*S),_.fillRect(6*S,0,S,7*S),_.fillRect((w-7)*S,0,S,7*S),_.fillRect((w-7+6)*S,0,S,7*S),_.fillRect(0,(w-7)*S,S,7*S),_.fillRect(6*S,(w-7)*S,S,7*S),_.fillRect(2*S,2*S,3*S,3*S),_.fillRect((w-7+2)*S,2*S,3*S,3*S),_.fillRect(2*S,(w-7+2)*S,3*S,3*S);const F=(null===(u=null===(c=this.options.components)||void 0===c?void 0:c.timing)||void 0===u?void 0:u.scale)||ht,G=.5*(1-F);for(let e=0;e<w-8;e+=2)ft._drawDot(_,8+e,6,S,G,F),ft._drawDot(_,6,8+e,S,G,F);const H=(null===(h=null===(d=this.options.components)||void 0===d?void 0:d.cornerAlignment)||void 0===h?void 0:h.scale)||ht,q=.5*(1-H);ft._drawAlign(_,U,U,S,q,H,this.options.colorDark,(null===(f=null===(p=this.options.components)||void 0===p?void 0:p.cornerAlignment)||void 0===f?void 0:f.protectors)||!1);const W=(null===(m=null===(g=this.options.components)||void 0===g?void 0:g.alignment)||void 0===m?void 0:m.scale)||ht,V=.5*(1-W);for(let e=0;e<N.length;e++)for(let t=0;t<N.length;t++){const o=N[t],i=N[e];(6!==o||6!==i&&i!==U)&&((6!==i||6!==o&&o!==U)&&(o===U&&i===U||ft._drawAlign(_,o,i,S,V,W,this.options.colorDark,(null===(y=null===(v=this.options.components)||void 0===v?void 0:v.alignment)||void 0===y?void 0:y.protectors)||!1)))}if(I&&(_.fillStyle=this.options.backgroundColor,_.fillRect(-k,-k,A,k),_.fillRect(-k,P,A,k),_.fillRect(P,-k,k,A),_.fillRect(-k,-k,k,A)),this.options.logoImage){const e=yield pt(this.options.logoImage);let t=this.options.logoScale,o=this.options.logoMargin,i=this.options.logoCornerRadius;(t<=0||t>=1)&&(t=.2),o<0&&(o=0),i<0&&(i=0);const n=P*t,r=.5*(A-n),a=r;_.restore(),_.fillStyle=this.options.logoBackgroundColor,_.save(),ft._prepareRoundedCornerClip(_,r-o,a-o,n+2*o,n+2*o,i+o),_.clip();const s=_.globalCompositeOperation;_.globalCompositeOperation="destination-out",_.fill(),_.globalCompositeOperation=s,_.restore(),_.save(),ft._prepareRoundedCornerClip(_,r,a,n,n,i),_.clip(),_.drawImage(e,r,a,n,n),_.restore(),_.save(),_.translate(k,k)}if(O){let e,t,o,i,n,r;if(L.forEach((function(a){e||(e=new ct(b,b),e.setDelay(a.delay),e.setRepeat(0));const{width:s,height:l}=a.dims;t||(t=new dt(s,l),o=t.getContext("2d"),o.rect(0,0,t.width,t.height),o.fillStyle="#ffffff",o.fill()),i&&r&&s===i.width&&l===i.height||(i=new dt(s,l),n=i.getContext("2d"),r=n.createImageData(s,l)),r.data.set(a.patch),n.putImageData(r,0,0),o.drawImage(i.getContext("2d").canvas,a.dims.left,a.dims.top);const c=new dt(A,A),u=c.getContext("2d");u.drawImage(t.getContext("2d").canvas,0,0,A,A),u.rect(0,0,A,A),u.fillStyle=T,u.fill(),u.drawImage(B.getContext("2d").canvas,0,0,A,A);const d=new dt(b,b),h=d.getContext("2d");h.drawImage(c.getContext("2d").canvas,0,0,b,b),e.addFrame(h.getImageData(0,0,d.width,d.height).data)})),!e)throw new Error("No frames.");if(e.finish(),gt(this.canvas)){const t=e.stream().toFlattenUint8Array().reduce(((e,t)=>e+String.fromCharCode(t)),"");return Promise.resolve(`data:image/gif;base64,${window.btoa(t)}`)}return Promise.resolve(Buffer.from(e.stream().toFlattenUint8Array()))}{E.drawImage(B.getContext("2d").canvas,0,0,A,A),_.drawImage(D.getContext("2d").canvas,-k,-k,A,A);const e=new dt(b,b);e.getContext("2d").drawImage(B.getContext("2d").canvas,0,0,b,b),this.canvas=e;const t=this.options.gifBackground?"gif":"png";return gt(this.canvas)?Promise.resolve(this.canvas.toDataURL(t)):Promise.resolve(this.canvas.toBuffer(t))}}))}}function gt(e){try{return e instanceof HTMLElement}catch(t){return"object"==typeof e&&1===e.nodeType&&"object"==typeof e.style&&"object"==typeof e.ownerDocument}}ft.CorrectLevel=qe,ft.defaultComponentOptions={data:{scale:.4},timing:{scale:.5,protectors:!1},alignment:{scale:.5,protectors:!1},cornerAlignment:{scale:.5,protectors:!0}},ft.defaultOptions={text:"",size:400,margin:20,colorDark:"#000000",colorLight:"rgba(255, 255, 255, 0.6)",correctLevel:qe.M,backgroundImage:void 0,backgroundDimming:"rgba(0,0,0,0)",logoImage:void 0,logoScale:.2,logoMargin:4,logoCornerRadius:8,whiteMargin:!0,components:ft.defaultComponentOptions,autoColor:!0,logoBackgroundColor:"#ffffff",backgroundColor:"#ffffff"};const mt={props:{text:{type:String,required:!0},qid:{type:String},correctLevel:{type:Number,default:1},size:{type:Number,default:200},margin:{type:Number,default:20},colorDark:{type:String,default:"#000000"},colorLight:{type:String,default:"#FFFFFF"},bgSrc:{type:String,default:void 0},background:{type:String,default:"rgba(0,0,0,0)"},backgroundDimming:{type:String,default:"rgba(0,0,0,0)"},logoSrc:{type:String,default:void 0},logoBackgroundColor:{type:String,default:"rgba(255,255,255,1)"},gifBgSrc:{type:String,default:void 0},logoScale:{type:Number,default:.2},logoMargin:{type:Number,default:0},logoCornerRadius:{type:Number,default:8},whiteMargin:{type:[Boolean,String],default:!0},dotScale:{type:Number,default:1},autoColor:{type:[Boolean,String],default:!0},binarize:{type:[Boolean,String],default:!1},binarizeThreshold:{type:Number,default:128},callback:{type:Function,default:function(){}},bindElement:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},components:{default:function(){return{data:{scale:1},timing:{scale:1,protectors:!1},alignment:{scale:1,protectors:!1},cornerAlignment:{scale:1,protectors:!0}}}}},name:"vue-qr",data:()=>({imgUrl:""}),watch:{$props:{deep:!0,handler(){this.main()}}},mounted(){this.main()},methods:{async main(){if(this.gifBgSrc){const t=await(e=this.gifBgSrc,new Promise(((t,o)=>{var i=new XMLHttpRequest;i.responseType="blob",i.onload=function(){var e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsArrayBuffer(i.response)},i.open("GET",e),i.send()}))),o=this.logoSrc;return void this.render(void 0,o,t)}var e;const t=this.bgSrc,o=this.logoSrc;this.render(t,o)},async render(e,t,o){const i=this;new ft({gifBackground:o,text:i.text,size:i.size,margin:i.margin,colorDark:i.colorDark,colorLight:i.colorLight,backgroundColor:i.backgroundColor,backgroundImage:e,backgroundDimming:i.backgroundDimming,logoImage:t,logoScale:i.logoScale,logoBackgroundColor:i.logoBackgroundColor,correctLevel:i.correctLevel,logoMargin:i.logoMargin,logoCornerRadius:i.logoCornerRadius,whiteMargin:X(i.whiteMargin),dotScale:i.dotScale,autoColor:X(i.autoColor),binarize:X(i.binarize),binarizeThreshold:i.binarizeThreshold,components:i.components}).draw().then((e=>{this.imgUrl=e,i.callback&&i.callback(e,i.qid)}))}}},vt=["src"];const yt=e(mt,[["render",function(e,n,r,a,s,l){return r.bindElement?(t(),o("img",{key:0,style:{display:"inline-block"},src:s.imgUrl},null,8,vt)):i("",!0)}]]),wt={id:"app"},bt={class:"scale"},Ct={class:"micro_header"},kt={class:"micro_left"},xt={class:"avatar"},It=["src"],Tt={class:"t1"},St={class:"micro_main"},Pt={class:"micro_main_top"},At={class:"micro_main-sp"},Bt={class:"micro_main_temp"},_t=["onClick"],Dt={class:"title"},Et={class:"price"},Ot={class:"micro_main_middle"},Lt={class:"micro_main_middle_banner"},Mt={class:"micro_main_middle_title"},Rt={class:"micro_main_middle_content"},Nt={key:0,class:"micro_main_bottom"},$t={class:"micro_pay"},zt={class:"micro_pay_right"},Ut={class:"noQrCode"},jt={class:"price"},Ft={class:"micro_way"},Gt={class:"t1"},Ht={class:"bd"},qt={class:"t2"},Wt={key:1,class:"btns"},Vt={key:2,class:"btns"},Xt=e({__name:"index",props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})}},emits:["close"],setup(e,{emit:B}){const{t:_}=n(),D=r(!1),E=a(),O=((null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location||location.origin.includes("medsci.cn")),r(!1)),L=e,M=L.userInfo||{},R=L.currentItem,N=r({}),$=r(),z=B,U=r(""),j=r(),F=r(),G=r(),H=r(localStorage.getItem("socialType")),q=r((null==M?void 0:M.avatar)?null==M?void 0:M.avatar:"https://img.medsci.cn/web/img/user_icon.png"),W=()=>{q.value="https://img.medsci.cn/web/img/user_icon.png"},V=()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},X=()=>{const e=window.innerWidth;O.value=e>768},Q=()=>{clearInterval(G.value),z("close")},Y=(e,t)=>{N.value=e,$.value=t,0==H.value&&0!=e.feePrice&&J(e,R.appUuid)};s((()=>{clearInterval(G.value)}));const J=async(e,t)=>{if(!N.value.coinType)return void k.warning("请选择订阅服务周期");let o=await x();if(null==M?void 0:M.userId){const o={appUuid:t,priceId:e.priceId,monthNum:e.monthNum};try{D.value=!0;let t=await I(o);if(t)if(D.value=!1,0==H.value&&0!=e.feePrice){const e=t;location.origin.includes(".medsci.cn")||location.origin.includes(".medon.com.cn")?U.value=location.origin+"/apps/payLink/"+encodeURIComponent(e):U.value=location.origin+"/payLink/"+encodeURIComponent(e),F.value=JSON.parse(e).piId,await void(G.value=setInterval((async()=>{"PAID"===(await A(F.value)).payStatus&&(location.reload(),clearInterval(G.value))}),2e3))}else k({type:"success",message:_("tool.sS")}),setTimeout((()=>{location.href=t}),1e3)}catch(i){D.value=!1}}else o&&"zh-CN"!=o?E.push("/login"):window.addLoginDom()};return l((()=>{window.removeEventListener("resize",X)})),c((()=>{"写作"==R.appType&&localStorage.setItem("appWrite-"+R.appUuid,JSON.stringify({appUuid:R.appUuid,directoryMd:R.directoryMd})),X(),window.addEventListener("resize",X),(async()=>{const e=await T("homePayImg");j.value=e.list[0].value})(),O.value&&1==R.feeTypes.length&&(R.feeTypes[0].feePrice>0&&0==H.value&&Y(R.feeTypes[0],0),R.feeTypes[0].feePrice>0&&0!=H.value&&Y(R.feeTypes[0],0),0==R.feeTypes[0].feePrice&&Y(R.feeTypes[0],0))})),(e,n)=>{var r,a,s,l,c,k,x,I,T,A;const B=S,_=P;return t(),o("div",wt,[u("div",bt,[u("div",Ct,[u("div",kt,[u("div",xt,[u("img",{src:d(q),onError:W,alt:""},null,40,It),u("span",Tt,h((null==(r=d(M))?void 0:r.realName)?null==(a=d(M))?void 0:a.realName:null==(s=d(M))?void 0:s.userName),1)])]),u("div",{class:"micro_right"},[u("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:Q})])]),u("div",St,[u("div",Pt,[u("div",At,[u("div",Bt,[0!=d(H)||d(R).feeTypes.length>1?(t(),o("div",{key:0,class:"swiper-vip",showIndicator:!1,autoPlay:!1,style:p({transform:`translate(${e.translateVipVal}px)`})},[(t(!0),o(f,null,g(d(R).feeTypes,((i,n)=>(t(),o("div",{class:"swiper-vip-item",key:n,onClick:e=>Y(i,n)},[u("div",{class:"newer",style:p({left:n%4==0&&0!=n?"6px":"-1px"})},null,4),u("div",{class:m(["swiper-vip-item-child",{sactvie:d($)==n}])},[u("div",Dt,h(e.$t(`tool.${i.type}`)),1),u("div",Et,[u("span",null,h("人民币"==i.coinType?"¥":"$"),1),v(" "+h(i.feePrice),1)])],2)],8,_t)))),128))],4)):i("",!0)])])]),u("div",Ot,[u("div",Lt,[u("div",Mt,[n[2]||(n[2]=u("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"},null,-1)),v(h(d(R).appName),1)]),u("div",Rt,h(d(R).appDescription),1)])]),(null==(l=d(N))?void 0:l.coinType)&&0!=d(N).feePrice&&0==d(H)?(t(),o("div",Nt,[u("div",$t,[u("div",zt,[y(u("div",Ut,null,512),[[_,d(D)],[w,d(D)]]),y(b(yt,{ref:"qrcode",class:"qr-code",id:"qrcode",correctLevel:3,autoColor:!1,colorDark:"#000000",text:d(U),size:95,margin:0,logoMargin:3},null,8,["text"]),[[w,!d(D)]]),u("div",jt,[u("div",Ft,[n[3]||(n[3]=u("div",{class:"box"},[u("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})],-1)),u("span",null,h(e.$t("tool.Support_Alipay_Payment")),1)]),u("span",Gt,[v(h(e.$t("tool.Support_Alipay_Payment")),1),u("span",Ht,h(null==(c=d(N))?void 0:c.feePrice),1),v(h("人民币"==(null==(k=d(N))?void 0:k.coinType)?"¥":"$")+"/"+h(3==(null==(x=d(N))?void 0:x.monthNum)?e.$t("tool.Quarter"):12==(null==(I=d(N))?void 0:I.monthNum)?e.$t("tool.Year"):e.$t("tool.Month")),1)]),u("span",qt,h(e.$t("tool.Meisi_Account"))+"："+h(d(M).userName),1),u("span",{class:"t3",onClick:V},[v(h(e.$t("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"))+" ",1),u("img",{onClick:V,src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})])])])])])):i("",!0),(null==(T=d(N))?void 0:T.coinType)&&0==d(N).feePrice?(t(),o("div",Wt,[b(B,{type:"primary",onClick:n[0]||(n[0]=e=>J(d(N),d(R).appUuid))},{default:C((()=>[v(h(e.$t("tool.Free_Trial")),1)])),_:1})])):i("",!0),(null==(A=d(N))?void 0:A.coinType)&&d(N).feePrice>0&&0!=d(H)?(t(),o("div",Vt,[b(B,{type:"primary",onClick:n[1]||(n[1]=e=>J(d(N),d(R).appUuid))},{default:C((()=>[v(h(e.$t("market.subscribe")),1)])),_:1})])):i("",!0)])])])}}},[["__scopeId","data-v-138e2384"]]);function Qt(){}const Yt=Object.assign,Jt="undefined"!=typeof window,Kt=e=>null!==e&&"object"==typeof e,Zt=e=>null!=e,eo=e=>"function"==typeof e,to=e=>"number"==typeof e||/^\d+(\.\d+)?$/.test(e);function oo(e,t){const o=t.split(".");let i=e;return o.forEach((e=>{var t;i=Kt(i)&&null!=(t=i[e])?t:""})),i}function io(e,t,o){return t.reduce(((t,i)=>(o&&void 0===e[i]||(t[i]=e[i]),t)),{})}const no=null,ro=[Number,String],ao={type:Boolean,default:!0},so=e=>({type:e,required:!0}),lo=e=>({type:ro,default:e}),co=e=>({type:String,default:e});var uo="undefined"!=typeof window;function ho(e){return uo?requestAnimationFrame(e):-1}function po(e){uo&&cancelAnimationFrame(e)}function fo(e){ho((()=>ho(e)))}var go=(e,t)=>({top:0,left:0,right:e,bottom:t,width:e,height:t}),mo=e=>{const t=d(e);if(t===window){const e=t.innerWidth,o=t.innerHeight;return go(e,o)}return(null==t?void 0:t.getBoundingClientRect)?t.getBoundingClientRect():go(0,0)};function vo(e){const t=B(e,null);if(t){const e=_(),{link:o,unlink:i,internalChildren:n}=t;o(e),l((()=>i(e)));return{parent:t,index:D((()=>n.indexOf(e)))}}return{parent:null,index:r(-1)}}var yo,wo,bo=(e,t)=>{const o=e.indexOf(t);return-1===o?e.findIndex((e=>void 0!==t.key&&null!==t.key&&e.type===t.type&&e.key===t.key)):o};function Co(e,t,o){const i=function(e){const t=[],o=e=>{Array.isArray(e)&&e.forEach((e=>{var i;z(e)&&(t.push(e),(null==(i=e.component)?void 0:i.subTree)&&(t.push(e.component.subTree),o(e.component.subTree.children)),e.children&&o(e.children))}))};return o(e),t}(e.subTree.children);o.sort(((e,t)=>bo(i,e.vnode)-bo(i,t.vnode)));const n=o.map((e=>e.proxy));t.sort(((e,t)=>n.indexOf(e)-n.indexOf(t)))}function ko(e){const t=E([]),o=E([]),i=_();return{children:t,linkChildren:n=>{$(e,Object.assign({link:e=>{e.proxy&&(o.push(e),t.push(e.proxy),Co(i,t,o))},unlink:e=>{const i=o.indexOf(e);t.splice(i,1),o.splice(i,1)},children:t,internalChildren:o},n))}}}function xo(e){let t;c((()=>{e(),O((()=>{t=!0}))})),L((()=>{t&&e()}))}function Io(e,t,o={}){if(!uo)return;const{target:i=window,passive:n=!1,capture:r=!1}=o;let a,s=!1;const c=o=>{if(s)return;const i=d(o);i&&!a&&(i.addEventListener(e,t,{capture:r,passive:n}),a=!0)},u=o=>{if(s)return;const i=d(o);i&&a&&(i.removeEventListener(e,t,r),a=!1)};let h;return l((()=>u(i))),M((()=>u(i))),xo((()=>c(i))),R(i)&&(h=N(i,((e,t)=>{u(t),c(e)}))),()=>{null==h||h(),u(i),s=!0}}var To,So=/scroll|auto|overlay/i,Po=uo?window:void 0;function Ao(e){return"HTML"!==e.tagName&&"BODY"!==e.tagName&&1===e.nodeType}function Bo(e,t=Po){let o=e;for(;o&&o!==t&&Ao(o);){const{overflowY:e}=window.getComputedStyle(o);if(So.test(e))return o;o=o.parentNode}return t}function _o(e,t=Po){const o=r();return c((()=>{e.value&&(o.value=Bo(e.value,t))})),o}function Do(){if(!To&&(To=r("visible"),uo)){const e=()=>{To.value=document.hidden?"hidden":"visible"};e(),window.addEventListener("visibilitychange",e)}return To}var Eo=Symbol("van-field");function Oo(e){const t="scrollTop"in e?e.scrollTop:e.pageYOffset;return Math.max(t,0)}function Lo(e,t){"scrollTop"in e?e.scrollTop=t:e.scrollTo(e.scrollX,t)}function Mo(e){Lo(window,e),Lo(document.body,e)}function Ro(e,t){if(e===window)return 0;const o=t?Oo(t):window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;return mo(e).top+o}Jt&&/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase());function No(e,t){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault(),t&&(e=>{e.stopPropagation()})(e)}function $o(e){const t=d(e);if(!t)return!1;const o=window.getComputedStyle(t),i="none"===o.display,n=null===t.offsetParent&&"fixed"!==o.position;return i||n}const{width:zo,height:Uo}=function(){if(!yo&&(yo=r(0),wo=r(0),uo)){const e=()=>{yo.value=window.innerWidth,wo.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:yo,height:wo}}();function jo(e){if(Zt(e))return to(e)?`${e}px`:String(e)}function Fo(e){const t={};return void 0!==e&&(t.zIndex=+e),t}let Go;function Ho(e){return+(e=e.replace(/rem/g,""))*function(){if(!Go){const e=document.documentElement,t=e.style.fontSize||window.getComputedStyle(e).fontSize;Go=parseFloat(t)}return Go}()}function qo(e){if("number"==typeof e)return e;if(Jt){if(e.includes("rem"))return Ho(e);if(e.includes("vw"))return function(e){return+(e=e.replace(/vw/g,""))*zo.value/100}(e);if(e.includes("vh"))return function(e){return+(e=e.replace(/vh/g,""))*Uo.value/100}(e)}return parseFloat(e)}const Wo=/-(\w)/g,Vo=e=>e.replace(Wo,((e,t)=>t.toUpperCase())),Xo=(e,t,o)=>Math.min(Math.max(e,t),o),{hasOwnProperty:Qo}=Object.prototype;function Yo(e,t){return Object.keys(t).forEach((o=>{!function(e,t,o){const i=t[o];Zt(i)&&(Qo.call(e,o)&&Kt(i)?e[o]=Yo(Object(e[o]),i):e[o]=i)}(e,t,o)})),e}const Jo=r("zh-CN"),Ko=E({"zh-CN":{name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,t)=>`${e}年${t}月`,rangePrompt:e=>`最多选择 ${e} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>`${e}折`,condition:e=>`满${e}元可用`},vanCouponCell:{title:"优惠券",count:e=>`${e}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}}});var Zo={messages:()=>Ko[Jo.value],use(e,t){Jo.value=e,this.add({[e]:t})},add(e={}){Yo(Ko,e)}};function ei(e){const t=Vo(e)+".";return(e,...o)=>{const i=Zo.messages(),n=oo(i,t+e)||oo(i,e);return eo(n)?n(...o):n}}function ti(e,t){return t?"string"==typeof t?` ${e}--${t}`:Array.isArray(t)?t.reduce(((t,o)=>t+ti(e,o)),""):Object.keys(t).reduce(((o,i)=>o+(t[i]?ti(e,i):"")),""):""}function oi(e){return(t,o)=>(t&&"string"!=typeof t&&(o=t,t=""),`${t=t?`${e}__${t}`:e}${ti(t,o)}`)}function ii(e){const t=`van-${e}`;return[t,oi(t),ei(t)]}const ni="van-hairline",ri=`${ni}--top`,ai=`${ni}--left`,si=`${ni}--surround`,li=`${ni}--top-bottom`;function ci(e,{args:t=[],done:o,canceled:i,error:n}){if(e){const a=e.apply(null,t);Kt(r=a)&&eo(r.then)&&eo(r.catch)?a.then((e=>{e?o():i&&i()})).catch(n||Qt):a?o():i&&i()}else o();var r}function ui(e){return e.install=t=>{const{name:o}=e;o&&(t.component(o,e),t.component(Vo(`-${o}`),e))},e}const di=Symbol();function hi(e){const t=B(di,null);t&&N(t,(t=>{t&&e()}))}function pi(e){const t=_();t&&Yt(t.proxy,e)}const[fi,gi]=ii("badge");const mi=ui(U({name:fi,props:{dot:Boolean,max:ro,tag:co("div"),color:String,offset:Array,content:ro,showZero:ao,position:co("top-right")},setup(e,{slots:t}){const o=()=>{if(t.content)return!0;const{content:o,showZero:i}=e;return Zt(o)&&""!==o&&(i||0!==o&&"0"!==o)},i=()=>{const{dot:i,max:n,content:r}=e;if(!i&&o())return t.content?t.content():Zt(n)&&to(r)&&+r>+n?`${n}+`:r},n=e=>e.startsWith("-")?e.replace("-",""):`-${e}`,r=D((()=>{const o={background:e.color};if(e.offset){const[i,r]=e.offset,{position:a}=e,[s,l]=a.split("-");t.default?(o[s]="number"==typeof r?jo("top"===s?r:-r):"top"===s?jo(r):n(r),o[l]="number"==typeof i?jo("left"===l?i:-i):"left"===l?jo(i):n(i)):(o.marginTop=jo(r),o.marginLeft=jo(i))}return o})),a=()=>{if(o()||e.dot)return b("div",{class:gi([e.position,{dot:e.dot,fixed:!!t.default}]),style:r.value},[i()])};return()=>{if(t.default){const{tag:o}=e;return b(o,{class:gi("wrapper")},{default:()=>[t.default(),a()]})}return a()}}}));let vi=2e3;const[yi,wi]=ii("config-provider"),bi=Symbol(yi),[Ci,ki]=ii("icon");const xi=ui(U({name:Ci,props:{dot:Boolean,tag:co("i"),name:String,size:ro,badge:ro,color:String,badgeProps:Object,classPrefix:String},setup(e,{slots:t}){const o=B(bi,null),i=D((()=>e.classPrefix||(null==o?void 0:o.iconPrefix)||ki()));return()=>{const{tag:o,dot:n,name:r,size:a,badge:s,color:l}=e,c=(e=>null==e?void 0:e.includes("/"))(r);return b(mi,j({dot:n,tag:o,class:[i.value,c?"":`${i.value}-${r}`],style:{color:l,fontSize:jo(a)},content:s},e.badgeProps),{default:()=>{var e;return[null==(e=t.default)?void 0:e.call(t),c&&b("img",{class:ki("image"),src:r},null)]}})}}})),[Ii,Ti]=ii("loading"),Si=Array(12).fill(null).map(((e,t)=>b("i",{class:Ti("line",String(t+1))},null))),Pi=b("svg",{class:Ti("circular"),viewBox:"25 25 50 50"},[b("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]);const Ai=ui(U({name:Ii,props:{size:ro,type:co("circular"),color:String,vertical:Boolean,textSize:ro,textColor:String},setup(e,{slots:t}){const o=D((()=>Yt({color:e.color},function(e){if(Zt(e)){if(Array.isArray(e))return{width:jo(e[0]),height:jo(e[1])};const t=jo(e);return{width:t,height:t}}}(e.size)))),i=()=>{const i="spinner"===e.type?Si:Pi;return b("span",{class:Ti("spinner",e.type),style:o.value},[t.icon?t.icon():i])},n=()=>{var o;if(t.default)return b("span",{class:Ti("text"),style:{fontSize:jo(e.textSize),color:null!=(o=e.textColor)?o:e.color}},[t.default()])};return()=>{const{type:t,vertical:o}=e;return b("div",{class:Ti([t,{vertical:o}]),"aria-live":"polite","aria-busy":!0},[i(),n()])}}})),Bi={show:Boolean,zIndex:ro,overlay:ao,duration:ro,teleport:[String,Object],lockScroll:ao,lazyRender:ao,beforeClose:Function,overlayStyle:Object,overlayClass:no,transitionAppear:Boolean,closeOnClickOverlay:ao},_i=Object.keys(Bi);function Di(){const e=r(0),t=r(0),o=r(0),i=r(0),n=r(0),a=r(0),s=r(""),l=r(!0),c=()=>{o.value=0,i.value=0,n.value=0,a.value=0,s.value="",l.value=!0};return{move:r=>{const c=r.touches[0];o.value=(c.clientX<0?0:c.clientX)-e.value,i.value=c.clientY-t.value,n.value=Math.abs(o.value),a.value=Math.abs(i.value);var u,d;(!s.value||n.value<10&&a.value<10)&&(s.value=(u=n.value,d=a.value,u>d?"horizontal":d>u?"vertical":"")),l.value&&(n.value>5||a.value>5)&&(l.value=!1)},start:o=>{c(),e.value=o.touches[0].clientX,t.value=o.touches[0].clientY},reset:c,startX:e,startY:t,deltaX:o,deltaY:i,offsetX:n,offsetY:a,direction:s,isVertical:()=>"vertical"===s.value,isHorizontal:()=>"horizontal"===s.value,isTap:l}}let Ei=0;const Oi="van-overflow-hidden";function Li(e){const t=r(!1);return N(e,(e=>{e&&(t.value=e)}),{immediate:!0}),e=>()=>t.value?e():null}const Mi=()=>{var e;const{scopeId:t}=(null==(e=_())?void 0:e.vnode)||{};return t?{[t]:""}:null},[Ri,Ni]=ii("overlay");const $i=ui(U({name:Ri,props:{show:Boolean,zIndex:ro,duration:ro,className:no,lockScroll:ao,lazyRender:ao,customStyle:Object,teleport:[String,Object]},setup(e,{slots:t}){const o=r(),i=Li((()=>e.show||!e.lazyRender))((()=>{var i;const n=Yt(Fo(e.zIndex),e.customStyle);return Zt(e.duration)&&(n.animationDuration=`${e.duration}s`),y(b("div",{ref:o,style:n,class:[Ni(),e.className]},[null==(i=t.default)?void 0:i.call(t)]),[[w,e.show]])}));return Io("touchmove",(t=>{e.lockScroll&&No(t,!0)}),{target:o}),()=>{const t=b(F,{name:"van-fade",appear:!0},{default:i});return e.teleport?b(G,{to:e.teleport},{default:()=>[t]}):t}}})),zi=Yt({},Bi,{round:Boolean,position:co("center"),closeIcon:co("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:co("top-right"),destroyOnClose:Boolean,safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[Ui,ji]=ii("popup");const Fi=ui(U({name:Ui,inheritAttrs:!1,props:zi,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:t,attrs:o,slots:i}){let n,a;const l=r(),u=r(),d=Li((()=>e.show||!e.lazyRender)),h=D((()=>{const t={zIndex:l.value};if(Zt(e.duration)){t["center"===e.position?"animationDuration":"transitionDuration"]=`${e.duration}s`}return t})),p=()=>{n||(n=!0,l.value=void 0!==e.zIndex?+e.zIndex:++vi,t("open"))},g=()=>{n&&ci(e.beforeClose,{done(){n=!1,t("close"),t("update:show",!1)}})},m=o=>{t("clickOverlay",o),e.closeOnClickOverlay&&g()},v=()=>{if(e.overlay)return b($i,j({show:e.show,class:e.overlayClass,zIndex:l.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0},Mi(),{onClick:m}),{default:i["overlay-content"]})},C=e=>{t("clickCloseIcon",e),g()},k=()=>{if(e.closeable)return b(xi,{role:"button",tabindex:0,name:e.closeIcon,class:[ji("close-icon",e.closeIconPosition),"van-haptics-feedback"],classPrefix:e.iconPrefix,onClick:C},null)};let x;const I=()=>{x&&clearTimeout(x),x=setTimeout((()=>{t("opened")}))},T=()=>t("closed"),S=e=>t("keydown",e),P=d((()=>{var t;const{destroyOnClose:n,round:r,position:a,safeAreaInsetTop:s,safeAreaInsetBottom:l,show:c}=e;if(c||!n)return y(b("div",j({ref:u,style:h.value,role:"dialog",tabindex:0,class:[ji({round:r,[a]:a}),{"van-safe-area-top":s,"van-safe-area-bottom":l}],onKeydown:S},o,Mi()),[null==(t=i.default)?void 0:t.call(i),k()]),[[w,c]])})),A=()=>{const{position:t,transition:o,transitionAppear:i}=e;return b(F,{name:o||("center"===t?"van-fade":`van-popup-slide-${t}`),appear:i,onAfterEnter:I,onAfterLeave:T},{default:P})};return N((()=>e.show),(e=>{e&&!n&&(p(),0===o.tabindex&&O((()=>{var e;null==(e=u.value)||e.focus()}))),!e&&n&&(n=!1,t("close"))})),pi({popupRef:u}),function(e,t){const o=Di(),i=t=>{o.move(t);const i=o.deltaY.value>0?"10":"01",n=Bo(t.target,e.value),{scrollHeight:r,offsetHeight:a,scrollTop:s}=n;let l="11";0===s?l=a>=r?"00":"01":s+a>=r&&(l="10"),"11"===l||!o.isVertical()||parseInt(l,2)&parseInt(i,2)||No(t,!0)},n=()=>{document.addEventListener("touchstart",o.start),document.addEventListener("touchmove",i,{passive:!1}),Ei||document.body.classList.add(Oi),Ei++},r=()=>{Ei&&(document.removeEventListener("touchstart",o.start),document.removeEventListener("touchmove",i),Ei--,Ei||document.body.classList.remove(Oi))},a=()=>t()&&r();xo((()=>t()&&n())),M(a),s(a),N(t,(e=>{e?n():r()}))}(u,(()=>e.show&&e.lockScroll)),Io("popstate",(()=>{e.closeOnPopstate&&(g(),a=!1)})),c((()=>{e.show&&p()})),L((()=>{a&&(t("update:show",!0),a=!1)})),M((()=>{e.show&&e.teleport&&(g(),a=!0)})),$(di,(()=>e.show)),()=>e.teleport?b(G,{to:e.teleport},{default:()=>[v(),A()]}):b(f,null,[v(),A()])}}));let Gi=0;const[Hi,qi]=ii("toast"),Wi=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"];var Vi,Xi=U({name:Hi,props:{icon:String,show:Boolean,type:co("text"),overlay:Boolean,message:ro,iconSize:ro,duration:(Vi=2e3,{type:Number,default:Vi}),position:co("middle"),teleport:[String,Object],wordBreak:String,className:no,iconPrefix:String,transition:co("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:no,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:ro},emits:["update:show"],setup(e,{emit:t,slots:o}){let i,n=!1;const r=()=>{const t=e.show&&e.forbidClick;n!==t&&(n=t,n?(Gi||document.body.classList.add("van-toast--unclickable"),Gi++):Gi&&(Gi--,Gi||document.body.classList.remove("van-toast--unclickable")))},a=e=>t("update:show",e),s=()=>{e.closeOnClick&&a(!1)},u=()=>clearTimeout(i),d=()=>{const{icon:t,type:o,iconSize:i,iconPrefix:n,loadingType:r}=e;return t||"success"===o||"fail"===o?b(xi,{name:t||o,size:i,class:qi("icon"),classPrefix:n},null):"loading"===o?b(Ai,{class:qi("loading"),size:i,type:r},null):void 0},h=()=>{const{type:t,message:i}=e;return o.message?b("div",{class:qi("text")},[o.message()]):Zt(i)&&""!==i?"html"===t?b("div",{key:0,class:qi("text"),innerHTML:String(i)},null):b("div",{class:qi("text")},[i]):void 0};return N((()=>[e.show,e.forbidClick]),r),N((()=>[e.show,e.type,e.message,e.duration]),(()=>{u(),e.show&&e.duration>0&&(i=setTimeout((()=>{a(!1)}),e.duration))})),c(r),l(r),()=>b(Fi,j({class:[qi([e.position,"normal"===e.wordBreak?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:s,onClosed:u,"onUpdate:show":a},io(e,Wi)),{default:()=>[d(),h()]})}});const Qi=ui(Xi),[Yi,Ji]=ii("checkbox-group"),Ki=Symbol(Yi),Zi={name:no,disabled:Boolean,iconSize:ro,modelValue:no,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var en=U({props:Yt({},Zi,{bem:so(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:ao,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(e,{emit:t,slots:o}){const i=r(),n=t=>{if(e.parent&&e.bindGroup)return e.parent.props[t]},a=D((()=>{if(e.parent&&e.bindGroup){const t=n("disabled")||e.disabled;if("checkbox"===e.role){const o=n("modelValue").length,i=n("max");return t||i&&o>=+i&&!e.checked}return t}return e.disabled})),s=D((()=>n("direction"))),l=D((()=>{const t=e.checkedColor||n("checkedColor");if(t&&e.checked&&!a.value)return{borderColor:t,backgroundColor:t}})),c=D((()=>e.shape||n("shape")||"round")),u=o=>{const{target:n}=o,r=i.value,s=r===n||(null==r?void 0:r.contains(n));a.value||!s&&e.labelDisabled||t("toggle"),t("click",o)},d=()=>{var t,r;const{bem:s,checked:u,indeterminate:d}=e,h=e.iconSize||n("iconSize");return b("div",{ref:i,class:s("icon",[c.value,{disabled:a.value,checked:u,indeterminate:d}]),style:"dot"!==c.value?{fontSize:jo(h)}:{width:jo(h),height:jo(h),borderColor:null==(t=l.value)?void 0:t.borderColor}},[o.icon?o.icon({checked:u,disabled:a.value}):"dot"!==c.value?b(xi,{name:d?"minus":"success",style:l.value},null):b("div",{class:s("icon--dot__icon"),style:{backgroundColor:null==(r=l.value)?void 0:r.backgroundColor}},null)])},h=()=>{const{checked:t}=e;if(o.default)return b("span",{class:e.bem("label",[e.labelPosition,{disabled:a.value}])},[o.default({checked:t,disabled:a.value})])};return()=>{const t="left"===e.labelPosition?[h(),d()]:[d(),h()];return b("div",{role:e.role,class:e.bem([{disabled:a.value,"label-disabled":e.labelDisabled},s.value]),tabindex:a.value?void 0:0,"aria-checked":e.checked,onClick:u},[t])}}});const[tn,on]=ii("checkbox");const nn=ui(U({name:tn,props:Yt({},Zi,{shape:String,bindGroup:ao,indeterminate:{type:Boolean,default:null}}),emits:["change","update:modelValue"],setup(e,{emit:t,slots:o}){const{parent:i}=vo(Ki),n=D((()=>i&&e.bindGroup?-1!==i.props.modelValue.indexOf(e.name):!!e.modelValue)),r=(o=!n.value)=>{i&&e.bindGroup?(t=>{const{name:o}=e,{max:n,modelValue:r}=i.props,a=r.slice();if(t)n&&a.length>=+n||a.includes(o)||(a.push(o),e.bindGroup&&i.updateValue(a));else{const t=a.indexOf(o);-1!==t&&(a.splice(t,1),e.bindGroup&&i.updateValue(a))}})(o):t("update:modelValue",o),null!==e.indeterminate&&t("change",o)};return N((()=>e.modelValue),(o=>{null===e.indeterminate&&t("change",o)})),pi({toggle:r,props:e,checked:n}),function(e){const t=B(Eo,null);t&&!t.customValue.value&&(t.customValue.value=e,N(e,(()=>{t.resetValidation(),t.validateWithTrigger("onChange")})))}((()=>e.modelValue)),()=>b(en,j({bem:on,role:"checkbox",parent:i,checked:n.value,onToggle:r},e),io(o,["default","icon"]))}})),rn={key:0,class:"vip-head"},an={class:"vip-introduce"},sn={class:"box"},ln={key:0,class:"box-left-1"},cn={class:"left2"},un={key:1,class:"box-left"},dn=["src"],hn={class:"box-word"},pn={class:"t1"},fn={class:"vip-main"},gn={class:"vip-one"},mn={class:"big"},vn={ref:"scroll"},yn=["onClick"],wn={class:"title ellipsis-2-lines"},bn={class:"price"},Cn={key:0,class:"isfava"},kn={class:"vip-two"},xn={class:"vip-two_banner"},In={class:"vip-two_title"},Tn={class:"vip-two_content"},Sn={key:0,class:"vip-three"},Pn={key:1,class:"vip-pay btns"},An={key:0,class:"pay-left"},Bn={class:"t1"};const _n=e({name:"Vip",data:()=>({isCheckW:!0,isCheckZ:!1,isLogin:!1,activeItem:{},appId:"wx9096048917ec59ab",appOrderId:"",isClick:!1,openId:"",isWx:!1,choseUserVip:{},isFromMedsci:!1,showAll:!1,checkCount:0,vipTypeList:[],activeType:0,active:0,radio:"",isShaking:!1,avatar:"",socialType:localStorage.getItem("socialType")}),components:{VanCheckbox:nn},props:{userInfo:{type:Object,default:()=>({})},currentItem:{type:Object,default:()=>({})}},created(){},mounted(){var e,t;const o=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${o}px`),"写作"==this.currentItem.appType&&localStorage.setItem("appWrite-"+this.currentItem.appUuid,JSON.stringify({appUuid:this.currentItem.appUuid,directoryMd:this.currentItem.directoryMd})),this.avatar=(null==(e=this.userInfo)?void 0:e.avatar)?null==(t=this.userInfo)?void 0:t.avatar:"https://img.medsci.cn/web/img/user_icon.png",this.isUp=(null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn")),1==this.currentItem.feeTypes.length&&(this.currentItem.feeTypes[0].feePrice>0&&0==localStorage.getItem("socialType")&&this.CheckItem(this.currentItem.feeTypes[0],this.currentItem.appUuid),0==this.currentItem.feeTypes[0].feePrice&&this.CheckItem(this.currentItem.feeTypes[0],this.currentItem.appUuid)),H.get("userInfo")&&JSON.parse(H.get("userInfo")).userId&&(this.isLogin=!0,this.initUser()),this.init(),this.$route.query.source&&"medsci"==this.$route.query.source&&(this.isFromMedsci=!0)},methods:{changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},async subscribe(e,t,o){var i;if(!this.radio&&o)return this.isShaking=!0,void setTimeout((()=>{this.isShaking=!1}),500);let n=await x();if(null==(i=this.userInfo)?void 0:i.userId){const o={appUuid:t,priceId:e.priceId,monthNum:e.monthNum};try{let t=await I(o);if(t)if(0==localStorage.getItem("socialType")&&0!=e.feePrice){let e=await q(JSON.parse(t));k({type:"success",message:this.$t("tool.sS")}),setTimeout((()=>{location.href=e}),1e3)}else k({type:"success",message:this.$t("tool.sS")}),setTimeout((()=>{location.href=t}),1e3)}catch(r){}}else n&&"zh-CN"!=n?this.$router.push((this.isUp,"/login")):window.addLoginDom()},CheckItem(e,t){this.activeItem=e,this.active=t},openActivity(e){e&&(window.location.href=e)},login(){addLoginDom()},initUser(){},init(){},isMedSci:()=>navigator.userAgent.includes("medsci_app"),goBack(){window.history.back(-1)},checkFn1(){this.isCheckW=!0,this.isCheckZ=!1},checkFn2(){this.isCheckW=!1,this.isCheckZ=!0},goAgreent(){const e="https://portal-test.medon.com.cn/agreement/27";this.isMedSci()?window.location.href=e:window.open(e)},createOrder(){this.isWx&&(this.isCheckW=!0,this.isCheckZ=!1);const e={accessAppId:"college",appOrderId:this.appOrderId,payChannel:this.isCheckW?"WX":"ALI",paySource:"MEDSCI_WEB",payType:this.isWx?"JSAPI":"MWEB"};this.$axios.post(api.payBuild,e).then((e=>{this.orderList(e.data.payOrderId)}))},orderList(e){const t={};this.$route.query.from&&(t.from="app"),this.isFromMedsci&&(t.sourcefrom="main",t.redirectUrl=this.$route.query.redirectUrl);const o={accessAppId:"college",openId:this.isWx?this.openId:"",payOrderId:e,extParam:JSON.stringify(t)};this.$axios.post(api.payOrder,o).then((e=>{if(this.isCheckW){if(this.isWx)return void this.wxOrder(e.data.wechatJsapi);window.location.href=e.data.wechatH5.h5Url}else if(e.data.aliH5.html){const t=document.createElement("div");t.innerHTML=e.data.aliH5.html,document.body.appendChild(t),document.forms[0].submit()}}))},wxOrder(e){WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:e.appId,timeStamp:e.timeStamp,nonceStr:e.nonceStr,package:e.packageStr,signType:e.signType,paySign:e.paySign},(function(e){"get_brand_wcpay_request:ok"==e.err_msg?(Qi.success("支付成功！"),setTimeout((()=>{window.location.reload()}),1e3)):e.err_msg}))}}},[["render",function(e,n,r,a,s,l){const c=W("van-checkbox"),d=S;return t(),o("div",{class:m(["vip",{sp:l.isMedSci()}])},[l.isMedSci()?i("",!0):(t(),o("div",rn,h(r.currentItem.appName),1)),u("div",an,[n[10]||(n[10]=u("img",{class:"crown",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""},null,-1)),u("div",sn,[s.isLogin?(t(),o("div",un,[u("img",{class:"avatar",src:s.avatar,alt:"",onError:n[1]||(n[1]=(...e)=>l.changeImg&&l.changeImg(...e))},null,40,dn),u("div",hn,[u("span",pn,h(r.userInfo.realName||r.userInfo.userName),1)])])):(t(),o("div",ln,[n[9]||(n[9]=u("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:""},null,-1)),u("div",cn,[u("span",{class:"t1",style:{cursor:"pointer"},onClick:n[0]||(n[0]=(...e)=>l.login&&l.login(...e))},"立即登录"),n[8]||(n[8]=u("span",{class:"t2"},"请登录后购买",-1))])]))])]),u("div",fn,[u("div",gn,[u("div",mn,[u("ul",vn,[(t(!0),o(f,null,g(r.currentItem.feeTypes,((n,r)=>(t(),o("li",{key:r,class:m({sactvie:n.type==s.activeItem.type}),onClick:e=>l.CheckItem(n)},[u("div",wn,h(e.$t(`tool.${n.type}`)),1),u("div",bn,[u("span",null,h("人民币"==n.coinType?"¥":"$"),1),v(h(n.feePrice),1)]),n.originalPrice?(t(),o("div",Cn,h("人民币"==n.coinType?"¥":"$")+h(n.feePrice),1)):i("",!0)],10,yn)))),128))],512)])]),u("div",kn,[u("div",xn,[u("div",In,[n[11]||(n[11]=u("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"},null,-1)),v(h(r.currentItem.appName),1)]),u("div",Tn,h(r.currentItem.appDescription),1)])]),s.activeItem.feePrice>0&&0==s.socialType?(t(),o("div",Sn,[u("div",{class:m(["pay",{isWx:s.isWx}])},[n[13]||(n[13]=u("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:""},null,-1)),u("div",{class:"item",onClick:n[2]||(n[2]=(...e)=>l.checkFn2&&l.checkFn2(...e))},n[12]||(n[12]=[u("div",{class:"item-left"},[u("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""}),u("span",null,"支付宝支付")],-1),u("div",{class:"item-right isCheck"},[u("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:""})],-1)]))],2)])):i("",!0)]),s.activeItem.feePrice>=0?(t(),o("div",Pn,[0!=s.activeItem.feePrice&&0==s.socialType?(t(),o("div",An,[u("div",Bn,h(r.currentItem.appName),1),u("div",{class:m(["t2",{shake:s.isShaking}])},[b(c,{modelValue:s.radio,"onUpdate:modelValue":n[3]||(n[3]=e=>s.radio=e)},null,8,["modelValue"]),u("span",{onClick:n[4]||(n[4]=(...e)=>l.goAgreent&&l.goAgreent(...e))},n[14]||(n[14]=[v("请在阅读并同意"),u("span",null,"协议",-1),v("后开通")]))],2)])):i("",!0),0!=s.activeItem.feePrice&&0==s.socialType?(t(),o("div",{key:1,class:"pay-right",onClick:n[5]||(n[5]=e=>l.subscribe(s.activeItem,r.currentItem.appUuid,"ali"))},[u("span",null,h(s.activeItem.feePrice)+"元确认协议并支付",1)])):i("",!0),0==s.activeItem.feePrice?(t(),V(d,{key:2,onClick:n[6]||(n[6]=e=>l.subscribe(s.activeItem,r.currentItem.appUuid)),type:"primary"},{default:C((()=>[v(h(e.$t("tool.Free_Trial")),1)])),_:1})):i("",!0),s.activeItem.feePrice>0&&0!=s.socialType?(t(),V(d,{key:3,onClick:n[7]||(n[7]=e=>l.subscribe(s.activeItem,r.currentItem.appUuid)),type:"primary"},{default:C((()=>[v(h(e.$t("market.subscribe")),1)])),_:1})):i("",!0)])):i("",!0)],2)}],["__scopeId","data-v-eda9c544"]]);export{ri as $,fo as A,No as B,Xo as C,so as D,jo as E,Zt as F,li as G,Mo as H,Ro as I,ci as J,mi as K,vo as L,no as M,io as N,Xt as O,_n as P,Fi as Q,si as R,xi as S,Ai as T,Kt as U,Xi as V,Bi as W,_i as X,Qt as Y,eo as Z,ai as _,ii as a,lo as b,po as c,qo as d,Yt as e,Fo as f,Oo as g,Io as h,Jt as i,$o as j,mo as k,Uo as l,co as m,ro as n,xo as o,ui as p,Di as q,ho as r,Lo as s,ao as t,_o as u,ko as v,zo as w,pi as x,Do as y,hi as z};
