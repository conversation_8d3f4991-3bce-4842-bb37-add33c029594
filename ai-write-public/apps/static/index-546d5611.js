/* empty css                  *//* empty css                  */import{a as e,u as t,f as s,t as a}from"./lang-082020ba.js";import{i}from"./index-5adb8a16.js";import{z as o,w as l,l as n,k as r,a as c,v as d,t as A,p as g,e as p,m,n as u,R as h,S as f,B as y,T as w,L as v,x as b,i as C,U as S,V as B,W as x,M as k,X as I,Y as U,N as T,E as D,Q as N,J as E,Z as R,_ as Y,$ as M,O as L,P as F}from"./index-24cc8b98.js";import{c as Q}from"./index-d6f7034f.js";import{r as V,o as q,P as W,w as G,f as K,M as O,O as J,ad as P,N as z,I as H,X as Z,ae as X,a2 as j,x as _,af as $,ag as ee,_ as te,u as se,$ as ae,a0 as ie,a8 as oe,A as le,ah as ne,s as re,a1 as ce,a5 as de,aa as Ae,a3 as ge,ai as pe,aj as me,a9 as ue,a6 as he,a as fe,Z as ye,l as we,c as ve,d as be,e as Ce,h as Se,i as Be,m as xe,q as ke,j as Ie,t as Ue,F as Te,y as De,S as Ne,k as Ee,ak as Re,V as Ye,al as Me,v as Le,E as Fe,G as Qe,H as Ve,am as qe,n as We}from"./index-15a3899c.js";/* empty css                   */function Ge(e,t){const s=((e,t)=>{const s=V(),a=()=>{s.value=r(e).height};return q((()=>{if(W(a),t)for(let e=1;e<=3;e++)setTimeout(a,100*e)})),o((()=>W(a))),G([l,n],a),s})(e,!0);return e=>K("div",{class:t("placeholder"),style:{height:s.value?`${s.value}px`:void 0}},[e()])}const[Ke,Oe]=c("action-bar"),Je=Symbol(Ke);const Pe=g(O({name:Ke,props:{placeholder:Boolean,safeAreaInsetBottom:A},setup(e,{slots:t}){const s=V(),a=Ge(s,Oe),{linkChildren:i}=d(Je);i();const o=()=>{var a;return K("div",{ref:s,class:[Oe(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[null==(a=t.default)?void 0:a.call(t)])};return()=>e.placeholder?a(o):o()}})),[ze,He]=c("button");const Ze=g(O({name:ze,props:p({},e,{tag:m("button"),text:String,icon:String,type:m("default"),size:m("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:m("button"),loadingSize:u,loadingText:String,loadingType:String,iconPosition:m("left")}),emits:["click"],setup(e,{emit:s,slots:a}){const i=t(),o=()=>e.loading?a.loading?a.loading():K(w,{size:e.loadingSize,type:e.loadingType,class:He("loading")},null):a.icon?K("div",{class:He("icon")},[a.icon()]):e.icon?K(f,{name:e.icon,class:He("icon"),classPrefix:e.iconPrefix},null):void 0,l=()=>{let t;if(t=e.loading?e.loadingText:a.default?a.default():e.text,t)return K("span",{class:He("text")},[t])},n=()=>{const{color:t,plain:s}=e;if(t){const e={color:s?t:"white"};return s||(e.background=t),t.includes("gradient")?e.border=0:e.borderColor=t,e}},r=t=>{e.loading?y(t):e.disabled||(s("click",t),i())};return()=>{const{tag:t,type:s,size:a,block:i,round:c,plain:d,square:A,loading:g,disabled:p,hairline:m,nativeType:u,iconPosition:f}=e,y=[He([s,a,{plain:d,block:i,round:c,square:A,loading:g,disabled:p,hairline:m}]),{[h]:m}];return K(t,{type:u,class:y,style:n(),disabled:p,onClick:r},{default:()=>[K("div",{class:He("content")},["left"===f&&o(),l(),"right"===f&&o()])]})}}})),[Xe,je]=c("action-bar-button");const _e=g(O({name:Xe,props:p({},e,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean}),setup(e,{slots:s}){const a=t(),{parent:i,index:o}=v(Je),l=J((()=>{if(i){const e=i.children[o.value-1];return!(e&&"isButton"in e)}})),n=J((()=>{if(i){const e=i.children[o.value+1];return!(e&&"isButton"in e)}}));return b({isButton:!0}),()=>{const{type:t,icon:i,text:o,color:r,loading:c,disabled:d}=e;return K(Ze,{class:je([t,{last:n.value,first:l.value}]),size:"large",type:t,icon:i,color:r,loading:c,disabled:d,onClick:a},{default:()=>[s.default?s.default():o]})}}}));function $e(){const e=z({show:!1}),t=t=>{e.show=t},s=s=>{p(e,s,{transitionAppear:!0}),t(!0)},a=()=>t(!1);return b({open:s,close:a,toggle:t}),{open:s,close:a,state:e,toggle:t}}function et(e){const t=P(e),s=document.createElement("div");return document.body.appendChild(s),{instance:t.mount(s),unmount(){t.unmount(),document.body.removeChild(s)}}}let tt=[],st=p({},{icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1});const at=new Map;function it(){if(!tt.length){const e=function(){const{instance:e,unmount:t}=et({setup(){const e=V(""),{open:t,state:s,close:a,toggle:i}=$e(),o=()=>{};return G(e,(e=>{s.message=e})),H().render=()=>K(B,Z(s,{onClosed:o,"onUpdate:show":i}),null),{open:t,close:a,message:e}}});return e}();tt.push(e)}return tt[tt.length-1]}function ot(e={}){if(!C)return{};const t=it(),s=S(a=e)?a:{message:a};var a;return t.open(p({},st,at.get(s.type||st.type),s)),t}const[lt,nt,rt]=c("dialog"),ct=p({},x,{title:String,theme:String,width:u,message:[String,Function],callback:Function,allowHtml:Boolean,className:k,transition:m("van-dialog-bounce"),messageAlign:String,closeOnPopstate:A,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:A,closeOnClickOverlay:Boolean,keyboardEnabled:A,destroyOnClose:Boolean}),dt=[...I,"transition","closeOnPopstate","destroyOnClose"];var At=O({name:lt,props:ct,emits:["confirm","cancel","keydown","update:show"],setup(e,{emit:t,slots:s}){const a=V(),i=z({confirm:!1,cancel:!1}),o=e=>t("update:show",e),l=t=>{var s;o(!1),null==(s=e.callback)||s.call(e,t)},n=s=>()=>{e.show&&(t(s),e.beforeClose?(i[s]=!0,E(e.beforeClose,{args:[s],done(){l(s),i[s]=!1},canceled(){i[s]=!1}})):l(s))},r=n("cancel"),c=n("confirm"),d=X((s=>{var i,o;if(!e.keyboardEnabled)return;if(s.target!==(null==(o=null==(i=a.value)?void 0:i.popupRef)?void 0:o.value))return;({Enter:e.showConfirmButton?c:U,Escape:e.showCancelButton?r:U})[s.key](),t("keydown",s)}),["enter","esc"]),A=()=>{const t=s.title?s.title():e.title;if(t)return K("div",{class:nt("header",{isolated:!e.message&&!s.default})},[t])},g=t=>{const{message:s,allowHtml:a,messageAlign:i}=e,o=nt("message",{"has-title":t,[i]:i}),l=R(s)?s():s;return a&&"string"==typeof l?K("div",{class:o,innerHTML:l},null):K("div",{class:o},[l])},p=()=>{if(s.default)return K("div",{class:nt("content")},[s.default()]);const{title:t,message:a,allowHtml:i}=e;if(a){const e=!(!t&&!s.title);return K("div",{key:i?1:0,class:nt("content",{isolated:!e})},[g(e)])}},m=()=>s.footer?s.footer():"round-button"===e.theme?K(Pe,{class:nt("footer")},{default:()=>[e.showCancelButton&&K(_e,{type:"warning",text:e.cancelButtonText||rt("cancel"),class:nt("cancel"),color:e.cancelButtonColor,loading:i.cancel,disabled:e.cancelButtonDisabled,onClick:r},null),e.showConfirmButton&&K(_e,{type:"danger",text:e.confirmButtonText||rt("confirm"),class:nt("confirm"),color:e.confirmButtonColor,loading:i.confirm,disabled:e.confirmButtonDisabled,onClick:c},null)]}):K("div",{class:[M,nt("footer")]},[e.showCancelButton&&K(Ze,{size:"large",text:e.cancelButtonText||rt("cancel"),class:nt("cancel"),style:{color:e.cancelButtonColor},loading:i.cancel,disabled:e.cancelButtonDisabled,onClick:r},null),e.showConfirmButton&&K(Ze,{size:"large",text:e.confirmButtonText||rt("confirm"),class:[nt("confirm"),{[Y]:e.showCancelButton}],style:{color:e.confirmButtonColor},loading:i.confirm,disabled:e.confirmButtonDisabled,onClick:c},null)]);return()=>{const{width:t,title:s,theme:i,message:l,className:n}=e;return K(N,Z({ref:a,role:"dialog",class:[nt([i]),n],style:{width:D(t)},tabindex:0,"aria-labelledby":s||l,onKeydown:d,"onUpdate:show":o},T(e,dt)),{default:()=>[A(),p(),m()]})}}});let gt;let pt=p({},{title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1});function mt(e){return C?new Promise(((t,s)=>{gt||function(){const e={setup(){const{state:e,toggle:t}=$e();return()=>K(At,Z(e,{"onUpdate:show":t}),null)}};({instance:gt}=et(e))}(),gt.open(p({},pt,e,{callback:e=>{("confirm"===e?t:s)(e)}}))})):Promise.resolve(void 0)}g(At);const[ut,ht]=c("notify"),ft=["lockScroll","position","show","teleport","zIndex"];var yt=O({name:ut,props:p({},x,{type:m("danger"),color:String,message:u,position:m("top"),className:k,background:String,lockScroll:Boolean}),emits:["update:show"],setup(e,{emit:t,slots:s}){const a=e=>t("update:show",e);return()=>K(N,Z({class:[ht([e.type]),e.className],style:{color:e.color,background:e.background},overlay:!1,duration:.2,"onUpdate:show":a},T(e,ft)),{default:()=>[s.default?s.default():e.message]})}});let wt,vt;let bt={type:"danger",color:void 0,message:"",onClose:void 0,onClick:void 0,onOpened:void 0,duration:3e3,position:void 0,className:"",lockScroll:!1,background:void 0};const Ct=()=>{vt&&vt.toggle(!1)};function St(e){var t;if(C)return vt||({instance:vt}=et({setup(){const{state:e,toggle:t}=$e();return()=>K(yt,Z(e,{"onUpdate:show":t}),null)}})),e=p({},bt,S(t=e)?t:{message:t}),vt.open(e),clearTimeout(wt),e.duration>0&&(wt=setTimeout(Ct,e.duration)),vt}g(yt),j.defaults.baseURL=window.location.href.includes(".medsci.cn")?"https://www.medsci.cn/":"https://portal-test.medon.com.cn/",j.interceptors.request.use((e=>(_.get("userInfo")&&(e.headers.Authorization=encodeURIComponent("userInfo="+_.get("userInfo"))),e)),(e=>{Promise.reject(e)}));const Bt={name:"common",components:{Popup:N,Loading:w,pay:L,payMobile:F,CloseBold:$,Document:ee},data(){return{isIOS:!1,fnlList:[],recomendList:[],topKeywords:[],isLightTheme:!1,contextIdIndex:-1,hoverIndex:-1,activeIndex:-1,isImgVisible:!1,footerHeight:"100px",imgTop:0,titleText:" 你好，我是&nbsp梅斯AI小智",isOtherTips:!1,otherTipsList:[],isLeftNav:!0,message:"",messageList:[],contextDisable:!1,messageTp:"",userInfo:{},defaultAvatar:te((()=>Promise.resolve().then((()=>Rt))),void 0),userName:"",userAgent:this.getRandomUser(),avatar:"",logList:[],showMenu:!1,chatTopic:"",showTxt:"",isShow:!0,chatTemplateList:[],filterList:[],answer:"",feedbackHref:"",eventSource:void 0,templogList:[],payShow:!1,currentItem:"",textareaHeight:"40px",isNewDialogue:!0,route:{},conversation_id:"",lights:"",darks:"",typingQueue:[],isTyping:!1,interval:null,controller:null,nodeInfo:"",qa_id:"",query:"",isZH:!1,isSubscribe:!1,fileUploadType:"",acceptList:{image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},fileList:[],files:[],dAppUuid:""}},computed:{highlightText(){return this.isLightTheme?this.titleText.replace(/梅斯AI小智/g,"<span style='color:#4892E7'>梅斯AI小智</span>"):this.titleText.replace(/梅斯AI小智/g,"<span style='color:#79B7FF'>梅斯AI小智</span>")},currentTheme(){return this.isLightTheme?"light-theme":"dark-theme"}},async created(){this.route=se(),window.location.href.includes("medon.com.cn")?this.feedbackHref="https://portal-test.medon.com.cn/message/add_msg.do":this.feedbackHref="https://www.medsci.cn/message/add_msg.do",_.get("userInfo")&&(this.userInfo=JSON.parse(_.get("userInfo")),this.userName=this.userInfo.userName)},async mounted(){var e,t;const s=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${s}px`),(null==(e=this.$route.query)?void 0:e.query)&&(this.query=decodeURIComponent(null==(t=this.$route.query)?void 0:t.query)),this.qa_id=this.$route.query.qa_id,this.$i18n.locale=ae();const a=navigator.userAgent;this.isIOS=/iPad|iPhone|iPod/.test(a)&&!window.MSStream,this.getInputForm(),this.getChatTemplate(),this.chatMessage(),this.getTemplateInfo(),this.insert(),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(this.isZH=!0);const o=await ie(this.$route.params.appUuid);this.currentItem=o,this.dAppUuid=o.dAppUuid,i(o.customCss,o.customJs),this.listMessage()},watch:{fileList:{handler(){setTimeout((()=>{this.adjustContentHeight()}),0)},deep:!0,immediate:!0}},methods:{adjustContentHeight(){const e=document.getElementById("scrollbar"),t=document.getElementById("scrollFiles"),s=document.getElementById("scrollFilesPc");if(e&&t){const s=`calc(var(--vh) * 100 - 200px - ${t.offsetHeight}px)`;e.style.height=s}if(e&&s){const t=`calc(var(--vh) * 100 - 280px - ${s.offsetHeight}px)`;e.style.height=t}},checkFileType(){let e="";return this.fileUploadType&&this.fileUploadType.file_upload.allowed_file_types.forEach(((t,s)=>{s<this.fileUploadType.file_upload.allowed_file_types.length-1?e+=this.acceptList[t].join(",")+",":e+=this.acceptList[t].join(",")})),e},handleRemove(e,t){this.fileList=t},handlePreview(e){},handleExceed(e,t){oe.warning("超出上传文件个数")},fileToBase64:e=>new Promise(((t,s)=>{const a=new FileReader;a.onload=e=>{t(e.target.result)},a.onerror=s,a.readAsDataURL(e)})),async handleSuccess(e,t){},async customRequest(e){const{file:t,onSuccess:s,onError:a}=e,i=new FormData;i.append("file",t),i.append("appId",this.dAppUuid),i.append("user",this.userInfo.userName);try{const e=await le(i),a=await this.fileToBase64(t);if(e.mime_type.includes("image"))this.fileList.push({url:a,type:e.mime_type.split("/")[0],upload_file_id:e.id,name:e.name,filename:e.name,size:e.size,transfer_method:"local_file",extension:e.extension.toUpperCase()});else{const t={jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"};this.fileList.push({url:"",type:t[e.extension],name:e.name,size:e.size,filename:e.name,extension:e.extension.toUpperCase(),upload_file_id:e.id,transfer_method:"local_file"})}s(e,t)}catch(o){a(o)}return!1},beforeRemove:(e,t)=>ne.confirm(`Cancel the transfer of ${e.name} ?`).then((()=>!0),(()=>!1)),handleDelete(e){this.fileList=this.fileList.filter((t=>t.upload_file_id!=e))},toAgreement(){window.open("https://www.medsci.cn/about/index.do?id=27")},async getAppListData(){const e={appUuid:this.$route.params.appUuid};re(e).then((e=>{this.currentItem=e[0]})).catch((e=>{}))},getInputForm(){this.dAppUuid&&ce({appId:this.dAppUuid,user:this.userInfo.userName}).then((e=>{this.fileUploadType=e,e.opening_statement&&this.messageList.push({chatType:2,answer:e.opening_statement,suggested_questions:e.suggested_questions,isTrue:!0})}))},defoultAnswer(e){this.message=e,this.sendMessage()},handleOrder(){this.payShow=!0},async stop(){var e;await de({appId:this.dAppUuid,user:this.userInfo.userName,mode:null==(e=this.nodeInfo)?void 0:e.mode,task_id:this.task_id})},async getAnswer(){var e;this.answer="";try{let t=`${window.location.origin}/dev-api/ai-base/chat/chat-messages?locale=zh`;if(this.messageTps=this.messageTp.replaceAll(/\[|\]/g,"").replaceAll("<br/>",""),this.typingQueue=[],this.answer="",this.controller=new AbortController,this.templogList.length>0&&this.templogList.forEach(((e,t)=>{e.isActive=!1,e.id==this.conversation_id&&(e.updated_at=parseInt((new Date).getTime()/1e3),e.isActive=!0,this.templogList.unshift(e),this.templogList.splice(t+1,1))})),!this.conversation_id){let e={};e.updated_at=parseInt((new Date).getTime()/1e3),e.isActive=!0,e.name=this.$t("faq.newChat"),this.templogList.unshift(e)}this.logList=this.oldList(this.templogList,this.getNowTime());const a={user:this.userName,response_mode:"streaming",conversation_id:this.conversation_id,query:this.messageTps,requestId:crypto.randomUUID(),files:this.files,inputs:{spsPrompt:"",sourceText:this.messageTps,outputLanguage:Ae(),article_id:null==(e=this.$route.query)?void 0:e.article_id,query:this.messageTps,qa_id:this.qa_id,sys:{conversation_id:this.conversation_id,files:[],query:this.messageTps,user_id:""}},appId:this.dAppUuid,appUuid:this.route.params.appUuid};await s(t,{method:"POST",headers:{"Content-Type":"application/json; charset=utf-8",Authorization:`Bearer ${localStorage.getItem("yudaoToken")}`},body:JSON.stringify(a),signal:this.controller.signal,openWhenHidden:!0,onopen:e=>{if(!e.ok)throw new Error("连接失败")},onmessage:e=>{const t=JSON.parse(e.data);this.task_id=t.task_id,t.error&&(this.controller.abort(),this.handleMessage(t.error),this.handleChatStatus("error"),oe.error(errorMsg)),"error"===t.event&&(5047==t.code&&(this.isSubscribe=!0),setTimeout((()=>{this.controller.abort(),this.task_id&&this.stop()}),0),this.handleMessage(t.message),this.handleChatStatus("error")),"message"===t.event&&(this.conversation_id=t.conversation_id,this.typingQueue.push(t.answer),this.isTyping||this.processTypingQueue()),"message_end"===t.event&&(this.handleChatStatus(),this.contextDisable=!1,this.task_id="",this.qa_id="",this.listMessage(),this.files=[])},onerror:()=>{setTimeout((()=>{this.controller.abort(),this.task_id&&this.stop()}),0),this.handleChatStatus("error"),this.handleMessage(this.$t("tool.accessbusy"))}})}catch(t){setTimeout((()=>{this.controller.abort(),this.task_id&&this.stop()}),0),this.handleChatStatus("error"),this.handleMessage(this.$t("tool.accessbusy"))}},processTypingQueue(){if(0===this.typingQueue.length)return void(this.isTyping=!1);this.isTyping=!0;const e=this.typingQueue.shift();this.simulateTyping(e).then((()=>{this.processTypingQueue()}))},simulateTyping(e){return new Promise((t=>{let s=0;this.interval=setInterval((()=>{s<e.length?(this.answer+=e[s++],this.handleMessage(this.answer)):(clearInterval(this.interval),t())}),0)}))},handleMessage(e){const t=this.messageList.length;this.messageList[t-1].answer=e,this.$nextTick((()=>{document.getElementById("scrollbar").scrollTo(0,document.getElementById("scrollbar").scrollHeight)}))},handleChatStatus(e){"error"==e&&(this.contextDisable=!1);const t=this.messageList.length;this.messageList[t-1].loading=!1},adjustTextareaHeight(){var e=this.$refs.myTextarea;e.style.height="auto",e.style.height>=60?e.style.height=60:e.style.height=e.scrollHeight+"px"},getAiKeywordTop(){j.post("/medsciAiKeywordTop/queryAiKeywordTop",{pageIndex:1,pageSize:5}).then((e=>{200==e.status&&(this.topKeywords=e.data.data)})).catch((e=>{}))},getChatTemplate(){this.filterList=[]},getFilteredItems(){return this.chatTemplateList.filter((e=>1===e.approvalStatus)).map((e=>{const t=e.chatVariable.split(",");return e.chatFormatTemplate=e.chatTemplate,t.forEach((t=>{e.chatFormatTemplate=e.chatFormatTemplate.replaceAll(`[${t}]`,`<span style="color:#79B7FF">[${t}]</span>`)})),e}))},stripHtmlTags:e=>e.replace(/<[^>]*>/g,""),onMouseover(e){this.hoverIndex=e},onMouseout(){this.hoverIndex=-1},setInput(e){j.post("/medsciAiTemplate/queryAiTemplate",{chatTags:e,pageIndex:1,pageSize:20}).then((e=>{200==e.data.status?(this.message=e.data.data[0].chatTemplate,this.isOtherTips=!1):204==e.data.status&&ot("暂无审核通过的数据")})).catch((e=>{}))},chatMessage(){"/"==this.message?this.isOtherTips=!0:this.isOtherTips=!1},getDiffDay(e,t){let s,a;this.isIOS&&(e=e.replace(/-/g,"/"),t=t.replace(/-/g,"/"));let i=Date.parse(e),o=Date.parse(t);return a=Math.abs(i-o),s=Math.floor(a/864e5),s},changeImg(){this.avatar="https://img.medsci.cn/web/img/user_icon.png"},async initLogin(){localStorage.getItem("yudaoToken")||await this.loginMain()},escapeHtml:e=>e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):"",unescapeHtml:e=>e?e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):"",async loginMain(){var e;try{const t=await ge({userId:this.userInfo.userId,userName:this.userInfo.userName,realName:this.userInfo.realName,avatar:this.userInfo.avatar,plaintextUserId:this.userInfo.plaintextUserId,mobile:this.userInfo.mobile,email:this.userInfo.email,fromPlatform:this.$route.query.fromPlatform||"",appUuid:this.route.params.appUuid||""});(null==t?void 0:t.token)&&(null==t?void 0:t.htoken)&&(localStorage.setItem("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType),(null==(e=this.$route.query)?void 0:e.article_id)&&(this.message=this.query,this.sendMessage()))}catch(t){}},getRandomUser(){let e=`randomUser${Date.now()+Math.ceil(99999*Math.random())}`;return window.localStorage.getItem("randomUser")||window.localStorage.setItem("randomUser",e),window.localStorage.getItem("randomUser")},async newTalk(){this.task_id&&(clearInterval(this.interval),this.controller.abort(),this.isTyping=!0,await this.stop()),this.conversation_id="",this.isNewDialogue=!0,this.messageList=[],this.chatTopic="",this.listMessage(),this.showMenu=!1,this.isShow=!0,this.contextDisable=!1,this.answer="",this.getInputForm()},hidePage(){this.isShow=!1},close(){this.payShow=!1},closeMenuPc(){this.isLeftNav=!1},openMenuPc(){this.isLeftNav=!0},listMessage(){pe({appId:this.dAppUuid,user:this.userName,limit:30,lastId:"",pinned:!1}).then((e=>{if(e.data){this.logList=[];let t=this.getNowTime();e.data.forEach(((t,s)=>{t.id==this.conversation_id&&(e.data[s].isActive=!0)}));let s=e.data;if(this.templogList=e.data,!s)return;this.logList=this.oldList(s,t)}})).catch((e=>{}))},oldList(e,t){let s=e.map((e=>(e.createdTime=this.formatTimestamp(e.updated_at),{...e,daySubVal:this.getDiffDay(e.createdTime,this.getNowTime()),yearSubVal:Number(t.substring(0,4)-e.createdTime.substring(0,4))})));s=JSON.parse(JSON.stringify(s));for(var a=0;a<s.length;a++)s[a].createdTime=this.getDaySubFn(s[a].daySubVal,s[a].yearSubVal,s[a].createdTime);let i="";for(var o=0;o<s.length;o++)0!=o?o>0&&(s[o].createdTime==i?s[o].createdTime="":i=s[o].createdTime):i=s[o].createdTime;return s},async insert(){var e,t;let s=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=this.userInfo)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:this.dAppUuid,userUuid:null==(t=this.userInfo)?void 0:t.openid}];await j.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",s)},getDaySubFn(e,t,s){return t>1?s.subString(0,10):e>=30?this.$t("tool.30daysAgo"):e>=7?this.$t("tool.1weekAgo"):e>=1?this.$t("tool.yesterday"):this.$t("tool.today")},transformMultipleData(e){const t=[];return e.forEach((e=>{if(e.query){const s={...e,answer:e.query,chatType:1};t.push(s)}if(e.answer){const s={...e,answer:e.answer,chatType:2};t.push(s)}})),t},async getLog(e,t,s){this.fileList=[],this.task_id&&(clearInterval(this.interval),this.controller.abort(),this.isTyping=!0,await this.stop(),this.contextDisable=!1),this.conversation_id="",this.isNewDialogue=!0,this.contextIdIndex=s,this.logList.forEach((t=>{t.isActive=!1,t.id==e&&(t.isActive=!0)})),this.chatTopic=t,me({appId:this.dAppUuid,conversation_id:e,user:this.userName,limit:50,firstId:""}).then((e=>{if(e.data&&Array.isArray(e.data)){const t=this.transformMultipleData(e.data);this.messageList=[...t],this.conversation_id=e.data[0].conversation_id}this.showMenu=!1})).catch((e=>{this.showMenu=!1})),setTimeout((()=>{this.$nextTick((()=>{document.getElementById("scrollbar").scrollTo({top:document.getElementById("scrollbar").scrollHeight,behavior:"smooth"})}))}),1e3)},getTemplateInfo(){this.dAppUuid&&ue({appId:this.dAppUuid,user:this.userInfo.userName}).then((e=>{this.nodeInfo={...e}}))},async sendMessage(){var e,t,s,a;if(!_.get("userInfo")){const e=he();return void(e&&"zh-CN"!=e?this.$router.push("/login"):window.addLoginDom())}if(this.userInfo=JSON.parse(_.get("userInfo")),this.userName=(null==(e=this.userInfo)?void 0:e.userName)||this.userAgent,(null==(t=this.userInfo)?void 0:t.avatar)&&"/v1.0.0/img/user_icon.png"!==(null==(s=this.userInfo)?void 0:s.avatar)&&(this.avatar=null==(a=this.userInfo)?void 0:a.avatar),await this.initLogin(),!this.currentItem.appUser)return void(this.payShow=!0);this.isNewDialogue=!0,this.isTyping=!1,this.task_id="";const i=document.getElementById("scrollbar");if(this.message){this.contextDisable=!0;const e=[{chatName:this.userName,chatType:1,answer:this.escapeHtml(this.message).replace(/\n/g,"<br/>"),message_files:this.fileList},{chatName:"medsciAI",conversationId:111,loading:!0,chatType:2,answer:""}];this.files=JSON.parse(JSON.stringify(this.fileList)),this.fileList=[],this.messageList.push(...e),this.$nextTick((()=>{i.scrollTop=i.scrollHeight})),this.messageTp=this.unescapeHtml(this.message),this.message="",this.getAnswer()}},reloadMessage(){this.message=this.messageTp,this.messageList.splice(-2),this.sendMessage()},openMenu(){this.showMenu=!0},logout(){var e;(e={title:"提示",message:"是否退出登录？"},mt(p({showCancelButton:!0},e))).then((()=>{let e;localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken"),_.remove("userInfo",{domain:".medon.com.cn"}),_.remove("userInfo",{domain:".medsci.cn"}),_.remove("userInfo",{domain:"localhost"}),e=window.location.href.includes(".medsci.cn")?"https://www.medsci.cn":"https://portal-test.medon.com.cn",window.location.href=e+"/sso_logout?redirectUrl="+window.location.href})).catch((()=>{}))},getNowTime(){const e=new Date;return`${e.getFullYear()}-${e.getMonth()+1}-${e.getDate()} 00:00:00`},editBtn(e){this.message=e},formatTimestamp(e){const t=new Date(1e3*e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}:${String(t.getSeconds()).padStart(2,"0")}`}}},xt="/apps/static/empty-a0e46f50.png",kt="/apps/static/empty2-0d4f47e9.png",It="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAVCAYAAABCIB6VAAAAAXNSR0IArs4c6QAAAqdJREFUSEudlUuIT2EYxn/PGJeIyC0bEslCaRYMRW6RCGkWymzEQjEpGxOiWCAmiRgLl5JLLhELZZqocWnEJFnZmCiSa1KUubx6ju9MZ/4Npvk2//855zvP97zP+7zPEYUVEWWSOn0rIuYDy4AFwDRgUNr6BngC3AIaJL1P+7ve9bXSTf/2k9QeEZXADmBlAvoJfAA+A0OAMcCI9OwVcAI4KqmtSKwLWFJExEbguA8BmoBzwD1JBshWRAwHKoDVwKa09zZQI6k1ImQso5lpR0TUAMeAb8Bm4KqkX0WpSv9HxCTgcKruGbAkVdYlhcu+mUqukvQgsSsHOsygwNhVlmU6/iHk6g4BW4EbQFX2KCKGAg+BqW6WpMaI6A+0FwF7Yl6o1ofdARYDayVdMvA64CxwRtKG3oIWKsilnA60AI+BeQa2BMuBhZKachb/0rbEombrhnVGhC24FKg08DtgMDDKdustYAl41gugFtgPVGceA55KmtEX0NTkXI5q4LwbaWBP2gtJ1qhPKyLK03CtB04BWwz8GrDpJ0r60hdkNzxN3l5gF7DKwCcBT9waSVfy03t7gCctRYO93ZxsW2HgRUAjcN/OSICdeRj974CCDPZwA3BdUpWBBwDXgBWZ6NKR3lquIMHI5N8JwFxJzXkITQYeAaMdLJLqU4k54WxfYfnaVdlUY4ELgCvfKWlflnJ51EXELOAyMB7YDtSV5kQPIeSMcU5MAQ5KqjWeQzBnnNtlT+rqNkl11g+wVB6ggSns7aDZbjYwB2gDdks6kAIpqyQHdkJ5UMzY6eQJGgfM9ESmcB+WsrdI/DRQL6klz+Fu2hVSyjqbTb4+pnz9CvwAvgOtwHM7SdLb4uSVNsFfhex7FRE2uMfyInAXeAl8+lvgJz0dQM6Jbus3KV9e6MbuvdIAAAAASUVORK5CYII=",Ut="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAVCAYAAABCIB6VAAAAAXNSR0IArs4c6QAAA3tJREFUSEudlF9oHUUUxr+ze72WmA0Rq+KLIoqRIPTuzppYUFxbKlKxIuFSMC+iD4IWoS+KSgX7oGKLiKL1wT8g/kGvKO2DYAl6qdq0uZlZghDwIRYFRapVJKCJvTufnOvesIlSYwb2Yc/M/Oacb+Z8gtUjAOA1lKZpBmAngFu899eJyKZy6XckOySP1Gq1o51O58cyvrJX/6UMSpZlYbvd7hpjxkk+JiK7yrk/AJwmeQbABSJyCYALy7lvALwM4AVr7VkAK/AVMACmaXo/yZcAhCJyDMCbIvJZp9NRQG80Go3hMAxjAHcBeEDXAvg4DMM9MzMzp8pkKc1mM2y1WkWSJHtE5EUAv5F8cGlpqTU/P//nGqlW/RpjriL5XFldDuBWa61W9rcUaZruInmY5GkRmbDWfqHxLMtq7Xa70GoqRE1GS4YmpIktLCwcEJG9AD6y1k4oV0ZGRqLBwcEvAVzrvd+Z5/mUMeY8a213DfAfyferVVAcx58EQbDDe393nufvSpIk94jIGyRfd87dt15o/5Q+PI7jLUEQWJInh4aGblbwYRG53Xu/Lc/zY5UsziVvdU7l1M+naXqE5G0iMi7GmB8ADERRtFmf23pp1XX9uzDGPALgaQCTCqaIzM7Ozl6/Eaju6VfZaDQmwzB8i+ReBWunfWWt3bJRcJlxN03Te0m+KiIPqcbfishwvV6/cnp6+peNwMsLP2uM2Q9gn4jcqeBXREQ7brdz7v3+6f/jgN7lZVkWLC4untBnWxRFrO9vexAEUwA+j6JomwLb7bbK0zOj/xoVGXaQPEryQ+fchIyOjtYHBgY+IHmHiu6ce369T64vwdjY2EVFUZwEcAXJm5xzJ3otnSTJ1QCOi8jFaizW2kMV51tp/UpTSKvV0oo4Pj5+aVEUb5PcTvJx59xT6nIK7lldkiQ3iMh7AC4H8GgURQf/xSdWKVN6zAEA15B81jmn71h57GXc1ymO4yeDINgH4GFr7UEANWNMXRuo2+2eX6vVNpEcBrBVRHYDuBGA+vAT1tpnSgl7lfTAZYDGGM14gqSefBnJsSAINgNQcx8qvbea9WskDznnbN+H+5NVcJGm6XGSWys7fwJwRkR+BfC7934RwCkRmet2u1Nzc3PfVztvrYHof0/nOI73i8ikiLwD4FPv/dfLy8s/n8Pwg2azqRepnr1q/AUULLdcOgYOhAAAAABJRU5ErkJggg==",Tt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAAAXNSR0IArs4c6QAABRxJREFUWEfVmFmIXFUQhr9fE3fBHTfcQFFEBY0r0agEXFBEhMH1SWGCRIUkj5K4PbkgBkI0Li9GRUZEkQER9wcTVBDcRSQxPhiXmCjigpojf1OnOXPn9nTf2+1ILjTTc/ss//mr6q+qI3awRzsYXkYGOKW0sw8v6Z+ShF7v2xI1EsAGVQJNKe1k7MB2ScngqmP+F8ApJYOSpO0ppeOBS4ALgAOBucD3wMvAC5I2BdvdQ7QBPRTDBmwGU0q3AHcC+5rMYLdDbHzfDNwr6cE8pw3Yjsu1nViAvQ9YZvMDdoVtwKZY9whgnwL4A5KWDQO6FeDsjymlpcD9gAPNn8eA1cC3Afgg4EZgSRzIgTkuaU1bn24M2AEVPnsU8BZgFm36MUnP5d8j0PLYq4GnwgIbgAWSvinHDmrpNoA7ipBSGgcejo3WSBov1KGjDNnlYvwq4OZ4v1jSqjYstwGcA+1J4HrgD+BCSevqABTucxbwBrCb2ZbkuY2fxoDzDikly9VFgE1swBvrTFy40JHA68AxljpJlsDGzzCAJ4FLgY0BeEMfwAZqwAY+KemyxmjbyFohZ1aDRbHpQkmvpZTmSPq7kpo771JKCwKwpe8RSYvayFtjhjOolNIY8GyAm5A0FpnP0pWDzj93MltK6Wngmhh/g6S1sxV0War2A94GTgwQKyTdVbJWWGMFcEeM+wQ4T9JPsyJrFX29wnVCMOpMNwE8ETWEWbZGL47gzGnaej3Rht1hU3Nm+nbg7iL9GtjWYNO1RXY7v18u6Z42vpvjorEPVwIqg3bELwdOi2zWVb8A/AWwVNJkGzco9xwKcMU99gQWAucAhwN/hWusdwqXtKWtG4wUcIDuKINrjF7aOiyzI3GJqnuEOxh0WUtYdy1tPQ/TJIEM5BLRKXRanorGdvbKbVC/jUOnq8O8rg9lC03pB+vWmxFwbODA6rtQP7CD/B7VnoGXiWfK1J6AY7LZc7/m2nce4L7tUGDvYNoL7xpdRT+T527kz1AO7/0L8APwObBO0tflvgMzXGSo/aNXuwo4eBCWhhjzXSQd937bemn1NIaL+vUQ4HnAdWzu14znV+C3IiF4jd9DxnpZzJZwF717EQN+twdgOczxYStYBq+UtLlOBqdsUDDrOuFFYH4wtgV4PApwf68C9v/W3X6ADTD7p//6AM6G50bl5+sBP+8CF0vaWmW6CjhnrrXAdTH5PX+X9OUwKXUGfc4dzLGA9z0jxuYStINpmg4XrnA68GaYyxchZ0Y34eTgA9ZFcM+orgCts0Bnzej7DNpFvjPlz76UkfRB6RrdBQrAvhRZGRvdJmllSmmuJJv8P3vyHimlW4GHYqMlcfnSvQorAWfTPArcFIHkXm39KGqAfictCLN8ulndC3hG0rXl3GlBB7zqHg34Kno134lN8aN+m7f5vWhWDwu3OA54RZIb3e5TBWw/taz4lB+HD/04y4CtGo6hkwPL/DLT1gF+JyL1owDssnBOaHEb8gadYyu6WT0gGD4JsEKdPSjgD4HzQwtn0yXMsH34lEEYNpN2CXcOnwGXR653lhpUugZltDrO1rYSOXm8BJwAmLR5pUJVXcKFzKdxO+Ps5UsS3zMMVIa2RVrMMykmzcWWs6Lj6NSZAO8SldPRlYvpEWBptESuXd53LVPrw0Ud4dscf2yefiVjIxQNBtuiZnh13HJ2coTn15q6uDZtsMfIh9b2iHXlZfc0I4fQcMG6Ymu2gqkh1N7DdzjA/wLi671LeMOTvwAAAABJRU5ErkJggg==",Dt="data:image/png;base64,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",Nt="data:image/png;base64,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",Et="/apps/static/avat-f0cd2051.png",Rt=Object.freeze(Object.defineProperty({__proto__:null,default:Et},Symbol.toStringTag,{value:"Module"})),Yt="/apps/static/logo_pc-ebd04dd3.png",Mt="/apps/static/robot-08128bd4.png",Lt={key:0,class:"chatBar"},Ft={style:{padding:"0 50px","margin-top":"30px"}},Qt=["href"],Vt=["src"],qt={key:0},Wt=["onClick"],Gt={key:0,class:"chatTime"},Kt=["onMouseover","onMouseout"],Ot={class:"chatTopic"},Jt={key:1,class:"empty-box"},Pt=["src"],zt={style:{"margin-top":"0","text-align":"center"}},Ht={class:"userList"},Zt={class:"userBox"},Xt=["src"],jt={key:0},_t={key:0,class:"userBox"},$t={class:"userBox-text"},es={class:"feedback"},ts=["src"],ss=["href","title"],as={key:0},is={key:0,style:{cursor:"pointer"}},os={key:0,class:"openBtnLeft"},ls=["src"],ns=["href"],rs={key:1,class:"openBtn"},cs=["src"],ds=["href"],As={class:"topTitle"},gs=["href"],ps=["src"],ms=["href"],us={style:{position:"relative"}},hs={id:"scrollbar"},fs={key:0,class:"box"},ys={key:0,class:"contextStyle1"},ws={style:{display:"flex","align-items":"center",width:"34px","margin-right":"10px"}},vs=["src"],bs={style:{display:"flex"}},Cs={class:"name"},Ss={key:0,class:"logo",src:"https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png",alt:""},Bs={key:1,class:"logo",src:"https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png",alt:""},xs={class:"size"},ks=["innerHTML"],Is={key:1,class:"contextStyle2"},Us={class:"contextStyle2Top",style:{"text-align":"justify"}},Ts={style:{width:"calc(100% - 3.3rem)"}},Ds={key:1},Ns={class:"tags"},Es=["onClick"],Rs={key:1,class:"box"},Ys={key:0,class:"contextStyle1"},Ms={style:{display:"flex","align-items":"center",width:"34px","margin-right":"10px"}},Ls=["src"],Fs=["innerHTML"],Qs={key:1,class:"contextStyle2"},Vs={class:"contextStyle2Top",style:{"text-align":"justify"}},qs={style:{width:"calc(100% - 3.3rem)"}},Ws={key:1},Gs={class:"tags"},Ks=["onClick"],Os={class:"footer"},Js=["href"],Ps={class:"footer-wrap-inner"},zs={style:{display:"flex","flex-direction":"column",position:"relative","border-radius":"8px"}},Hs={class:"footer-wrap"},Zs={class:"files",id:"scrollFilesPc"},Xs={class:"icon"},js={class:"name"},_s={key:0,class:"logo",src:"https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png",alt:""},$s={key:1,class:"logo",src:"https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png",alt:""},ea={class:"size"},ta={class:"btomArea"},sa=["placeholder","readonly"],aa={key:0,class:"upload"},ia=["src"],oa=["src"],la={style:{"margin-top":"16px","text-align":"center",position:"relative"}},na={class:"change_background",style:{position:"absolute"}},ra={style:{"margin-left":"4px"}},ca={class:"response"},da=["title"],Aa={href:"https://www.medsci.cn/about/index.do?id=14",target:"_blank"};const ga="data:image/png;base64,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",pa={class:"logList"},ma={key:0},ua={key:0,class:"chatTime"},ha={class:"chatItem"},fa=["onClick"],ya={key:1,class:"empty-box"},wa=["src"],va={class:"userList"},ba={class:"userBox"},Ca=["src"],Sa={class:"userBox"},Ba={class:"userBox-text"},xa={class:"feedback"},ka=["src"],Ia=["href"],Ua={class:"betaBox"},Ta={class:"logoBox"},Da=["href"],Na=["src"],Ea=["href"],Ra=["src"],Ya={style:{padding:"0 15px"}},Ma={id:"scrollbar"},La={key:0,class:"box"},Fa={key:0,class:"contextStyle1"},Qa={style:{display:"flex","align-items":"center",width:"34px","margin-right":"10px"}},Va=["src"],qa={style:{width:"calc(100% - 25px)"}},Wa={style:{display:"flex","flex-wrap":"wrap"}},Ga={class:"name"},Ka={key:0,class:"logo",src:"https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png",alt:""},Oa={key:1,class:"logo",src:"https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png",alt:""},Ja={class:"size"},Pa=["innerHTML"],za={key:1,class:"contextStyle2"},Ha={class:"contextStyle2Top",style:{"text-align":"justify"}},Za={style:{width:"calc(100% - 3.3rem)"}},Xa={key:1},ja={class:"tags"},_a=["onClick"],$a={key:1,class:"box"},ei={key:0,class:"contextStyle1"},ti={style:{display:"flex","align-items":"center",width:"34px","margin-right":"10px"}},si=["src"],ai=["innerHTML"],ii={key:1,class:"contextStyle2"},oi={class:"contextStyle2Top",style:{"text-align":"justify"}},li={style:{width:"calc(100% - 3.3rem)"}},ni={key:1},ri={class:"tags"},ci=["onClick"],di={style:{"text-align":"center",position:"fixed",bottom:"0",width:"100%",padding:"0 15px 10px"}},Ai=["href"],gi={class:"footer"},pi={class:"files",id:"scrollFiles"},mi={class:"icon"},ui={class:"name"},hi={key:0,class:"logo",src:"https://img.medsci.cn/202504/48cc100dc0504cd69497dedfc62e3253-nT0cKepokaex.png",alt:""},fi={key:1,class:"logo",src:"https://img.medsci.cn/202504/a43866f6e96d420e8a6dcdeefd09f4f4-Iw757USlM4QI.png",alt:""},yi={class:"size"},wi={class:"btn_box"},vi=["placeholder","readonly"],bi={key:0,class:"upload"},Ci=["src"],Si=["disabled"],Bi=["src"],xi=["disabled"],ki=["src"],Ii={class:"footer_box"},Ui={class:"change_background",style:{position:"absolute"}},Ti={style:{"text-align":"right",width:"100%"}},Di={class:"linkStyle"},Ni={href:"https://www.medsci.cn/about/index.do?id=14",target:"_blank"};const Ei=fe({name:"App",components:{pcPage:fe({mixins:[Bt],setup(){const e=se();ye({title:()=>{var t;return`<${null==(t=e.query)?void 0:t.appName}>${a[localStorage.getItem("ai_apps_lang")]}`}})},data:()=>({emptyDark:xt,emptytlight:kt,open_pc:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAiCAYAAAA3WXuFAAAAAXNSR0IArs4c6QAAAgtJREFUWEftmLtKXFEYhb81GtQqXUhtlUdIZaFlOtNIwBCwsLFKHcXCV7AQIcFSSBqfICFFWtOEFHaSTsitUNHxl3XYezhMxsvAuRXZzTSH2Wt//23trYjYAN4AD4AARLMr7+nfTUXEJTDRrIYbdzu3oD5w1QKZYVUmNGFBZ8BURwj9tqA14CVw0TIlR2mnSOCIcA71WqZ0JalvQhYSkhzD1lZEGE4vE2q61Ece3FA6IaSs7r+gTCPljCS5ugarFUIW43xJoigXVK6yotJqKDEfuJ8275lGRDwBdoHvwGtJf912XPLev3ZCJRqTki4j4jnwPh3+I7Ao6afbjwWb0GPgWWnaVwHKtO0ejiUdpOabBS0CHwCPrGngcxJ1UoiKiC/A0xqsR7YVryTtRcSUpPMSoTyqJoFvwAtJXy3IWV7Er+JlW2MCbyWtRMSMpNMhQabo7yzqB7CQBVlU1UntQ9pFbEtai4hpSWe3CDoC5i3oEzCXPFEdA3ZJ0v4dITsEHNoiZI9SUvs0VRm1+yT1KTDzT1JXnDc3/l1E3LvsHaYmG+Ms8A5wmNYl/Wm0MY5ClpvgyNHRVMiGhupgSnRiuN4GofZZNm4Eumdh043Djr/qTj0WnIFh6+I1aBlYbfleljv7nkfHL+DhWHzr+7h4bMj2o+2KM6XCoHXpsaG4SvvBajP56zYfrOwgt64BvwEu6hxOlDsAAAAASUVORK5CYII=",open_pc2:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAARCAYAAADQWvz5AAAAAXNSR0IArs4c6QAAATBJREFUOE+tlL9KA0EYxGfuEq1VBE0aDdj4JJfCRjwb8QWMEmzEzhN78c4n0DLB2uiDWJqAJCdY2JnivB3ZA238g3vJV+3CMvv7Zr5dBknaB7ACIANAuJUAVAEMGCSp3UxaYpCMNimvDupdxo2IHiSwSqDv2sqv5Aw78l8e3Eis2uI69PgKz64bczCFkItB3RAGpKJIhYit6ARybs1e3N1m3kzSS0jzt+3ajhWzZscUG/KYWed+oiMhSL6AMXMc9w6XB0GSdgCEAq5nx5V9l/hzAL6orbuD2s2nUEEjnDGIn88Jrf2HCMSb8czRfav+9CVEXFVntFfeo3h0IXgLvfbSbkE1SWo2regULFKb2hy5zNBfZ9lMhhsCVwlmZd4aZCoihzZ+U+L7+AZnhabyH30AN4KV2zvMaWAAAAAASUVORK5CYII=",close_pc:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAiCAYAAAA3WXuFAAAAAXNSR0IArs4c6QAAAgRJREFUWEfNmL1qVFEUhb+ViSQpVAQ7sYogWIgvIPgENgpWKgpioY8gEtDexsZCMIVgYzpLLax8CPEZtEqGmFmyrueEcZyREeb+nGKmudz7nbV/zjpbtp8CT4ATgAHR7arfzP+ObP8ERt0yLPzaOEBHwKQHZWapotAoQAfAxkAU+hGgx8Ad4LBnlRKlV00C204OrfWs0kTSURQKiCUlhr0t2xFnrSrUdanP3XhEGQTINN3wgUpONbmen65z6w+FSmI1EAGTlFLsdB0DpfRTdrZPAc+AK8A9Sd8qXAFOi2ijIsMyqVXWqGH7DLAHXCuy3JT03va6pJx5ra+mDxWYswXmKrAPbAE3JO1NA9m+DpxfYWeP2umFY+BDVegysFvCFCXyUOxIVWhD0tj2LeBdCzIlVwP1OQplt5+AC0Bg1svup4E2JR3Yfgk8KrtZtWX53altXwQ+Auf+AbQlad/2a+A+EIcQ8FWvUQ1ZKuotcKlALQrZXeBNC86yusYvi5I6Cmx2lNRN/y1pcpzUS5V9+lDbnXteYzwJvAC2gQeSvnbeGGtmzhwdrasxryL+Ou2rYasPtx2iWajh249VN5b/fd/wLGy5ccTxt2EplhaoFJQGeQ26DTzs+V5Wj6rdHB3fgdNLa9vug82wIV4kA4e+W0Bj1IY2bGiu0hlY7RSF+hxYZdjx/BeiSCRMK7bK0wAAAABJRU5ErkJggg==",close_pc2:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAARCAYAAADQWvz5AAAAAXNSR0IArs4c6QAAATtJREFUOE+tlD1Ow0AQhd9zcJFIFBQUBCF+Sg4CNlIoQEkZbpBwAYShC51NQKJGNJienIQGKaGxgxQQNTjeQRtwGkBam0yz2tXbp29mdpZOMBwAWAOQACDyhQCwATzRCYZ6898QOkG8R7GWQRmLykdECyKgTWCQN5U/yVm/lVL/DZZWbCxAjR7yUel7i5sQQoTeCegdQyarR6XP6uGXuWlMU3P852tCvd63q4eaMmwwNTXROtau4krywUsIDgCEvdZSY+siWrGUdQZBBWSqK/qbKQmhElvAR7r+8FSIo2/hxGj7PN6n8A6ApiqZkHG3M5p/L4+7BJoZ0U43Xk0VOwTKRkSU/hTZ9eMbIV56rWq7UI2yrmX4hbs2u3dkUkkDDd0gqgm4TjApMmsQNSdkpKdfFfg+fjBqo5n8R59rBJFX+1lRDAAAAABJRU5ErkJggg==",feedback:It,feedback2:Ut,user:Tt,user2:Dt,top_logo:"/apps/static/top_logo-2e020525.png",top_logo2:"/apps/static/top_logo2-f04130c4.png",logo:Yt,logo1:"/apps/static/logo1-41a1cd51.png",refreshtn_pc:Nt,avat:Et,hrefUrl:"",types:we,payShow:!1}),mounted(){this.hrefUrl="https://ai.medon.com.cn"}},[["render",function(e,t,s,a,i,o){var l,n,r,c,d,A,g,p,m,u,h,f,y;const w=Q,v=L,b=Le,C=ve("v-md-preview"),S=Fe,B=ve("Loading"),x=ve("CloseBold"),k=Qe,I=Ve,U=qe;return be(),Ce("div",{class:Ne(e.currentTheme)},[e.isZH?(be(),Se(w,{key:0})):Be("",!0),i.payShow?(be(),Se(b,{key:1,modelValue:i.payShow,"onUpdate:modelValue":t[0]||(t[0]=e=>i.payShow=e),class:"payPC","show-close":!1},{default:xe((()=>[K(v,{userInfo:e.userInfo,appTypes:e.appTypes,currentItem:e.currentItem,onToAgreement:e.toAgreement,onClose:e.close,onSubscribe:e.subscribe},null,8,["userInfo","appTypes","currentItem","onToAgreement","onClose","onSubscribe"])])),_:1},8,["modelValue"])):Be("",!0),(null==(l=e.$route.query)?void 0:l.hideHistory)?Be("",!0):(be(),Ce("div",{key:2,style:ke([{width:!e.isLeftNav||(null==(n=e.$route.query)?void 0:n.hideHistory)?"0px":"264px"}]),class:"left"},[e.isLeftNav?(be(),Ce("div",Lt,[Ie("div",Ft,[Ie("a",{href:i.hrefUrl,class:"hover:text-[#5298FF]"},[Ie("img",{src:"dark-theme"==e.currentTheme?"https://img.medsci.cn/202502/006b10f5a9bf4de6ad9a7c82d83c8607-lQLFMQuVe7dR.png":"https://img.medsci.cn/202502/249ed78c2d9246619edb188e0f086c95-BW5tMjX5qolD.png",alt:""},null,8,Vt)],8,Qt)]),Ie("div",{class:"startBtn",onClick:t[1]||(t[1]=(...t)=>e.newTalk&&e.newTalk(...t))},[e.isLeftNav?(be(),Ce("span",{key:0,class:"beginTalk",style:ke([{opacity:"1",color:"#ffffffff","font-size":"16px","font-weight":"500","font-family":"'PingFang SC'","text-align":"center","margin-left":"-34px"},"fr"==e.$i18n.locale?{fontSize:"10.5px"}:{}])},Ue(e.$t("faq.newChat")),5)):Be("",!0)]),Ie("div",{class:"logList",onClick:t[2]||(t[2]=t=>e.hidePage())},[0!==e.logList.length?(be(),Ce("div",qt,[(be(!0),Ce(Te,null,De(e.logList,((s,a)=>(be(),Ce("div",{key:a,class:Ne(s.isActive?"isActive logBox":"logBox"),onClick:t=>e.getLog(s.id,s.name,a)},[""!=s.createdTime?(be(),Ce("div",Gt,Ue(s.createdTime),1)):Be("",!0),Ie("div",{class:"chatItem",onMouseover:t=>e.onMouseover(a),onMouseout:t=>e.onMouseout(a)},[t[15]||(t[15]=Ie("span",{class:"middle-dot"},"·",-1)),Ie("div",Ot,Ue(s.name),1)],40,Kt)],10,Wt)))),128))])):(be(),Ce("div",Jt,[Ie("img",{src:"dark-theme"==e.currentTheme?i.emptyDark:i.emptytlight,alt:"Your Image"},null,8,Pt),Ie("p",zt,Ue(e.$t("faq.noChat")),1)]))]),Ie("div",Ht,[Ie("div",Zt,[Ie("img",{class:"avatar",src:"dark-theme"==e.currentTheme?i.user:i.user2,alt:"avatar"},null,8,Xt),e.isLeftNav?(be(),Ce("span",jt,Ue(e.userName||e.$t("faq.guest")),1)):Be("",!0)]),e.userName!==e.$t("faq.guest")?(be(),Ce("div",_t,[Ie("div",$t,[Ie("span",null,Ue(e.$t("faq.beta")),1),Ie("div",es,[Ie("img",{src:"dark-theme"==e.currentTheme?i.feedback:i.feedback2,style:{}},null,8,ts),Ie("a",{href:e.feedbackHref,target:"_blank",title:e.$t("faq.feedback")},[Ee(Ue(e.$t("faq.feedback").slice(0,6))+" ",1),"fr"==e.$i18n.locale||"en"==e.$i18n.locale?(be(),Ce("i",as,"...")):Be("",!0)],8,ss)]),t[16]||(t[16]=Ie("span",{class:"vertical-line"},"|",-1)),e.isLeftNav?(be(),Ce("span",{key:0,class:"logout",onClick:t[3]||(t[3]=(...t)=>e.logout&&e.logout(...t))},Ue(e.$t("faq.logout")),1)):Be("",!0)])])):Be("",!0),"游客"==e.userName?(be(),Ce("div",{key:1,class:"userBox",onClick:t[4]||(t[4]=(...t)=>e.login&&e.login(...t))},[t[17]||(t[17]=Ie("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAMAAAANIilAAAAAXVBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9RKvvlAAAAHnRSTlMA9QrgHv0E3Ab6StkT5VYNxryVhiHu1KupcG8+PC6rjG2lAAAA6klEQVRIx+2Wya7CMAxF05abOOnE9B7z/f/PRF2xSQvcCgkEZ39ky3Ycux8/3h1fZvGPuCkUWUJ6IG5NMANY349dFgRmyKSa9iAXjT1dsJtcugzfLZvpcqpCp8oWyJicV+XIv7PzUtodALYXsWAHIrJeOC+1akOAy0bs83qIveo12e0ZI3eK7L3rVwS4FsezWQ72Zkrut4s82+ZUDfZxXE7/1Rht3ZIAujHZAsFJwGAvkMW09YLNb9U04pDo46k/DP1J6stAX0P6AtRXr770JzH7yF9SlfWDZt4ppR9xwdTz8Sbrh6t+Mr+QKwG4K3eWd2kPAAAAAElFTkSuQmCC",alt:"logout_h5"},null,-1)),e.isLeftNav?(be(),Ce("span",is,Ue(e.$t("market.login")),1)):Be("",!0)])):Be("",!0)])])):Be("",!0)],4)),Ie("div",{style:ke([{width:!e.isLeftNav||(null==(r=e.$route.query)?void 0:r.hideHistory)?" calc(100%)":" calc(100% - 264px)"}]),class:"right"},[Ie("header",null,[e.isLeftNav||(null==(c=e.$route.query)?void 0:c.hideHistory)?Be("",!0):(be(),Ce("div",os,[Ie("img",{onClick:t[5]||(t[5]=(...t)=>e.openMenuPc&&e.openMenuPc(...t)),src:"dark-theme"===e.currentTheme?i.open_pc:i.open_pc2},null,8,ls),Ie("span",null,[Ie("a",{href:i.hrefUrl,class:"hover:text-[#5298FF]"},Ue(e.$t("tool.home")),9,ns),t[18]||(t[18]=Ie("span",{class:"line"},"/",-1)),Ee(Ue(e.$t(`${i.types["问答"]}`)),1),t[19]||(t[19]=Ie("span",{class:"line"},"/",-1)),Ee(" "+Ue(null==(d=e.$route.query)?void 0:d.appName),1)])])),e.isLeftNav&&!(null==(A=e.$route.query)?void 0:A.hideHistory)?(be(),Ce("div",rs,[Ie("img",{onClick:t[6]||(t[6]=(...t)=>e.closeMenuPc&&e.closeMenuPc(...t)),src:"dark-theme"===e.currentTheme?i.close_pc:i.close_pc2},null,8,cs),Ie("span",null,[Ie("a",{href:i.hrefUrl,class:"hover:text-[#5298FF]"},Ue(e.$t("tool.home")),9,ds),t[20]||(t[20]=Ie("span",{class:"line"},"/",-1)),Ee(Ue(e.$t(`${i.types["问答"]}`)),1),t[21]||(t[21]=Ie("span",{class:"line"},"/",-1)),Ee(Ue(null==(g=e.$route)?void 0:g.query.appName),1)])])):Be("",!0),Ie("div",As,[Ie("a",{href:i.hrefUrl},[Ie("img",{alt:"MedsciAI",src:"dark-theme"===e.currentTheme?i.logo1:i.logo},null,8,ps)],8,gs),Ie("span",null,[Ie("a",{href:i.hrefUrl},Ue(null==(p=e.$route.query)?void 0:p.appName),9,ms)])])]),Ie("main",us,[Ie("section",hs,[e.isNewDialogue?(be(),Ce("div",fs,[(be(!0),Ce(Te,null,De(e.messageList,((s,a)=>(be(),Ce("div",{key:a,class:"contextStyle"},[1==s.chatType?(be(),Ce("div",ys,[Ie("div",ws,[Ie("img",{src:e.avatar||i.avat,alt:"avatar",onError:t[7]||(t[7]=(...t)=>e.changeImg&&e.changeImg(...t)),class:"avatar"},null,40,vs)]),Ie("div",null,[Ie("div",bs,[(be(!0),Ce(Te,null,De(s.message_files,((e,s)=>(be(),Ce("div",{class:"documents",key:s},[Ie("div",null,[Ie("div",Cs,["image"==e.type?(be(),Ce("img",Ss)):Be("",!0),"image"!=e.type?(be(),Ce("img",Bs)):Be("",!0),Ee(Ue(e.filename.slice(e.filename.indexOf("_")+1)),1)]),Ie("div",xs,[t[22]||(t[22]=Ie("img",null,null,-1)),Ee(Ue(e.filename.split(".")[1].toUpperCase())+" · "+Ue((e.size/1024).toFixed(2))+"KB ",1)])])])))),128))]),Ie("div",{style:{flex:"1","line-height":"22px","font-size":"14px","overflow-wrap":"anywhere","text-align":"左侧历史记录列表ustify","letter-spacing":"0.5px"},innerHTML:s.answer},null,8,ks)])])):(be(),Ce("div",Is,[Ie("div",Us,[t[23]||(t[23]=Ie("div",{style:{width:"2.5rem",height:"2.5rem","margin-right":"0.8rem"}},[Ie("img",{src:Mt,alt:"resAvatar",class:"avatar"})],-1)),Ie("div",Ts,[e.isSubscribe?Be("",!0):(be(),Se(C,{key:0,text:(s.answer||"").replace(/\\\[/g,"$[").replace(/\\\]/g,"]$").replace(/\\\(/g,"$(").replace(/\\\)/g,")$"),style:{width:"calc(100% - 44px)","letter-spacing":"0.5px","line-height":"22px","margin-top":"5px","font-size":"14px","word-break":"break-word"}},null,8,["text"])),e.isSubscribe?(be(),Ce("div",Ds,[Ee(Ue(s.answer),1),a==e.messageList.length-1?(be(),Se(S,{key:0,type:"text",onClick:e.handleOrder},{default:xe((()=>[Ee(Ue(e.$t("market.subscribe")),1)])),_:1},8,["onClick"])):Be("",!0)])):Be("",!0),Ie("div",Ns,[(be(!0),Ce(Te,null,De(s.suggested_questions,(t=>(be(),Ce("div",{class:"tag",key:t,onClick:s=>e.defoultAnswer(t)},Ue(t),9,Es)))),128))])]),s.loading?(be(),Se(B,{key:0,color:"#1989fa",style:{width:"25px"}})):Be("",!0)])]))])))),128))])):Be("",!0),e.isNewDialogue?Be("",!0):(be(),Ce("div",Rs,[(be(!0),Ce(Te,null,De(e.messageList,((s,a)=>(be(),Ce("div",{key:a,class:"contextStyle"},[s.id?(be(),Ce("div",Ys,[Ie("div",Ms,[Ie("img",{src:e.avatar||i.avat,alt:"avatar",onError:t[8]||(t[8]=(...t)=>e.changeImg&&e.changeImg(...t)),class:"avatar"},null,40,Ls)]),Ie("div",{style:{width:"calc(100% - 44px)","line-height":"22px","font-size":"14px","overflow-wrap":"anywhere","text-align":"左侧历史记录列表ustify","letter-spacing":"0.5px"},innerHTML:s.query},null,8,Fs)])):Be("",!0),s.isTrue||s.id?(be(),Ce("div",Qs,[Ie("div",Vs,[t[24]||(t[24]=Ie("div",{style:{width:"2.5rem",height:"2.5rem","margin-right":"0.8rem"}},[Ie("img",{src:Mt,alt:"resAvatar",class:"avatar"})],-1)),Ie("div",qs,[e.isSubscribe?Be("",!0):(be(),Se(C,{key:0,text:(s.answer||"").replace(/\\\[/g,"$[").replace(/\\\]/g,"]$").replace(/\\\(/g,"$(").replace(/\\\)/g,")$"),style:{width:"calc(100% - 44px)","letter-spacing":"0.5px","line-height":"22px","margin-top":"5px","font-size":"14px","word-break":"break-word"}},null,8,["text"])),e.isSubscribe?(be(),Ce("div",Ws,[Ee(Ue(s.answer)+" ",1),a==e.messageList.length-1?(be(),Se(S,{key:0,type:"text",onClick:e.handleOrder},{default:xe((()=>[Ee(Ue(e.$t("market.subscribe")),1)])),_:1},8,["onClick"])):Be("",!0)])):Be("",!0),Ie("div",Gs,[(be(!0),Ce(Te,null,De(s.suggested_questions,(t=>(be(),Ce("div",{class:"tag",key:t,onClick:s=>e.defoultAnswer(t)},Ue(t),9,Ks)))),128))])])])])):Be("",!0)])))),128))]))]),Ie("div",Os,[(null==(m=e.$route.query)?void 0:m.hideHistory)?Be("",!0):(be(),Ce("div",{key:0,class:Ne(["backImg","dark-theme"==e.currentTheme?"backImg_dark":"backImg_light"])},[Ie("a",{href:i.hrefUrl},[t[25]||(t[25]=Ie("img",{src:"https://img.medsci.cn/202502/aa9a00f8e05a4c5b86e00f493fbdac51-KU2AOKigINI4.png",alt:"更多智能体"},null,-1)),Ee(Ue(e.$t("tool.moreAgents")),1)],8,Js)],2)),Ie("div",Ps,[Ie("div",zs,[Ie("div",Hs,[Ie("div",Zs,[(be(!0),Ce(Te,null,De(e.fileList,((t,s)=>(be(),Ce("div",{class:"documents",key:s},[Ie("div",Xs,[K(k,{onClick:Re((s=>e.handleDelete(t.upload_file_id)),["stop"])},{default:xe((()=>[K(x)])),_:2},1032,["onClick"])]),Ie("div",js,["image"==t.type?(be(),Ce("img",_s)):Be("",!0),"image"!=t.type?(be(),Ce("img",$s)):Be("",!0),Ee(Ue(t.name.slice(t.name.indexOf("_")+1)),1)]),Ie("div",ea,Ue(t.extension)+" · "+Ue((t.size/1024).toFixed(2))+"KB ",1)])))),128))])]),Ie("div",ta,[Ye(Ie("textarea",{ref:"myTextarea",class:Ne([{textareaStyle:e.contextDisable},"chatInput"]),onInput:t[9]||(t[9]=t=>e.chatMessage()),"onUpdate:modelValue":t[10]||(t[10]=t=>e.message=t),placeholder:e.messageList.length?e.$t("faq.iMSL"):e.$t("faq.questionTemp"),maxlength:"1024",readonly:e.contextDisable,onKeydown:t[11]||(t[11]=X(Re(((...t)=>e.sendMessage&&e.sendMessage(...t)),["exact","prevent"]),["enter"]))},null,42,sa),[[Me,e.message]]),(null==(h=null==(u=e.fileUploadType)?void 0:u.file_upload)?void 0:h.enabled)?(be(),Ce("div",aa,[K(I,{"file-list":e.fileList,class:"upload-demo","show-file-list":!1,multiple:"",limit:null==(y=null==(f=e.fileUploadType)?void 0:f.file_upload)?void 0:y.number_limits,"on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-remove":e.beforeRemove,"http-request":e.customRequest,"on-Success":e.handleSuccess,"on-exceed":e.handleExceed,accept:e.checkFileType()},{default:xe((()=>[Ie("img",{style:{width:"25px",height:"25px","margin-right":"4px"},src:"dark-theme"==e.currentTheme?"https://img.medsci.cn/202504/ebbbbbd8d4ee4beb9d2578930ac69fb5-NxjOfuavyQ26.png":"https://img.medsci.cn/202504/ed70d9316c644dfbb586bb23f55a7431-Jz8JLYrqoqid.png"},null,8,ia)])),_:1},8,["file-list","limit","on-preview","on-remove","before-remove","http-request","on-Success","on-exceed","accept"])])):Be("",!0),e.messageList&&0==e.messageList.length||e.message?(be(),Se(S,{key:1,class:Ne([{textareaStyle:e.contextDisable},"chatButton"]),disabled:e.contextDisable,onClick:t[12]||(t[12]=t=>e.sendMessage())},{default:xe((()=>[Ie("span",null,Ue(e.$t("faq.send")),1)])),_:1},8,["class","disabled"])):(be(),Se(S,{key:2,class:Ne([{textareaStyle:e.contextDisable},"chatRefreshButton"]),disabled:e.contextDisable,size:"small",onClick:t[13]||(t[13]=t=>e.reloadMessage())},{default:xe((()=>[Ie("img",{src:i.refreshtn_pc,alt:"icon",class:"refreshBtn"},null,8,oa)])),_:1},8,["class","disabled"]))])])]),Ie("footer",la,[Ie("div",na,[K(U,{modelValue:e.isLightTheme,"onUpdate:modelValue":t[14]||(t[14]=t=>e.isLightTheme=t),"inactive-color":"#858585","active-color":"#D1E7FF",size:"small",style:{top:"-4px"}},null,8,["modelValue"]),Ie("span",ra,Ue(e.$t("faq.gentleMode")),1)]),Ie("div",ca,[Ie("span",{class:"linkStyle",title:e.$t("faq.beforeUse")},[Ee(Ue("fr"==e.$i18n.locale?e.$t("faq.beforeUse").slice(0,37)+"...":e.$t("faq.beforeUse"))+" ",1),Ie("a",Aa,Ue(e.$t("faq.disclaimer")),1)],8,da)])])])])],4)],2)}],["__scopeId","data-v-cf0bf394"]]),mobilePage:fe({mixins:[Bt],setup(){const e=se();ye({title:()=>{var t;return`<${null==(t=e.query)?void 0:t.appName}>${a[localStorage.getItem("ai_apps_lang")]}`}})},data:()=>({imgpath:{},avat:Et,lightpath:{delete:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAudJREFUOE+NVV9IU1EY/33n3lkGEriEMoowInPiBougnia9+FA+RGtL6XVBYBTWk147uiJ66KUgyl76h14b0WMPQew9ppu4KWRYUAaKQWnpdLtfnM3pNrfZebmX853vd37f7/tzCGUXE0Bc3l7aQpUcmryyqnoF+oIdljpnX4CoWcRqOCxT5fwKAaUUnjBEXV0Tx5Go0zXtAwFHAOSYqu+onra1NaBhaX4+QWEPLEiZuVCtsgxdHXdcbFljBPQD/AUkmJmdAK4LQYfGhnq/lmK5CSilcMb145YgOwkrScytAPWCcZYhZpWzAB9j4ldg6mbBUUHYSWn+PjZijK/zU8JnxXd4Za2uaXEAe/NuVlrpRUzWANjyQvy89Gu3Y/rd1aTCKmDYPKXv14BTAA8xc6dNWNEUhKbDSisA9c8pSgmdTzPTQ4utM0zaxITZ8y1XEQWAStxmX/CwRpgmoDVqGuFSOrX4BvxENJxKp+3xkPwJKUUuMYWAAFxTOMjQZgjcGW20TPfsPi1S/yPDsCkOPeFAqmVS6yLCfT1ts0ccK4uQt3grw0It54m4Kzrc98gdeGKLDAYydecODOqRwctrLn9QMnAz1piuyTLbbIK8ssluugMBW+r3gUUm9I8PG3fzD+fCd/qCD0A4HzON+uxeBUBldvqD8yB+ERvu6z7afq8mWbusMotdf6AnQnLJ6R94ySD3uGk0rdfyRouWLGyn//YMwG+Z6CMxPwOgANVZnSDOMaxLABpipnGiOIIiwCx1pz84CuATWDOYrHYi3mTA6Tcg7TlbtBwb6W3zel9rodCFTNK2tt56+lsuBt+DYRs3DU+psnH6ByYJNBY1jQ6PR+r5w6KAYc7o8gdDDJxk5htEwgayNhiyhRQRPSZgKGoaV/4P0BfsYUIPgOoyY+ovAdeipvG0csjr3urWuTrs2YGqDW3ygZNY1RIOzOWPrW3HV7kBut1+hYmtMg5A9hcNYdVmmXyWfB7+ATv7SSTTVmO0AAAAAElFTkSuQmCC",user:Dt,feedback:Ut,logo:Yt,beta:"data:image/png;base64,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",open:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAaCAYAAADWm14/AAABUklEQVRIx82WsUoDQRRFz52dpLHRQsH/8BOErKQwiDaWFoKF+QERJD8QTCytUwgKBoR8h98gbhAsBTGbeRa7i6nFnfXCwMwU896bd+/lqTOe38nsCDBARISMiZfZIRDKO4uZgIljD+RAK2bg1Rw8ximOfWANWEZqg4CFGQ8NFb6SSTrOHKHkgGDW3641YHf0hg/CgIUPrukPaB7aHb6QYIpqAKXel8i894mrfGB2Xm//K6TjDAEenNLBM0ACxCZEAJae9c0LsB7wFdGKDWiDpt7QlcBHrr7CTuM69MIugV5EG+anBUyVDjKaJeEGCe3iQIDZWf1STK+zauuUDjNiDyJFQOFIos4f/xPau5m7Vt4KQuQu56m/VWvAdPQOlhQH99m4DaDO6PVE0kE5F8YghZWy/yDYoxe6xeKroChfXV9KMPzBc7+Bd2ZMSheMuQQ4Q/ff7n5fmUgrqF4AAAAASUVORK5CYII=",edit:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAApJJREFUSEulVM9LVGEUPfcbNRfTwiiaN5BQ/RPiIiIitTAimMCgiPYzJrjOF22j8o24bdVGoXKEnKFNQe3aVpCrWjivH5hgEtnMd+I+feNzxvcc9VvNm/e9c84999wr2M8hxb0HcV2x+rnr0oS/m+Fkz/ikQIS5GaZ++0sXRerVl/ne9/o8e03qByMgJTcLo0AD3vdHgvodAKsAb5UL2WdnXXa8dqUWJWm/gk3l+vHgpP8QwjEAapEB8IeUkcpoZq6ZpD2CiPILnj9pwAIAteMfgE4AKQBrAG9qJdGe7E6ws3ItZA3kDZhUP2jHAVArgchIOZ8phSTJBPHK16XO6wtj2ReBZZ5fAji84b28Ky8fPwNNGCnxBEnKN60IwB/7l2H4BMARfSZ5vzKavavgmradCRKUh81UsCHPH7bAjIDdG+JRLOedQgi+8Vfz2ZvypwDSAYSl17fijAUDF8FoIQib05SW9RjlQYIEmFooOPlg2HKwak2oe4vgoMonwOj6aCFoKC9+mzC07mbO9628tYKgaYuHgPRnAr2acxK3K6POTCQtrZ7HKI8lINIfAJwG8abvV+acNi2Sll09T1x2WgGR/gTgpABfLeBRTNrQ6moIct5Iyy7KkypYBHAissj0ru6dxLTErf3tMSVlsOjPAdCxDzelRk5ATB92MoUfHyGrDuRUT/A+9oRxbYnppekvPbVaV7+BNXWry0RSEFmu5J230XwngUffSTh1ww+Wjta6JQdKByRIUINct4qBDSbW0jSGqKWhWqfYFC1Xuv7K/Px49mcDZHCqWoLdZk27IpvvBf0CZK5cyFzZIvCqpSbvD0Yg8rycz1xtWDTkVY8R9nziCm+TUoTsXJdX2yxq89s9X/sPBHezJ4sgaaAAAAAASUVORK5CYII=",robot:Mt,send:ga,refresh:"data:image/png;base64,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",copy:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAEnklEQVRYw+2YTWhcVRTHf+e+92YskybVxphpmopFKcWFaG2tgiCt4BNdFaQIdmdBpdrUhZIu7EKk7cI0tqUIblyI6EJXoq/FuqlW6YdI/SgKgnWaTPolTtomM5n37nHx5o2TppmPJASF/DfzmHvPvf/zP+eed+6DBbQGaWaSfyBPigm6uEiOXiNoU3aNoJHY0Wy7pm2JW0aKBDuy1TG3IanBPOVimVAM+fSdJmWMBVBVPtt2+4wI+YfysSqOypKLfwugNzKp67m/Lxc/GEFD5PCeHn1i14WVoto5S7FEkdCqOasqY64JxUikAJ9vW96YWKIYixAMyjivA/1AB6DNpsI0CIHTgvYB3wHmi1eW2eYUGzgHXtoBIqzdhMgnwChwtkJsRmoBZWApsBr0RKl09THQcc/LyJG+XqVhjjkeiCiqIPIwgCqHdDH95gIpJrBN0auZoyk1o/7oRNuxtuWOcb4CWZdOt90LcgowQDSFmD+YTyIkGDG4rkUtRBGIuMQjObkGZIiC/mxUV/H38rE+imAAReVaJB1vfw3nzgyxuf8icA9izI22ZhIpC1gBD1ilEeWyVkglOdESgq1Z3DEXd9xV97qLc90xqiLRh8+Dv9UFTYSZovukHPP35+G5bhj8CTo7N6H6COAAZcQ8Dno/cBTVU0C6iTzTOCqSQ/gYJIeNDIVLFhu5LM0eA1mP8BBwAnCCl7OTQ+kP5MHB8NGIpbNzJ/AWIjUnr8phIyIbWxDOViKzBfQpHOc8d98n/PZ9XSO35skQYYFe4AWgqMJeEfJAqkJQKhs1eyIFKGHZDGwA3SKY3ZRDVOpXGrdmiWTmcpRe4LgUCm8iJiKTkaCvp+Xy4L8z7FROmYfIBqBbcWGNq/xR3/Zm5SJJcoPpcLgDZQzjH8y3XrdUHaxGcZ4KMUmFocamNyMm1d8yliEsi9FgR3YGilUZ6KTlnca2ps6YtpRNcwwzB2ssEPtPYIHY/4NYBhAj9frB+SZmsB78WHBJpaSmGOnUifMKLSJlSHVE2NCCJK30lELf8JY0R4hDJuZBZOJFro+otnd1i+oDQA615yvTqsrNF7E4MsoGlI0xU01aqndx3RyKoRRWLyPzRSxR4geUL4l75AjhOJf5lD4LH2CDV3uqBm6dRQQX4Y1uGBiW6h2zNUil6a+sqUcR7zUQgxYtq9LghML78bg/OEzQt2xaYskRDv8MCDPPjnAbRg/vmEk/NqQVP5NWykUFXAVxhF9Dg8Vi4i2Dvpt9Ivh32yTOXSuemejh0RXnCX7x/F2nbXN0EvcExq4au2xlJIW/eiUhaCJQgdBqsL1n2ltWlZixWK8MwM+lNCeAdRjvCN/kr7D4Vm+mDZApXBaQ1bHz8i1i4+tcVL8pm1R5n9w/bCqKrVdkEFgzywOSnLwCsJtF7MUijKO1YatLzD8YN+FqHbGhUUQzxjGrRcSZbbsoVq6c3HPy97U714qGWl2r9rNTfcUODFf/V0cSjwlequ/ddHj64CWkchuy1ppSqWRVFc/zaFoxAH9fnuKSIqVyifaxdhFH5uSVJSqaO5OzXXd1kWpLEWyfmaMLqId/AFb5wHRerRl1AAAAAElFTkSuQmCC",like:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAE8ElEQVR42t2XT0xcVRTGv3PfG7BtbKkF+Vu1NSIaXWhjG7e1gYdpTLRbUWOTqigMKTZx60I31pYZiP23sGnShSaNiUEZIBoXxkSMxmgaLTZWBpgZgjYmKlKYd49n7pykUCAII4z6S8583MvMvO+dd+65d/C/JugaN7nAv4ngrTTNG3enPQlCgfgolFnfD46P7QbRFIz/faKt8s/mWMoAYBSAwWrQzMCRbYHnfQpjvgLZr4N4+gm+icz+NyapKMYSbdVhEE95ILRCYOIkGPUALuAajvQeqeDG+AQVYKyAbBE9DmCXxLds+W7RQxIzEq83xVJ7B9orWdSsizGpJ8LGTdYNGC2qZweitdOJ9uozAHogGDLPQIiYCK1PxjzfJA5u5iCe2QPgMYkREN7Fdd7Pe+XbIfS2VYSNx8dp7Y3hD6t/HFK9IJkak6IvdSPClM6XBK+m/fxFzNq2iyCWJjHBQVd6J8AHAGTBOIU8oSoBil2vPkbwnBmDp0S3AHw+Ea0ZDmKTBMrygneXwARnRwm/cm5nYMyDQSDu66jlgozJSjTSIrJBT7oGFk/qtU/kJWtwHdbXLOJVs4kJ4uBYxiYOV/ES30vafniVGSNyEvKjILoTwCdI+p/pI7PwoOZUCdv4lfHmAGMzQNbIauaF30gjfW3Vw/u7tRkvZ6wpnvEMEbi0xMJawDISreVh0H3VA888r0k5mThawVL0XqLdNVs4mA1AswAayPN6ncUlYMDK589nw2wngElXw9FqXtRYc3eK+tqqwnnpjk94IiF49gDAuwBckngPQs4Ucli2ECi0F60xJ8nQfQCml1j57K7Nrjm3wIByWkpQbribvW/+TB93lnNTPL0HzPsIqNDV5klMgajRdXpGu9xZd24HyG1NWCHBMcnMYbe6d8NgEMBG5vCe/mjd5abYOPVHa9mH0hSbMP3RciuP5CCAUyDyRHmOeat3PwqyvfrYeMFF42OUz2QdYwlyppx2VA8FsdQXIHqEyO2zl0VJlP28qZS4rLTSocsAeg2Ax6DTZPhLABskppipnphfBpC0I9dGtHMx5qGG/ibBiaSPLLa6YgP9ok1k7grSkYcGAJUgXKQIv5h4qfo0GD2iZwj8DvJEzJYN3o2VsNJGjRzTfgRMO93Gb+kHrT5WY+rLDcxm/eeoNSaUnuU5u/NXsHWZKgALm1fDO0TKmOhKWFryG4T+aM2iq5JVaeC5ShYNpcChWPxDkNaRaIMbs/3poxfKZ+dv4kWAYEi1Xme+g9DYM0FFNcYgUt2uE99A8Niaohrz/YiFQOA7nBIlAX24xTIWxMbNB63bbNCVvAXADglrYa/qouCiGSPS+jKRKpHbJJJsw4w27OIZs8RQzWVsk0RyoGN72s2hiMaIiVRrdOpHPZf5g9G6ZY0R1ggDz2qDv9cpYcTpMj9GPNXZZVr3qvmw/Vb9NDW4V2uTqrzAGDNDdUK3qIbGWKpMJwl5uNC2LIdCctqTfoiARldqMEPabC2w1HnsaNqPRDBEhAcADINwRd+TlSgD42HR30H4XOcIy7H0AXGrxDk5aD6dMyy6+Jm/uTtj5OSalSPQswCdA3A/GHctcvGbwdiHwrASb1vYTq235YtcV0gEIR4Eo2xORZFGQVXGYLJkLw121I1gJTR1pQjrQHM8Q82xjMFK24Kcxw3WCAvLA7kfuv9F/gJe0gpJ2a89bwAAAABJRU5ErkJggg==",unlike:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAE+ElEQVR42t1WXWhcRRQ+Z24SbFWotrpm0yRV0Lb4A0VMQXxRY/ZGoSCCPlhj36ohySZ9Mwp9EV80NruRWISWYgRB6EvF5ib0WbESES02BLU1SXeTVmNaaEuS3Tl+s3tY99ItYZdkLf12z/1m5t47890zZ2YO3apgWgWxwRlmYtZH1xhCQe9WS+WgPTFn2pNzTFVA++Acl++xQzPNTLwdRaHKYZQlb9rGtEie92PQXb+yqrBYMsVjPVFpS87eY6hmgEje1I7XGqLj/0JiO4J4w0/+UNpApCWg5kal13IMUYNE0oHiP2icAGdgXMHgtbjuBt9JTN+CL+u4gt+D4MeZzNE9H6VbTnTXZ0pPWyLFyo/5yXQWtgBrIcAfTJclCu8Z5U2ws7DrsJ3av+e4NTG7KZZM/4Z2iSUu7CIAbEDhKWLUlZ0YIyTjQU/9aQKC3nqhNQIz5/hUfOsii0zm2ySS53BQao1ZuUlbci+9mJw3tH6oU86GpNwkMpodMfGvji3Z9RQmpCgp7IXELAf/Bd9DBAhzSlmoyjCFgk58W3KmHtQEu8qSWSCAJeTl6gpjSNNt4gEVNm0sz+WFVd9jNaQQYla+l0mc4HMnexsWXhqeN990RiyFUc2p1CAU26SBf96xzUBkdREWJuxZ3Uee0JU4oyxUXYSFBV33iXpqJzmITCkLVR/hs/L5T6drMXXbWJhEN1ewrLkjmDx/+CLZq7MWNVsqoTDFZ6S35N3Nwu5gXTSWz+UfMGuRjBrd2TO60rJB5/1Zc1e9R4YbCbDEV3TxFXmMczXB6w+D61A7Qxt4hYAgXvEZKcoWtgzbSEx7/U/SLnY3gK/LCj/JJI+iPg/Zk/qW5IWpLvXOZr25ELylp8AqwACcj9EbPoBVXAS2Bcao9cNMYb3hn/ekvDve17AYS8ybsXjEFoQJoDzFzE5Qi59IPwVv/eDSnZtlFqUFKdiwLp4YMbtNe4KExsEbdVo9Ir5ETKeQwXz/3MDfPBbfbEnBRfkT4wEBj6C6l1ZPEC3sDhE+Y6x9b7Q3egUfw8VTj77qQD/DtsNeQ/9foc0DZ0M5/1CKR7ujUnpVSoEPaAy+jmvraqkxkzwjHn+J8ne6UpBgzmPwCAY3L0O/EzVBTMd1ROsfTrN7UpZwFaLRbhVaQlghyGtN7V9fd2/piA2m3nfpDxOXWmWWSeogaADlHTAb8jxEOUbzfv2Ew85L/hC81RkSkV19H1Os4IcOXAo0hSosDP/jaQ4ONIkf8Zn6j/YTMxULK9oanoY9C/udvMxJnX0pf4NVQJA4hseYddQwhP1jYunC2Vpifb84BqUg8m1t+CLoakz5yVRN0B3NVCAsjLHeqJTKLv1DMxzsY/EPTkDAJglrFgR/VPzE7CMu2GGXycrnei9b3hFRKYwpsZmyp7RfP/p40NvwRy7u4g2yrsKscnBwV4aIl1XWRkcI8CU/eWkriq9QHp+FxllPYeN9jdI+dNHTc+1Pbd5DBWRexaUZdsJtnP6RNFOWbPnZRSWwVhwZoWPC9AaKXZiuSSN2BAr2kQPbkRxfExP0RbPln/wVoi05z+M9EYGgd1D9gABmmRbhJiKZIFreHfRsCwmqSvA7Ua1HZpiW6UMSxBTTlBOlMTfsRPlDcx5VCFauGO2JlBmNRy08t4Es7cgtBEOncZKs0P8Jl31AlBdqGz7PdCsBIo0zup3xLxiUFQRBAdGQAAAAAElFTkSuQmCC",empty2:kt},darkpath:{delete:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAhNJREFUOE+NlcGLTlEYxn+PGWIxG0PJlEQWzMIUKVaj2cwCC1mI/ANKjYYVU2ZlZUNJ2TCEmiZ/gJK9QiILhMKCmo2JxjCP3ts5X+f73HubU7fuPed9n/Oc5zzve0XDsC1Jblpvmldbgu01QD+wnOJWAb8l/VkRoO1IiCeYbQQeAzvSd2DE/DNgHFgAgtCypLxhNVE7bI8Az4Fp4GMC2w2cBbZK+lSX2AFM7PYCg8AicBC4CBwGvqbkncBdYBJ4AawFvgAvYz00Vxbf9nrgNbCp2Dm0Cg3LsQSsLibeA8OSFiusvJAYDgEHgHvAycSiD/ib4uI9NhkDrgGHgFfA5+yILsAQ1/Z24F0cWdKTOp1sHwfuhzyS5oNMvphehpG/BfiQGD4ASoZx/GB4BriS9P4RF1bHsDJy0vJ7JEm6bjv0yr7rl7Rk+xJwHhhIp+oUQckwAwZA7Dot6XJdxdi+ChyTtLkyZ1FV/wGmgGA4I2nS9gAQNxsjGC7YvgPskbSrd8NaY9sODR8CT4FbCTBiQ8OjwClgm6R9rYCFJ6O83gJTwJGi9ILlHHAb+CVp3HafpGyr7tLL12/7UZhX0miDbd5EWUo6YTtk6DSLriPnRduzwH7gXKqKso1F8o0wv6TTKwW8AMSzrqF3/AQmJN1sPXJRhiH+hqLkenHD7N/KtpUDWhtsU2trm2/rh3mtN6bSs+n38A+1xvgTB37p3wAAAABJRU5ErkJggg==",user:Tt,feedback:It,logo:"data:image/png;base64,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",beta:"data:image/png;base64,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",open:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAAuBAMAAACGzGfRAAAAHlBMVEUAAAD///////////////////////////////////8kfJuVAAAACXRSTlMACvXr4YqHgQzwZSlCAAAASElEQVQ4y2Nos5yJE0zOYMiciQdMYwBqxqOdkHQkPumpDOJOSjiBSiEDgyAewDAK6A8IRAmBCCWQHEbT2igYImkNf6FJoMgFACgPDt5gKlJFAAAAAElFTkSuQmCC",edit:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAdFJREFUSEullT1LHFEYhZ+zmsRGsEiKECFgK1ppmjSJfyDJb02XShdioSAWKxYi6G4gsJAildl8+MpZ7l0mk/ncLCwzzO4955lz3/e9YslPREhSeHnxviynJfXnosAL4E7StzqT3gZZKCL2gF0bAJ8ljatMehukSPaT+E9gAPwBjiRNImIg6T4n09mgQJ7FfwMWWgGs86tgstifzgYF8p1EbOoh8AzwM2+4nx1K+pKBWg1K5Fnc9ENHkowPgJcprqmkj70iigjHYvEci8XHSXwTeA08AVaBM0nnrW/Qkdzib5KwN3sk6aRY+o0RdSC3uDfZ3xFwmpuvNqKe5BY2+YXJO/dBX/IcS6NBgdzd+SrVdblacuat5JURuQuBD8A64C49lnRTqJa/Mm8ibzJ4BzwFbiV9qhBvzLxxmqY3eA9sAN+BS+ARsJ2u82rpQl73BhawgSPynDGtSznf11ZL3dj/pw8iwm2/lbo2r5vHAuQm8rr5YVMrnA6jKoPHaYDl33ydSZouczgtDCLCsyQPLJdnueM9Z1rJ039cgRNJs6LB2xSNR27dCGmMJRH5P07hStJhlYEPjtYx3hCXDVx512WDNeD5MjnXrPkq6cf/kHZieQBxEhkoRFyyhQAAAABJRU5ErkJggg==",robot:Mt,send:ga,refresh:Nt,copy:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAQAAAACNCElAAACZ0lEQVR42u3Wv2sTYRjA8e/z3qVSRR0UKdg6CA7FTaWoU2lGtyI4Fdd2UTGXa1JEqlabXFpQEBFnFwf9A0Qdq1QFF+nklIoiFKmD/ZX3fVSOSpPmLiVdLPgZD+7L87683L38s4QEoVGhJeemlVYKhi25LqPSYrJApjQ8qgdJJ9RkLvoZSqSJsZIUNBylyH4UIU2N91yJ3oQmck1joRfZcJCn/GAOTZ1rjQP0Mmv7p5fyUlHwaWAUOAPyoFy81FFzSiMFwDf3V3Pd3iv6zHHeYbB1saviG+OA+GkVOm3ZNtt04YbmBB5/vviNY2ogZvhrN5EtKQA1UtzUZUIjAud9fBAltnGyCc0PylnnhWuSVRgMj+iuUKmn+FrlSVTNG7CKAtIQu2YmXDjGbRQBBTRLlmacGB0KzlXmx+UrsbpY/ncq6GGYZS3zhQ4EwaFsJqzIBQbMEJMgzWIISDc9zCzeemT/HFmaiw9OhgG6YFxHmsbWN910eGO6ZvKJMfWweIAFIDEmgFh3xwWaMhmAksCwkSptSIjB/9hOiokgsP2YgaLvCQrodmPLsNeuOtzGg++3szrQU/mR77qvixNUdR7QdmMGZIAsoAgPp6o5U3HtxhTkg74gg9WZyjOYdo3LVEA8ASRIXSIK+jIKC6bk4n9s869GbbYGpH01FKitv1cQZ8TB5pgDDvUffju/khl2SWMtGSw9xEFKioXGmAP30ZulT56HC2Q6lUSh0Au8BicJt6DATLngtLnLSXzSKMIik1H5stzThFi8kcEe6cVDSSUL0ad421MEkhO2pGhofdkbFWdoSTRy7DC/AHYu6d3OkBPfAAAAAElFTkSuQmCC",like:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAQAAAACNCElAAADIUlEQVR42r3WTWhcVRTA8f85940axTqtiBLwoxHjKLrQYMWtKQVdCNqFG6OiUBRBYmbumyJdKGLMzGtFsGAbF0qhCwURpDs3gq1gxCJK0cZinamgUhRBjSHz3j1OHagPmrwhM21/MNs/Z94974OLzKtXzo9dAgB154U+Ivr4I6ptYSn9rvGPV4xCSoGdDkam9LAeLX3lH4l0WijkKHDYYifzjNKWMXnUlvd8GsuRwSbzDuxhJvjG3Wo7WGHW3980rwPEanJZAJkC3n1tOXnb9oI8CSoDxFRfsfheHqKl7wPIR8CN0Mhqsu4YAdgB9sHcT/5SsCXgkmejAU6zJk2rjrGdlP1ABgiADbJn6kjd41zFwWSxLsHokZK+JH+r/79pifWJeW2mtVEeA94CUwAMSI90jlot7LbcLgrMWUFMBfRBbuaT8mcgwRRQ4OrJByZXyE1mrbnFXbLKZLEDFwyjkcWOZ0D2vWh118g8gNKhIocQciTEB5ernPaSWC4WSzM7G83YzoQdTz+kGwYNEB1L93EHy7kjMyKZYEqFKQFAAGBGXu9uFVu5hgzHEtu6seeTN8/MxRrq0jC/RT7mcrstOXFmtggg1maIn2Y/DkOAgHJKDoEZ9HiBxMhpGCQL/guZZJwTCBb9t1NhpsyrOObtS0ZYYlxqtE+3IBg9uVDeCxEbQX4DARRMQCpca8c6zyXz7O3eh+8BpRFHoVggKskYK3wPGCgooBtAT23Iqg4HEgEhGIUyur/NlDnJn9DMnaYB8rLRW4MA/algrgL2Y9IBAGVgIiDjIN9CLEPGEOB6sK9BdMhYFICbQNpgw01W09kws4nNBH4HbKiYCOh13ECbX4aPAbqJK2g3fwYbfjIbBX6AOErOiQnr4AJwO1gLDMjHHNCBPKPIbACpAG3AzsYMsF/BKtUyIID1G3SnQHwP2wi6ABrI/7XpqLQgd7FoJxFSynIff9nnpGsWew/GjRxoPjEtb+TfAXVtpP4pDnCn3ILQc6VspViQd9IqlFa76L5kd0uZAAjS73Y3seN7WqzOC+tWl7qy1jp4ZR3MdtsF/Pr9FyjFJCK5RF9iAAAAAElFTkSuQmCC",unlike:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAQAAAACNCElAAADKUlEQVR42r2VTWhcVRiGn/fcSWyiQv2BVtDEFmxa/IEqpiBuhII7UQS7sEQ3UgU3aebepKmFLmo1507EneJCxAqC0I0LF8WlWlpJ8adoCGq1haKoMSlotZlzPscJ5WCHGTJM6QMfXPi4Dy/nXN7L1URcQS5EF5SxzSJ3k6JrJtU+2TAjGJ1wgGGAsyWdKldaZLlKm7ipMmvP4FgrhviaMf9F4XyECiRr9rqN8YfNUUdtX+/TDq63z1imgmkT9/J2MerrJKqCiXuKUCzmo+kUWs8UquuLb4uLU9ugyGBiffFdYfn21Z2jif57HMXpWHkSZmytH8LsEvPABpo40mYIrLEqHN3QD4T/yURjhoFvgO5kBkCSjcvXwTYD5wGje5KsIqjepiH+jItg9CSTwG1kiLPuZ1BvyRBwM44zfnHa+dhbMgMbAn6EuoOeZFkE7gPOAdaj7LAB24CF3mQVmhR93Ak23xhjDZhrTFblhvhXTHXhoCqo38gmljgDgs4IR1AdFGphObM7QBcaA1TAhOku+jmtFfBGewwsconBsDs/Fwfyi3qAu/nF5gFryhyNuQVY9HVa2C942VhFmDZwK9I0DjBEYH+5lPrMgAXqjBYP+s+n9KqRSCLACfQoG5njGIMEMn7lY38iV0MFCKCQt+IIu1uKMbJOp+NLtQu5SgPY2599pRHtmvlgMpsJcPntdJur7MV4Wjtbavlh9z7H5QhF5kPlCUaYs6NAnJIIDnwAkswb6LeZsfyQDSvdWqRfs2y1CEZVPjwu9oC9WYbLuQgkUrJIrnKBhVTkNbtfO6eBCOAcYctDPML39hFEI9EqK+2KH7AOxt/7mnsBhAi8ALxXO19UfL29LAktJTtoz8cBYxXNWr6FXSzzLhA6VVAblIo5A/ZQ0VH/Q6OXrUtZBN6ocwk0COU/xe16Euwt6HPQpew1m8qAn8AeA7CnGOZDf+KAYuxaBsHA3gG9mD83vk7Pgh2Bv13NaIPowD69Yvk+HQbOMsQcO3ygAxkd+IQJZcf50rZrM3DAnyqyTw26TJbIXRknB+JWBq87eWiF3iiUZzQZF72R8uWOa8u/VXsxd36YUokAAAAASUVORK5CYII=",empty:xt}}),created(){this.imgpath={...this.darkpath};const e=document.querySelector(":root");e.style.setProperty("--text-color","#fff"),e.style.setProperty("--gray-color","rgb(121, 133, 142)"),e.style.setProperty("--accent-color","#4892E7"),e.style.setProperty("--accent-color-light","#A3EEFF"),e.style.setProperty("--primary-color","#263c4aff"),e.style.setProperty("--secondary-color","#132c3bff")},watch:{isLightTheme(e){const t=document.querySelector(":root");t.style.setProperty("--text-color",e?"#2F5883":"#fff"),t.style.setProperty("--gray-color",e?"#4892E7":"rgb(121, 133, 142)"),t.style.setProperty("--accent-color","#4892E7"),t.style.setProperty("--accent-color-light","#A3EEFF"),this.imgpath=e?this.lightpath:this.darkpath}},mounted(){this.hrefUrl="https://ai.medon.com.cn"},methods:{copy(e){const t=document.createElement("input");t.setAttribute("readonly","readonly"),t.setAttribute("value",e),document.body.appendChild(t),t.select(),document.execCommand("copy")&&(document.execCommand("copy"),St({message:this.$t("tool.copysuccess"),type:"primary"})),document.body.removeChild(t)},like(){St({message:this.$t("tool.thankyoulike"),type:"primary"})},unlike(){St({message:this.$t("tool.thankyoufeedback"),type:"primary"})}}},[["render",function(e,t,s,a,i,o){var l,n,r,c,d,A;const g=F,p=ve("Popup"),m=Q,u=ve("v-md-preview"),h=Fe,f=ve("Loading"),y=ve("CloseBold"),w=Qe,v=Ve,b=qe;return be(),Ce("div",{class:Ne(["container_box",{light:e.isLightTheme,dark:!e.isLightTheme}])},[e.payShow?(be(),Se(p,{key:0,show:e.payShow,"onUpdate:show":t[0]||(t[0]=t=>e.payShow=t),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:xe((()=>[K(g,{userInfo:e.userInfo,appTypes:e.appTypes,currentItem:e.currentItem,onToAgreement:e.toAgreement,onClose:e.close},null,8,["userInfo","appTypes","currentItem","onToAgreement","onClose"])])),_:1},8,["show"])):Be("",!0),e.isZH?(be(),Se(m,{key:1})):Be("",!0),K(p,{position:"right",show:e.showMenu,"onUpdate:show":t[3]||(t[3]=t=>e.showMenu=t),style:{width:"284px"}},{default:xe((()=>[Ie("div",{class:Ne(["chatBar",{light:e.isLightTheme,dark:!e.isLightTheme}])},[Ie("div",{class:"startBtn",onClick:t[1]||(t[1]=(...t)=>e.newTalk&&e.newTalk(...t))},[Ie("span",null,Ue(e.$t("faq.newChat")),1)]),Ie("div",pa,[0!==e.logList.length?(be(),Ce("div",ma,[(be(!0),Ce(Te,null,De(e.logList,((s,a)=>(be(),Ce("div",{key:a,class:Ne(s.isActive?"isActive logBox":"logBox")},[""!=s.createdTime?(be(),Ce("div",ua,Ue(s.createdTime),1)):Be("",!0),Ie("div",ha,[t[12]||(t[12]=Ie("span",{class:"middle-dot"},"·",-1)),Ie("div",{class:"chatTopic",onClick:t=>e.getLog(s.id,s.name)},Ue(e.stripHtmlTags(s.name)),9,fa)])],2)))),128))])):(be(),Ce("div",ya,[Ie("img",{src:e.isLightTheme?i.lightpath.empty2:i.darkpath.empty,alt:"Your Image"},null,8,wa),t[13]||(t[13]=Ie("p",null,"无对话",-1))]))]),Ie("div",va,[Ie("div",ba,[Ie("img",{src:i.imgpath.user,alt:"avatar"},null,8,Ca),Ie("span",null,Ue(e.userName||"游客"),1)]),Ie("div",Sa,[Ie("div",Ba,[Ie("div",xa,[Ie("img",{src:i.imgpath.feedback,alt:"",style:{}},null,8,ka),Ie("a",{href:e.feedbackHref,target:"_blank"}," 点此意见反馈 ",8,Ia)]),t[14]||(t[14]=Ie("span",{class:"vertical-line"},"|",-1)),e.isLeftNav?(be(),Ce("span",{key:0,class:"logout",onClick:t[2]||(t[2]=(...t)=>e.logout&&e.logout(...t))},"退出登录")):Be("",!0)])])])],2)])),_:1},8,["show"]),Ie("header",null,[Ie("div",Ua,[Ie("div",Ta,[Ie("a",{href:e.hrefUrl},[Ie("img",{alt:"MedsciAI",src:i.imgpath.logo},null,8,Na)],8,Da),Ie("span",null,[Ie("a",{href:e.hrefUrl},Ue(null==(l=e.$route.query)?void 0:l.appName),9,Ea)])])]),Ie("img",{onClick:t[4]||(t[4]=(...t)=>e.openMenu&&e.openMenu(...t)),class:"openBtn",src:i.imgpath.open,alt:""},null,8,Ra)]),Ie("main",Ya,[Ie("section",Ma,[e.isNewDialogue?(be(),Ce("div",La,[(be(!0),Ce(Te,null,De(e.messageList,((s,a)=>(be(),Ce("div",{key:a,class:"contextStyle"},[1==s.chatType?(be(),Ce("div",Fa,[Ie("div",Qa,[Ie("img",{src:e.avatar||i.avat,alt:"avatar",onError:t[5]||(t[5]=(...t)=>e.changeImg&&e.changeImg(...t)),class:"avatar"},null,40,Va)]),Ie("div",qa,[Ie("div",Wa,[(be(!0),Ce(Te,null,De(s.message_files,((e,s)=>(be(),Ce("div",{class:"documents",key:s},[Ie("div",null,[Ie("div",Ga,["image"==e.type?(be(),Ce("img",Ka)):Be("",!0),"image"!=e.type?(be(),Ce("img",Oa)):Be("",!0),Ee(Ue(e.filename.slice(e.filename.indexOf("_")+1)),1)]),Ie("div",Ja,[t[15]||(t[15]=Ie("img",null,null,-1)),Ee(Ue(e.filename.split(".")[1].toUpperCase())+" · "+Ue((e.size/1024).toFixed(2))+"KB ",1)])])])))),128))]),Ie("div",{style:{flex:"1","line-height":"22px","font-size":"14px","overflow-wrap":"anywhere","text-align":"左侧历史记录列表ustify","letter-spacing":"0.5px"},innerHTML:s.answer},null,8,Pa)])])):(be(),Ce("div",za,[Ie("div",Ha,[t[16]||(t[16]=Ie("div",{style:{width:"2.5rem",height:"2.5rem","margin-right":"0.8rem"}},[Ie("img",{src:Mt,alt:"resAvatar",class:"avatar"})],-1)),Ie("div",Za,[e.isSubscribe?Be("",!0):(be(),Se(u,{key:0,text:(s.answer||"").replace(/\\\[/g,"$[").replace(/\\\]/g,"]$").replace(/\\\(/g,"$(").replace(/\\\)/g,")$"),style:{width:"calc(100%)","letter-spacing":"0.5px","line-height":"22px","margin-top":"5px","font-size":"14px","word-break":"break-word"}},null,8,["text"])),e.isSubscribe?(be(),Ce("div",Xa,[Ee(Ue(s.answer),1),a==e.messageList.length-1?(be(),Se(h,{key:0,type:"text",onClick:e.handleOrder},{default:xe((()=>[Ee(Ue(e.$t("market.subscribe")),1)])),_:1},8,["onClick"])):Be("",!0)])):Be("",!0),Ie("div",ja,[(be(!0),Ce(Te,null,De(s.suggested_questions,(t=>(be(),Ce("div",{class:"tag",key:t,onClick:s=>e.defoultAnswer(t)},Ue(t),9,_a)))),128))])]),s.loading?(be(),Se(f,{key:0,color:"#1989fa",style:{width:"25px"}})):Be("",!0)])]))])))),128))])):Be("",!0),e.isNewDialogue?Be("",!0):(be(),Ce("div",$a,[(be(!0),Ce(Te,null,De(e.messageList,((s,a)=>(be(),Ce("div",{key:a,class:"contextStyle"},[s.id?(be(),Ce("div",ei,[Ie("div",ti,[Ie("img",{src:e.avatar||i.avat,alt:"avatar",onError:t[6]||(t[6]=(...t)=>e.changeImg&&e.changeImg(...t)),class:"avatar"},null,40,si)]),Ie("div",{style:{width:"calc(100% - 44px)","line-height":"22px","font-size":"14px","overflow-wrap":"anywhere","text-align":"左侧历史记录列表ustify","letter-spacing":"0.5px"},innerHTML:s.query},null,8,ai)])):Be("",!0),s.isTrue||s.id?(be(),Ce("div",ii,[Ie("div",oi,[t[17]||(t[17]=Ie("div",{style:{width:"2.5rem",height:"2.5rem","margin-right":"0.8rem"}},[Ie("img",{src:Mt,alt:"resAvatar",class:"avatar"})],-1)),Ie("div",li,[e.isSubscribe?Be("",!0):(be(),Se(u,{key:0,text:(s.answer||"").replace(/\\\[/g,"$[").replace(/\\\]/g,"]$").replace(/\\\(/g,"$(").replace(/\\\)/g,")$"),style:{width:"calc(100% - 44px)","letter-spacing":"0.5px","line-height":"22px","margin-top":"5px","font-size":"14px","word-break":"break-word"}},null,8,["text"])),e.isSubscribe?(be(),Ce("div",ni,[Ee(Ue(s.answer),1),a==e.messageList.length-1?(be(),Se(h,{key:0,type:"text",onClick:e.handleOrder},{default:xe((()=>[Ee(Ue(e.$t("market.subscribe")),1)])),_:1},8,["onClick"])):Be("",!0)])):Be("",!0),Ie("div",ri,[(be(!0),Ce(Te,null,De(s.suggested_questions,(t=>(be(),Ce("div",{class:"tag",key:t,onClick:s=>e.defoultAnswer(t)},Ue(t),9,ci)))),128))])])])])):Be("",!0)])))),128))]))])]),Ie("footer",di,[(null==(n=e.$route.query)?void 0:n.hideHistory)?Be("",!0):(be(),Ce("a",{key:0,href:e.hrefUrl,class:Ne(["backImg","dark-theme"==e.currentTheme?"backImg_dark":"backImg_light"])},[t[18]||(t[18]=Ie("img",{src:"https://img.medsci.cn/202502/aa9a00f8e05a4c5b86e00f493fbdac51-KU2AOKigINI4.png",alt:"更多智能体"},null,-1)),Ee(Ue(e.$t("tool.moreAgents")),1)],10,Ai)),Ie("div",gi,[Ie("div",pi,[(be(!0),Ce(Te,null,De(e.fileList,((t,s)=>(be(),Ce("div",{class:"documents",key:s},[Ie("div",mi,[K(w,{onClick:Re((s=>e.handleDelete(t.upload_file_id)),["stop"])},{default:xe((()=>[K(y)])),_:2},1032,["onClick"])]),Ie("div",ui,["image"==t.type?(be(),Ce("img",hi)):Be("",!0),"image"!=t.type?(be(),Ce("img",fi)):Be("",!0),Ee(Ue(t.name.slice(t.name.indexOf("_")+1)),1)]),Ie("div",yi,Ue(t.extension)+" · "+Ue((t.size/1024).toFixed(2))+"KB ",1)])))),128))]),Ie("div",wi,[Ye(Ie("textarea",{class:Ne([{textareaStyle:e.contextDisable},"chatInput"]),"onUpdate:modelValue":t[7]||(t[7]=t=>e.message=t),rows:"1",placeholder:e.messageList.length?e.$t("faq.iMSL"):e.$t("faq.questionTemp"),maxlength:"1024",readonly:e.contextDisable,onKeydown:t[8]||(t[8]=X(Re(((...t)=>e.sendMessage&&e.sendMessage(...t)),["exact","prevent"]),["enter"]))},null,42,vi),[[Me,e.message]]),(null==(c=null==(r=e.fileUploadType)?void 0:r.file_upload)?void 0:c.enabled)?(be(),Ce("div",bi,[K(v,{"file-list":e.fileList,class:"upload-demo","show-file-list":!1,multiple:"","on-preview":e.handlePreview,"on-remove":e.handleRemove,"before-remove":e.beforeRemove,"http-request":e.customRequest,"on-Success":e.handleSuccess,accept:e.checkFileType(),limit:null==(A=null==(d=e.fileUploadType)?void 0:d.file_upload)?void 0:A.number_limits,"on-exceed":e.handleExceed},{default:xe((()=>[Ie("img",{style:{width:"25px",height:"25px","margin-right":"4px"},src:"dark-theme"==e.currentTheme?"https://img.medsci.cn/202504/ebbbbbd8d4ee4beb9d2578930ac69fb5-NxjOfuavyQ26.png":"https://img.medsci.cn/202504/ed70d9316c644dfbb586bb23f55a7431-Jz8JLYrqoqid.png"},null,8,Ci)])),_:1},8,["file-list","on-preview","on-remove","before-remove","http-request","on-Success","accept","limit","on-exceed"])])):Be("",!0),e.messageList&&0==e.messageList.length||e.message?(be(),Ce("button",{key:1,class:Ne([{textareaStyle:e.contextDisable},"chatButton"]),disabled:e.contextDisable,onClick:t[9]||(t[9]=t=>e.sendMessage())},[Ie("img",{src:i.imgpath.send,alt:"icon",class:"sendBtn"},null,8,Bi)],10,Si)):(be(),Ce("button",{key:2,class:Ne([{textareaStyle:e.contextDisable},"chatButton"]),disabled:e.contextDisable,onClick:t[10]||(t[10]=t=>e.reloadMessage())},[Ie("img",{src:i.imgpath.refresh,alt:"icon",class:"refreshBtn"},null,8,ki)],10,xi))])]),Ie("div",Ii,[Ie("div",Ui,[K(b,{modelValue:e.isLightTheme,"onUpdate:modelValue":t[11]||(t[11]=t=>e.isLightTheme=t),size:"small","inactive-color":"#858585","active-color":"#D1E7FF",class:Ne(e.isLightTheme?e.lights:e.darks)},null,8,["modelValue","class"]),Ie("span",null,Ue(e.$t("faq.gentleMode")),1)]),Ie("div",Ti,[Ie("span",Di,[Ee(Ue("zh-CN"!=e.$i18n.locale?e.$t("faq.beforeUse").slice(0,30)+"...":e.$t("faq.beforeUse"))+" ",1),Ie("a",Ni,Ue(e.$t("faq.disclaimer")),1)])])])])],2)}],["__scopeId","data-v-c9b3fc92"]])},data:()=>({screenWidth:document.body.clientWidth,component:""}),watch:{screenWidth:{handler(e){this.component=e<750?"mobilePage":"pcPage"},immediate:!0}},mounted(){var e,t;["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=this.$route.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=this.$route.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),window.onresize=()=>{this.screenWidth=document.body.clientWidth}}},[["render",function(e,t,s,a,i,o){return be(),Ce("div",{class:Ne(e.currentTheme)},[(be(),Se(We(i.component)))],2)}]]);export{Ei as default};
