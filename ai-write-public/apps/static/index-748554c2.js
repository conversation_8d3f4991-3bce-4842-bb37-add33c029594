/* empty css                  */import{g as e,i as a}from"./index-0f75ba36.js";import{r as t,x as l,u as o,y as n,w as i,d as s,e as r,j as u,t as d,g as c,h as v,m as p,F as m,z as f,f as g,A as h,k as y,i as b,B as w,C as x,D as _,G as k,H as I,I as S,E as T,J as C,K as $,L as N,M as B,N as z,O as q,P as U,Q as j,R as A,o as V,S as O,T as E,U as R,V as M,q as L,W as D,X as H,Y as P,Z as W,a as F,b as J,$ as X,a0 as Z,a1 as Y,a2 as G,a3 as K,a4 as Q,c as ee,a5 as ae,p as te,a6 as le,a7 as oe,a8 as ne,a9 as ie,aa as se,ab as re,ac as ue,v as de}from"./index-3f661793.js";import{r as ce,a as ve,f as pe}from"./use-route-923f147f.js";/* empty css                  *//* empty css                  *//* empty css                 */import{c as me,r as fe,g as ge,s as he,i as ye,o as be,a as we,n as xe,m as _e,b as ke,u as Ie,d as Se,e as Te,f as Ce,h as $e,j as Ne,k as Be,w as ze,l as qe,p as Ue,t as je,q as Ae,v as Ve,x as Oe,y as Ee,z as Re,A as Me,B as Le,C as De,D as He,E as Pe,F as We,G as Fe,H as Je,I as Xe,J as Ze,K as Ye,L as Ge,M as Ke,N as Qe,O as ea,P as aa,Q as ta}from"./index-c2240e6b.js";/* empty css                   */const la={class:"p-3 flex-1 rounded-md"},oa={class:"text-[14px] font-bold mb-2 text-gray-600"},na={__name:"InputField",props:{type:{type:String,required:!0},label:{type:String,required:!0},value:{type:[String,Number],required:!0},required:{type:Boolean,required:!0},placeholder:{type:String,required:!0},max_length:{type:Number,required:!1},options:{type:Array,required:!1},fileVerify:{type:Array,required:!1,default:()=>[""]},currentItem:{type:Object,required:!1}},emits:["update:value"],setup(e,{expose:a,emit:$}){const N=t(l.get("userInfo")?JSON.parse(l.get("userInfo")):{}),B=o(),z=n(),q=t([]),U=t({jpg:"image",jpeg:"image",png:"image",webp:"image",gif:"image",svg:"image",mp4:"video",mov:"video",mpeg:"video",mpga:"video",mp3:"audio",m4a:"audio",wav:"audio",webm:"audio",amr:"audio",txt:"document",markdown:"document",md:"document",mdx:"document",pdf:"document",html:"document",htm:"document",xlsx:"document",xls:"document",docx:"document",csv:"document",eml:"document",msg:"document",pptx:"document",xml:"document",epub:"document"}),j=t(""),A=e,V=A.type,O=A.fileVerify,E=A.label,R=A.required,M=A.max_length,L=A.options;"file"==V&&(j.value=null),"file-list"==V&&(j.value=[]);const D={image:[".jpg",".jpeg",".png",".webp",".gif",".svg"],video:[".mp4",".mov",".mpeg",".mpga"],audio:[".mp3",".m4a",".wav",".webm",".amr"],document:[".txt",".markdown",".md",".mdx",".pdf",".html",".htm",".xlsx",".xls",".docx",".csv",".eml",".msg",".pptx",".xml",".epub"]},H=()=>{let e="";return O.forEach(((a,t)=>{t<O.length-1?e+=D[a].join(",")+",":e+=D[a].join(",")})),e},P=$,W=async()=>{var e,a,t,o,n,i;if(!l.get("userInfo")){localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const t=w();return t&&"zh-CN"!=t?z.push("/login"):window.addLoginDom(),null==(e=document.getElementsByTagName("input")[0])||e.blur(),null==(a=document.getElementsByTagName("textarea")[0])||a.blur(),!1}return!!(null==(o=null==(t=A.currentItem)?void 0:t.appUser)?void 0:o.status)||(P("payShowStatus",!0),null==(n=document.getElementsByTagName("input")[0])||n.blur(),null==(i=document.getElementsByTagName("textarea")[0])||i.blur(),!1)},F=(e,a,t)=>{},J=()=>{j.value=""},X=async e=>{if(!(await W()))return!1;const{file:a,onSuccess:t,onError:l}=e,o=new FormData;o.append("file",a),o.append("appId",B.params.uuid),o.append("user",N.value.userName);try{const e=await x(o);"file-list"==V?j.value.push({type:U.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id}):j.value={type:U.value[e.extension],transfer_method:"local_file",url:"",upload_file_id:e.id},t(e,a)}catch(n){l(n)}return!1};L&&L.length>0&&(j.value=L[0]);return a({updateMessage:()=>{L&&L.length>0?j.value=L[0]:"file"==V?(j.value=null,q.value=[]):"file-list"==V?(j.value=[],q.value=[]):j.value=""}}),i(j,(e=>{P("update:value",e)}),{immediate:!0,deep:!0}),(e,a)=>{const t=_,l=k,o=I,n=S,i=T,w=C;return s(),r("div",la,[u("div",oa,d(c(E)),1),"paragraph"===c(V)||"text-input"===c(V)?(s(),v(t,{key:0,onFocus:W,modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value=e),type:"paragraph"===c(V)?"textarea":"text",rows:5,required:c(R),placeholder:`${c(E)}`,"show-word-limit":"",resize:"none",maxlength:c(M)},null,8,["modelValue","type","required","placeholder","maxlength"])):"number"===c(V)?(s(),v(t,{key:1,modelValue:j.value,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value=e),modelModifiers:{number:!0},onFocus:W,type:"number",required:c(R),placeholder:`${c(E)}`,resize:"none"},null,8,["modelValue","required","placeholder"])):"select"===c(V)?(s(),v(o,{key:2,onChange:W,modelValue:j.value,"onUpdate:modelValue":a[2]||(a[2]=e=>j.value=e),required:c(R),placeholder:`${c(E)}`},{default:p((()=>[(s(!0),r(m,null,f(c(L),(e=>(s(),v(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue","required","placeholder"])):"file"===c(V)||"file-list"===c(V)?(s(),v(w,{key:3,"file-list":q.value,"onUpdate:fileList":a[3]||(a[3]=e=>q.value=e),class:"upload-demo",multiple:"","show-file-list":"","on-preview":e.handlePreview,"on-remove":J,"before-remove":e.beforeRemove,limit:c(M),accept:H(),"auto-upload":!0,"on-Success":F,"http-request":X,"on-exceed":e.handleExceed},{default:p((()=>[g(i,{disabled:"file"===c(V)?1==q.value.length:q.value.length==c(M)},{default:p((()=>[g(n,{class:"el-icon--upload",style:{"margin-right":"5px","font-size":"16px"}},{default:p((()=>[g(c(h))])),_:1}),a[4]||(a[4]=y("从本地上传"))])),_:1},8,["disabled"])])),_:1},8,["file-list","on-preview","before-remove","limit","accept","on-exceed"])):b("",!0)])}}};let ia=0;function sa(){const e=$(),{name:a="unknown"}=(null==e?void 0:e.type)||{};return`${a}-${++ia}`}function ra(e,a){if(!ye||!window.IntersectionObserver)return;const t=new IntersectionObserver((e=>{a(e[0].intersectionRatio>0)}),{root:document.body}),l=()=>{e.value&&t.unobserve(e.value)};B(l),z(l),be((()=>{e.value&&t.observe(e.value)}))}const[ua,da]=we("sticky");const ca=Ue(q({name:ua,props:{zIndex:xe,position:_e("top"),container:Object,offsetTop:ke(0),offsetBottom:ke(0)},emits:["scroll","change"],setup(e,{emit:a,slots:l}){const o=t(),n=Ie(o),s=U({fixed:!1,width:0,height:0,transform:0}),r=t(!1),u=j((()=>Se("top"===e.position?e.offsetTop:e.offsetBottom))),d=j((()=>{if(r.value)return;const{fixed:e,height:a,width:t}=s;return e?{width:`${t}px`,height:`${a}px`}:void 0})),c=j((()=>{if(!s.fixed||r.value)return;const a=Te(Ce(e.zIndex),{width:`${s.width}px`,height:`${s.height}px`,[e.position]:`${u.value}px`});return s.transform&&(a.transform=`translate3d(0, ${s.transform}px, 0)`),a})),v=()=>{if(!o.value||Ne(o))return;const{container:t,position:l}=e,n=Be(o),i=ge(window);if(s.width=n.width,s.height=n.height,"top"===l)if(t){const e=Be(t),a=e.bottom-u.value-s.height;s.fixed=u.value>n.top&&e.bottom>0,s.transform=a<0?a:0}else s.fixed=u.value>n.top;else{const{clientHeight:e}=document.documentElement;if(t){const a=Be(t),l=e-a.top-u.value-s.height;s.fixed=e-u.value<n.bottom&&e>a.top,s.transform=l<0?-l:0}else s.fixed=e-u.value<n.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:s.fixed})})(i)};return i((()=>s.fixed),(e=>a("change",e))),$e("scroll",v,{target:n,passive:!0}),ra(o,v),i([ze,qe],(()=>{o.value&&!Ne(o)&&s.fixed&&(r.value=!0,A((()=>{const e=Be(o);s.width=e.width,s.height=e.height,r.value=!1})))})),()=>{var e;return g("div",{ref:o,style:d.value},[g("div",{class:da({fixed:s.fixed&&!r.value}),style:c.value},[null==(e=l.default)?void 0:e.call(l)])])}}})),[va,pa]=we("swipe"),ma={loop:je,width:xe,height:xe,vertical:Boolean,autoplay:ke(0),duration:ke(500),touchable:je,lazyRender:Boolean,initialSwipe:ke(0),indicatorColor:String,showIndicators:je,stopPropagation:je},fa=Symbol(va);const ga=Ue(q({name:va,props:ma,emits:["change","dragStart","dragEnd"],setup(e,{emit:a,slots:l}){const o=t(),n=t(),s=U({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let r=!1;const u=Ae(),{children:d,linkChildren:c}=Ve(fa),v=j((()=>d.length)),p=j((()=>s[e.vertical?"height":"width"])),m=j((()=>e.vertical?u.deltaY.value:u.deltaX.value)),f=j((()=>{if(s.rect){return(e.vertical?s.rect.height:s.rect.width)-p.value*v.value}return 0})),h=j((()=>p.value?Math.ceil(Math.abs(f.value)/p.value):v.value)),y=j((()=>v.value*p.value)),b=j((()=>(s.active+v.value)%v.value)),w=j((()=>{const a=e.vertical?"vertical":"horizontal";return u.direction.value===a})),x=j((()=>{const a={transitionDuration:`${s.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${+s.offset.toFixed(2)}px)`};if(p.value){const t=e.vertical?"height":"width",l=e.vertical?"width":"height";a[t]=`${y.value}px`,a[l]=e[l]?`${e[l]}px`:""}return a})),_=(a,t=0)=>{let l=a*p.value;e.loop||(l=Math.min(l,-f.value));let o=t-l;return e.loop||(o=De(o,f.value,0)),o},k=({pace:t=0,offset:l=0,emitChange:o})=>{if(v.value<=1)return;const{active:n}=s,i=(a=>{const{active:t}=s;return a?e.loop?De(t+a,-1,v.value):De(t+a,0,h.value):t})(t),r=_(i,l);if(e.loop){if(d[0]&&r!==f.value){const e=r<f.value;d[0].setOffset(e?y.value:0)}if(d[v.value-1]&&0!==r){const e=r>0;d[v.value-1].setOffset(e?-y.value:0)}}s.active=i,s.offset=r,o&&i!==n&&a("change",b.value)},I=()=>{s.swiping=!0,s.active<=-1?k({pace:v.value}):s.active>=v.value&&k({pace:-v.value})},S=()=>{I(),u.reset(),Me((()=>{s.swiping=!1,k({pace:1,emitChange:!0})}))};let T;const C=()=>clearTimeout(T),$=()=>{C(),+e.autoplay>0&&v.value>1&&(T=setTimeout((()=>{S(),$()}),+e.autoplay))},N=(a=+e.initialSwipe)=>{if(!o.value)return;const t=()=>{var t,l;if(!Ne(o)){const a={width:o.value.offsetWidth,height:o.value.offsetHeight};s.rect=a,s.width=+(null!=(t=e.width)?t:a.width),s.height=+(null!=(l=e.height)?l:a.height)}v.value&&-1===(a=Math.min(v.value-1,a))&&(a=v.value-1),s.active=a,s.swiping=!0,s.offset=_(a),d.forEach((e=>{e.setOffset(0)})),$()};Ne(o)?A().then(t):t()},q=()=>N(s.active);let E;const R=a=>{!e.touchable||a.touches.length>1||(u.start(a),r=!1,E=Date.now(),C(),I())},M=()=>{if(!e.touchable||!s.swiping)return;const t=Date.now()-E,l=m.value/t;if((Math.abs(l)>.25||Math.abs(m.value)>p.value/2)&&w.value){const a=e.vertical?u.offsetY.value:u.offsetX.value;let t=0;t=e.loop?a>0?m.value>0?-1:1:0:-Math[m.value>0?"ceil":"floor"](m.value/p.value),k({pace:t,emitChange:!0})}else m.value&&k({pace:0});r=!1,s.swiping=!1,a("dragEnd",{index:b.value}),$()},L=(a,t)=>{const l=t===b.value,o=l?{backgroundColor:e.indicatorColor}:void 0;return g("i",{style:o,class:pa("indicator",{active:l})},null)};return Oe({prev:()=>{I(),u.reset(),Me((()=>{s.swiping=!1,k({pace:-1,emitChange:!0})}))},next:S,state:s,resize:q,swipeTo:(a,t={})=>{I(),u.reset(),Me((()=>{let l;l=e.loop&&a===v.value?0===s.active?0:a:a%v.value,t.immediate?Me((()=>{s.swiping=!1})):s.swiping=!1,k({pace:l-s.active,emitChange:!0})}))}}),c({size:p,props:e,count:v,activeIndicator:b}),i((()=>e.initialSwipe),(e=>N(+e))),i(v,(()=>N(s.active))),i((()=>e.autoplay),$),i([ze,qe,()=>e.width,()=>e.height],q),i(Ee(),(e=>{"visible"===e?$():C()})),V(N),O((()=>N(s.active))),Re((()=>N(s.active))),B(C),z(C),$e("touchmove",(t=>{if(e.touchable&&s.swiping&&(u.move(t),w.value)){!e.loop&&(0===s.active&&m.value>0||s.active===v.value-1&&m.value<0)||(Le(t,e.stopPropagation),k({offset:m.value}),r||(a("dragStart",{index:b.value}),r=!0))}}),{target:n}),()=>{var a;return g("div",{ref:o,class:pa()},[g("div",{ref:n,style:x.value,class:pa("track",{vertical:e.vertical}),onTouchstartPassive:R,onTouchend:M,onTouchcancel:M},[null==(a=l.default)?void 0:a.call(l)]),l.indicator?l.indicator({active:b.value,total:v.value}):e.showIndicators&&v.value>1?g("div",{class:pa("indicators",{vertical:e.vertical})},[Array(v.value).fill("").map(L)]):void 0])}}})),[ha,ya]=we("tabs");var ba=q({name:ha,props:{count:He(Number),inited:Boolean,animated:Boolean,duration:He(xe),swipeable:Boolean,lazyRender:Boolean,currentIndex:He(Number)},emits:["change"],setup(e,{emit:a,slots:l}){const o=t(),n=e=>a("change",e),s=()=>{var a;const t=null==(a=l.default)?void 0:a.call(l);return e.animated||e.swipeable?g(ga,{ref:o,loop:!1,class:ya("track"),duration:1e3*+e.duration,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:n},{default:()=>[t]}):t},r=a=>{const t=o.value;t&&t.state.active!==a&&t.swipeTo(a,{immediate:!e.inited})};return i((()=>e.currentIndex),r),V((()=>{r(e.currentIndex)})),Oe({swipeRef:o}),()=>g("div",{class:ya("content",{animated:e.animated||e.swipeable})},[s()])}});const[wa,xa]=we("tabs"),_a={type:_e("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ke(0),duration:ke(.3),animated:Boolean,ellipsis:je,swipeable:Boolean,scrollspy:Boolean,offsetTop:ke(0),background:String,lazyRender:je,showHeader:je,lineWidth:xe,lineHeight:xe,beforeChange:Function,swipeThreshold:ke(5),titleActiveColor:String,titleInactiveColor:String},ka=Symbol(wa);var Ia=q({name:wa,props:_a,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:a,slots:l}){let o,n,s,r,u;const d=t(),c=t(),v=t(),p=t(),m=sa(),f=Ie(d),[h,y]=function(){const e=t([]),a=[];return N((()=>{e.value=[]})),[e,t=>(a[t]||(a[t]=a=>{e.value[t]=a}),a[t])]}(),{children:b,linkChildren:w}=Ve(ka),x=U({inited:!1,position:"",lineStyle:{},currentIndex:-1}),_=j((()=>b.length>+e.swipeThreshold||!e.ellipsis||e.shrink)),k=j((()=>({borderColor:e.color,background:e.background}))),I=(e,a)=>{var t;return null!=(t=e.name)?t:a},S=j((()=>{const e=b[x.currentIndex];if(e)return I(e,x.currentIndex)})),T=j((()=>Se(e.offsetTop))),C=j((()=>e.sticky?T.value+o:0)),$=a=>{const t=c.value,l=h.value;if(!(_.value&&t&&l&&l[x.currentIndex]))return;const o=l[x.currentIndex].$el,n=o.offsetLeft-(t.offsetWidth-o.offsetWidth)/2;r&&r(),r=function(e,a,t){let l,o=0;const n=e.scrollLeft,i=0===t?1:Math.round(1e3*t/16);let s=n;return function t(){s+=(a-n)/i,e.scrollLeft=s,++o<i&&(l=fe(t))}(),function(){me(l)}}(t,n,a?0:+e.duration)},B=()=>{const a=x.inited;A((()=>{const t=h.value;if(!t||!t[x.currentIndex]||"line"!==e.type||Ne(d.value))return;const l=t[x.currentIndex].$el,{lineWidth:o,lineHeight:n}=e,i=l.offsetLeft+l.offsetWidth/2,s={width:Pe(o),backgroundColor:e.color,transform:`translateX(${i}px) translateX(-50%)`};if(a&&(s.transitionDuration=`${e.duration}s`),We(n)){const e=Pe(n);s.height=e,s.borderRadius=e}x.lineStyle=s}))},z=(t,l)=>{const o=(e=>{const a=e<x.currentIndex?-1:1;for(;e>=0&&e<b.length;){if(!b[e].disabled)return e;e+=a}})(t);if(!We(o))return;const n=b[o],i=I(n,o),r=null!==x.currentIndex;x.currentIndex!==o&&(x.currentIndex=o,l||$(),B()),i!==e.active&&(a("update:active",i),r&&a("change",i,n.title)),s&&!e.scrollspy&&Je(Math.ceil(Xe(d.value)-T.value))},q=(e,a)=>{const t=b.find(((a,t)=>I(a,t)===e)),l=t?b.indexOf(t):0;z(l,a)},V=(a=!1)=>{if(e.scrollspy){const t=b[x.currentIndex].$el;if(t&&f.value){const l=Xe(t,f.value)-C.value;n=!0,u&&u(),u=function(e,a,t,l){let o,n=ge(e);const i=n<a,s=0===t?1:Math.round(1e3*t/16),r=(a-n)/s;return function t(){n+=r,(i&&n>a||!i&&n<a)&&(n=a),he(e,n),i&&n<a||!i&&n>a?o=fe(t):l&&(o=fe(l))}(),function(){me(o)}}(f.value,l,a?0:+e.duration,(()=>{n=!1}))}}},E=(t,l,o)=>{const{title:n,disabled:i}=b[l],s=I(b[l],l);i||(Ze(e.beforeChange,{args:[s],done:()=>{z(l),V()}}),ce(t)),a("clickTab",{name:s,title:n,event:o,disabled:i})},R=e=>{s=e.isFixed,a("scroll",e)},M=()=>{if("line"===e.type&&b.length)return g("div",{class:xa("line"),style:x.lineStyle},null)},L=()=>{var a,t,o;const{type:n,border:i,sticky:s}=e,r=[g("div",{ref:s?void 0:v,class:[xa("wrap"),{[Fe]:"line"===n&&i}]},[g("div",{ref:c,role:"tablist",class:xa("nav",[n,{shrink:e.shrink,complete:_.value}]),style:k.value,"aria-orientation":"horizontal"},[null==(a=l["nav-left"])?void 0:a.call(l),b.map((e=>e.renderTitle(E))),M(),null==(t=l["nav-right"])?void 0:t.call(l)])]),null==(o=l["nav-bottom"])?void 0:o.call(l)];return s?g("div",{ref:v},[r]):r},D=()=>{B(),A((()=>{var e,a;$(!0),null==(a=null==(e=p.value)?void 0:e.swipeRef.value)||a.resize()}))};i((()=>[e.color,e.duration,e.lineWidth,e.lineHeight]),B),i(ze,D),i((()=>e.active),(e=>{e!==S.value&&q(e)})),i((()=>b.length),(()=>{x.inited&&(q(e.active),B(),A((()=>{$(!0)})))}));return Oe({resize:D,scrollTo:e=>{A((()=>{q(e),V(!0)}))}}),O(B),Re(B),be((()=>{q(e.active,!0),A((()=>{x.inited=!0,v.value&&(o=Be(v.value).height),$(!0)}))})),ra(d,B),$e("scroll",(()=>{if(e.scrollspy&&!n){const e=(()=>{for(let e=0;e<b.length;e++){const{top:a}=Be(b[e].$el);if(a>C.value)return 0===e?0:e-1}return b.length-1})();z(e)}}),{target:f,passive:!0}),w({id:m,props:e,setLine:B,scrollable:_,onRendered:(e,t)=>a("rendered",e,t),currentName:S,setTitleRefs:y,scrollIntoView:$}),()=>g("div",{ref:d,class:xa([e.type])},[e.showHeader?e.sticky?g(ca,{container:d.value,offsetTop:T.value,onScroll:R},{default:()=>[L()]}):L():null,g(ba,{ref:p,count:b.length,inited:x.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:x.currentIndex,onChange:z},{default:()=>{var e;return[null==(e=l.default)?void 0:e.call(l)]}})])}});const Sa=Symbol(),[Ta,Ca]=we("tab"),$a=q({name:Ta,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:xe,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:je},setup(e,{slots:a}){const t=j((()=>{const a={},{type:t,color:l,disabled:o,isActive:n,activeColor:i,inactiveColor:s}=e;l&&"card"===t&&(a.borderColor=l,o||(n?a.backgroundColor=l:a.color=l));const r=n?i:s;return r&&(a.color=r),a})),l=()=>{const t=g("span",{class:Ca("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||We(e.badge)&&""!==e.badge?g(Ye,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[t]}):t};return()=>g("div",{id:e.id,role:"tab",class:[Ca([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:t.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[Na,Ba]=we("swipe-item");const za=Ue(q({name:Na,setup(e,{slots:a}){let t;const l=U({offset:0,inited:!1,mounted:!1}),{parent:o,index:n}=Ge(fa);if(!o)return;const i=j((()=>{const e={},{vertical:a}=o.props;return o.size.value&&(e[a?"height":"width"]=`${o.size.value}px`),l.offset&&(e.transform=`translate${a?"Y":"X"}(${l.offset}px)`),e})),s=j((()=>{const{loop:e,lazyRender:a}=o.props;if(!a||t)return!0;if(!l.mounted)return!1;const i=o.activeIndicator.value,s=o.count.value-1,r=0===i&&e?s:i-1,u=i===s&&e?0:i+1;return t=n.value===i||n.value===r||n.value===u,t}));return V((()=>{A((()=>{l.mounted=!0}))})),Oe({setOffset:e=>{l.offset=e}}),()=>{var e;return g("div",{class:Ba(),style:i.value},[s.value?null==(e=a.default)?void 0:e.call(a):null])}}})),[qa,Ua]=we("tab");const ja=Ue(q({name:qa,props:Te({},ve,{dot:Boolean,name:xe,badge:xe,title:String,disabled:Boolean,titleClass:Ke,titleStyle:[String,Object],showZeroBadge:je}),setup(e,{slots:a}){const l=sa(),o=t(!1),n=$(),{parent:s,index:r}=Ge(ka);if(!s)return;const u=()=>{var a;return null!=(a=e.name)?a:r.value},d=j((()=>{const a=u()===s.currentName.value;return a&&!o.value&&(o.value=!0,s.props.lazyRender&&A((()=>{s.onRendered(u(),e.title)}))),a})),c=t(""),v=t("");E((()=>{const{titleClass:a,titleStyle:t}=e;c.value=a?R(a):"",v.value=t&&"string"!=typeof t?M(L(t)):t}));const p=t(!d.value);return i(d,(e=>{e?p.value=!1:Me((()=>{p.value=!0}))})),i((()=>e.title),(()=>{s.setLine(),s.scrollIntoView()})),D(Sa,d),Oe({id:l,renderTitle:t=>g($a,W({key:l,id:`${s.id}-${r.value}`,ref:s.setTitleRefs(r.value),style:v.value,class:c.value,isActive:d.value,controls:l,scrollable:s.scrollable.value,activeColor:s.props.titleActiveColor,inactiveColor:s.props.titleInactiveColor,onClick:e=>t(n.proxy,r.value,e)},Qe(s.props,["type","color","shrink"]),Qe(e,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title})}),()=>{var e;const t=`${s.id}-${r.value}`,{animated:n,swipeable:i,scrollspy:u,lazyRender:c}=s.props;if(!a.default&&!n)return;const v=u||d.value;if(n||i)return g(za,{id:l,role:"tabpanel",class:Ua("panel-wrapper",{inactive:p.value}),tabindex:d.value?0:-1,"aria-hidden":!d.value,"aria-labelledby":t,"data-allow-mismatch":"attribute"},{default:()=>{var e;return[g("div",{class:Ua("panel")},[null==(e=a.default)?void 0:e.call(a)])]}});const m=o.value||u||!c?null==(e=a.default)?void 0:e.call(a):null;return H(g("div",{id:l,role:"tabpanel",class:Ua("panel"),tabindex:v?0:-1,"aria-labelledby":t,"data-allow-mismatch":"attribute"},[m]),[[P,v]])}}})),Aa=Ue(Ia),Va={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(var(--vh) * 100 - 160px)"}},Oa={class:"pc_container",style:{display:"flex"}},Ea={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%"}},Ra={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Ma={class:"pc_right bg-[#fff]"},La={id:"typing-area"},Da={key:0,class:"decContaniner nop bg-[#fff]"},Ha={key:0,class:"img_box"},Pa=["src"],Wa={key:1,class:"icon"},Fa={class:"process_text label_width"},Ja={key:0,class:"process"},Xa={key:0,class:"img_box"},Za=["src"],Ya={key:1,class:"icon"},Ga={class:"process"},Ka={class:"process_text"},Qa={key:2},et=["src"],at=["src"],tt={class:"mobile_container"},lt={class:"p-3",style:{display:"flex","justify-content":"space-between"}},ot={class:"mobile_right"},nt={id:"typing-area"},it={key:0,class:"decContaniner nop bg-[#fff]"},st={key:0,class:"img_box"},rt=["src"],ut={key:1,class:"icon"},dt={class:"process_text label_width"},ct={key:0,class:"img_box"},vt=["src"],pt={key:1,class:"icon"},mt={class:"process"},ft={class:"process_text"},gt={key:2},ht=F({__name:"index",props:{currentItem:{type:Object,default:()=>{}}},setup(h){const x=h,_=e("loading.png"),k=e("copy.png"),I=U({}),C=o(),$={},N=t([]),B=t(l.get("userInfo")?JSON.parse(l.get("userInfo")):{}),z=t(null),{t:q,locale:A}=J(),O=n(),E=t(!1);let M=t("a"),L=t(!0),D=t("");const H=t(""),P=t(null),W=t(null),F=t(["1","2"]),ce=t(!1),ve=t(!1),me=t(!1);let fe;const ge=t(""),he=t(""),ye=async()=>{var e;await le({appId:ge.value,user:B.value.userName,mode:null==(e=z.value)?void 0:e.mode,task_id:H.value}),setTimeout((()=>{Fe.abort(),Je=!0,Ae.value=[],ce.value=!1,Ve.value.length&&Ve.value.forEach((e=>{e.status=!0})),Qe()}),0)},be=()=>{me.value=!1},we=async(e,a)=>{var t;let l=w();if(null==(t=B.value)?void 0:t.userId){const t={appUuid:a,priceId:e.priceId,monthNum:e.monthNum};let l=await oe(t);l&&(ne({type:"success",message:q("tool.sS")}),setTimeout((()=>{location.href=l}),1e3))}else l&&"zh-CN"!=l?O.push((isUp,"/login")):window.addLoginDom()};V((async()=>{var e,t;const o=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${o}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(e=C.params)?void 0:e.lang)?window.localStorage.setItem("ai_apps_lang",null==(t=C.params)?void 0:t.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),A.value=X(),qe();const n=await Z(C.params.appUuid);ge.value=n.dAppUuid,he.value=n.appName,l.get("userInfo")&&await ua(),Ie(),a(n.customCss,n.customJs)}));const xe=j((()=>`<${he.value}>AI智能体-梅斯小智/梅斯医学`));Y({title:xe});const _e=e=>{M.value=e.name},ke=async()=>{me.value=!0},Ie=()=>{ge.value&&G({appId:ge.value,user:B.value.userName}).then((e=>{(null==e?void 0:e.user_input_form)&&(N.value=e.user_input_form,e.user_input_form.forEach((e=>{const a=Object.keys(e)[0],t=e[a].variable;$[t]={label:e[a].label},I[t]=""})))}))},Se=j((()=>!!N.value.length)),Te=j((()=>N.value.filter((e=>!0===e[Object.keys(e)[0]].required)).map((e=>e[Object.keys(e)[0]].variable)))),Ce=()=>{ge.value&&ie({appId:ge.value,user:B.value.userName}).then((e=>{z.value={...e}}))},$e=t(!1),Ne=t(!1),Be=t(!1),ze=t(!1),qe=async()=>{var e,a;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(e=B.value)?void 0:e.userId,userToken:"",channel:"MedSci_xAI",appId:ge.value,userUuid:null==(a=B.value)?void 0:a.openid}];await K.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)},Ue=e=>{me.value=e},je=async()=>{var e,a,t,o;if(l.get("userInfo")){if(await ua(),!(null==(a=null==(e=x.currentItem)?void 0:e.appUser)?void 0:a.status))return me.value=!0,!1;if(0!=Te.value.length||(n=I,Object.values(n).some((e=>e)))){var n;for(let e in I)if(Te.value.includes(e)&&!I[e])return void ne({message:`${$[e].label}${q("tool.requiredfield")}`,type:"error"});(null==(t=z.value)?void 0:t.mode)&&(["advanced-chat","chat"].includes(null==(o=z.value)?void 0:o.mode)?ne({type:"success",message:q("tool.planning")}):"completion"==z.value.mode?(L.value=!1,setTimeout((()=>{M.value="b"}),1e3),Ke()):(L.value=!1,setTimeout((()=>{M.value="b"}),1e3),Ge()))}else ne({message:`${q("tool.enterquestion")}`,type:"error"})}else{localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken");const e=w();e&&"zh-CN"!=e?O.push("/login"):window.addLoginDom()}},Ae=t([]);var Ve=t([]),Oe=t([]);const Ee=t(""),Re=t(0),Me=t(""),Le=t(!1),De=t(!1);let He=t(0);const Pe=t(!1),We=t(!1);let Fe,Je=!1,Xe=!1;i(Ve,(()=>{Ze()}),{deep:!0});const Ze=()=>{He.value<Ve.value.length&&(Oe.value.push(Ve.value[He.value]),He.value++,setTimeout(Ze,1e3))},Ye=()=>{Re.value<Ee.value.length?(Le.value=!0,Me.value+=Ee.value.charAt(Re.value),Re.value++,setTimeout(Ye,20)):(We.value=!1,Le.value=!1,Be.value=!0,Qe())},Ge=async()=>{ve.value=!0,ce.value=!0,D.value="",Ve.value=[],Oe.value=[],He.value=0,Me.value="",Ee.value="",Ae.value=[],Pe.value=!1,Je=!1,Re.value=0,Fe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/workflows/run?locale=zh`;0,$e.value=!0,Ne.value=!0,ze.value=!1,await pe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:ge.value,user:B.value.userName,inputs:{...I,outputLanguage:I.outputLanguage?I.outputLanguage:"中文"==se()?"简体中文":se()},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:C.params.appUuid}),onmessage(e){var a,t,l,o,n,i,s,r,u,d;if(e.data.trim())try{const c=JSON.parse(e.data);if(H.value=c.task_id,c.error)throw new Error(c.error);"智能体推理思维链"==(null==(a=null==c?void 0:c.data)?void 0:a.title)&&"node_finished"===c.event&&(We.value=!0,Ee.value=null==(o=JSON.parse(null==(l=null==(t=null==c?void 0:c.data)?void 0:t.outputs)?void 0:l.text))?void 0:o.text,Ye()),"node_started"!==c.event||Pe.value||"开始"==(null==(n=null==c?void 0:c.data)?void 0:n.title)||Ve.value.push({node_id:null==(i=null==c?void 0:c.data)?void 0:i.node_id,title:null==(s=null==c?void 0:c.data)?void 0:s.title,status:!1}),"error"===c.event&&(5047==c.code&&(De.value=!0),Pe.value=!0,ce.value=!1,Je=!0,Ne.value=!1,D.value=null==c?void 0:c.message),"node_finished"===c.event&&Ve.value.forEach((e=>{var a;e.node_id==(null==(a=null==c?void 0:c.data)?void 0:a.node_id)&&(e.status=!0)})),"text_chunk"===c.event&&(E.value=!0,Ae.value.push(null==(r=null==c?void 0:c.data)?void 0:r.text),We.value||Qe()),"workflow_started"===c.event&&($e.value=!1),"workflow_finished"===c.event&&(Je=!0,Pe.value=!0,ce.value=!1,Ne.value=!1,Be.value=!1,E.value||(Ae.value.push(null==(d=null==(u=null==c?void 0:c.data)?void 0:u.outputs)?void 0:d.text),We.value||Qe()))}catch(c){ia(c)}},onerror(e){ia(e)},signal:Fe.signal,openWhenHidden:!0})}catch(e){ia()}},Ke=async()=>{D.value="",Ae.value=[],We.value=!1,Je=!1,Fe=new AbortController;try{let e=`${window.location.origin}/dev-api/ai-base/chat/completion-messages?locale=zh`;0,$e.value=!0,Ne.value=!0,ze.value=!1,await pe(e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("yudaoToken")||null}`},body:JSON.stringify({appId:ge.value,user:B.value.userName,inputs:{...I,outputLanguage:se()},files:[],response_mode:"streaming",appUuid:C.params.appUuid,requestId:crypto.randomUUID()}),onmessage(e){if($e.value=!1,Be.value=!0,e.data.trim())try{const a=JSON.parse(e.data);if(H.value=a.task_id,a.error)throw new Error(a.error);"error"===a.event&&(5047==a.code&&(De.value=!0),Je=!0,D.value=null==a?void 0:a.message,Be.value=!1,Ne.value=!1),"message"===a.event&&(Ae.value.push(null==a?void 0:a.answer),We.value||Qe()),"message_end"===a.event&&(Je=!0,Ne.value=!1,Be.value=!1)}catch(a){ia(a)}},onerror(e){ia(e)},signal:Fe.signal,openWhenHidden:!0})}catch(e){ia()}},Qe=()=>{if(0===Ae.value.length)return We.value=!1,Xe=!0,void la();We.value=!0;const e=Ae.value.shift();oa(e).then((()=>{Qe()}))},la=()=>{Xe&&Je&&(Ne.value=!1,Be.value=!1,ze.value=!0)},oa=e=>new Promise((a=>{let t=0;fe=setInterval((()=>{if(t<(null==e?void 0:e.length)){D.value+=e[t++];const a=document.getElementsByClassName("pc_right");a[0].scrollTop=a[0].scrollHeight;const l=document.getElementsByClassName("mobile_right");l[0].scrollTop=l[0].scrollHeight}else clearInterval(fe),a()}),0)})),ia=()=>{setTimeout((()=>{Fe.abort()}),0),ce.value=!1,$e.value=!1,Be.value=!1,Ne.value=!1,We.value=!1,ne.error(q("tool.accessbusy")),D.value=q("tool.accessbusy")},sa=async()=>{try{await navigator.clipboard.writeText(D.value),ne({type:"success",message:q("tool.copysuccess")})}catch(e){ne(e)}},ra=()=>{for(let e in I)I[e]="";P.value.forEach((e=>{e.updateMessage()})),W.value.forEach((e=>{e.updateMessage()}))},ua=async()=>{if(localStorage.getItem("yudaoToken"))Ce();else try{await Q({userId:B.value.userId,userName:B.value.userName,realName:B.value.realName,avatar:B.value.avatar,plaintextUserId:B.value.plaintextUserId,mobile:B.value.mobile,email:B.value.email}).then((async e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),Ce())}))}catch(e){}};return(e,a)=>{const t=T,l=S,o=re,n=ue,i=ee("v-md-preview"),h=de;return s(),r("div",Va,[u("div",Oa,[c(Se)?(s(),r(m,{key:0},[u("div",Ea,[(s(!0),r(m,null,f(c(N),((a,t)=>(s(),r("div",{class:"flex",key:t},[(s(!0),r(m,null,f(a,((a,t)=>(s(),v(na,{key:a.variable,type:t,onPayShowStatus:Ue,label:e.$t(null==a?void 0:a.label),value:c(I)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,currentItem:x.currentItem,"onUpdate:value":e=>c(I)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRef",ref:P},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","currentItem","onUpdate:value"])))),128))])))),128)),u("div",Ra,[g(t,{onClick:ra},{default:p((()=>[y(d(e.$t("tool.clear")),1)])),_:1}),g(t,{onClick:je,loading:c(Ne),type:"primary"},{default:p((()=>[y(d(e.$t("tool.execute")),1)])),_:1},8,["loading"])])]),u("div",Ma,[u("div",La,[c(Oe).length>0||c(Me)||c(ve)?(s(),r("div",Da,[g(n,{modelValue:c(F),"onUpdate:modelValue":a[0]||(a[0]=e=>te(F)?F.value=e:null)},{default:p((()=>[g(o,{name:"1"},{title:p((()=>[c(ce)?(s(),r("div",Ha,[u("img",{src:c(_),alt:"loading"},null,8,Pa)])):(s(),r("div",Wa,[g(l,null,{default:p((()=>[g(c(ae))])),_:1})])),y(" "+d(e.$t("tool.execution_progress")),1)])),default:p((()=>[(s(!0),r(m,null,f(c(Oe),((t,l)=>(s(),r("div",{key:l,class:"process"},[u("div",Fa,d(t.title),1),a[5]||(a[5]=y("    ")),u("span",{style:{color:"#36b15e"},class:R(t.status?"":"loading-text")},d(t.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),u("div",null,[c(Me)?(s(),r("div",Ja)):b("",!0)]),c(Me)?(s(),v(o,{key:0,name:"2"},{title:p((()=>[c(Le)?(s(),r("div",Xa,[u("img",{src:c(_),alt:"loading"},null,8,Za)])):(s(),r("div",Ya,[g(l,null,{default:p((()=>[g(c(ae))])),_:1})])),y(" "+d(e.$t("tool.reasoning_process")),1)])),default:p((()=>[u("div",Ga,[u("div",Ka,d(c(Me)),1)])])),_:1})):b("",!0)])),_:1},8,["modelValue"])])):b("",!0),c(D)&&!c(De)?(s(),v(i,{key:1,text:c(D),id:"previewMd"},null,8,["text"])):b("",!0),c(De)?(s(),r("div",Qa,[y(d(c(D))+" ",1),g(t,{type:"text",onClick:ke},{default:p((()=>[y(d(e.$t("market.subscribe")),1)])),_:1})])):b("",!0),u("div",null,[c(Be)?(s(),r("img",{key:0,src:c(_),alt:"loading",class:"spinner"},null,8,et)):b("",!0),c(Be)?(s(),r("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:ye},d(e.$t("tool.stopGeneration")),1)):b("",!0),c(ze)?(s(),r("img",{key:2,onClick:sa,src:c(k),alt:"",style:{width:"20px"},class:"copy"},null,8,at)):b("",!0)])])])],64)):b("",!0)]),u("div",tt,[g(c(Aa),{active:c(M),shrink:"","line-width":"20",onClickTab:_e},{default:p((()=>[g(c(ja),{title:"输入",name:"a"},{default:p((()=>[(s(!0),r(m,null,f(c(N),((a,t)=>(s(),r("div",{class:"flex",key:t},[(s(!0),r(m,null,f(a,((a,t)=>(s(),v(na,{key:a.variable,type:t,label:e.$t(null==a?void 0:a.label),value:c(I)[null==a?void 0:a.variable],required:null==a?void 0:a.required,placeholder:`${null==a?void 0:a.label}`,max_length:null==a?void 0:a.max_length,options:null==a?void 0:a.options,fileVerify:null==a?void 0:a.allowed_file_types,"onUpdate:value":e=>c(I)[null==a?void 0:a.variable]=e,ref_for:!0,ref_key:"childRefs",ref:W},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","onUpdate:value"])))),128))])))),128)),u("div",lt,[g(t,{onClick:ra},{default:p((()=>a[6]||(a[6]=[y("Clear")]))),_:1}),g(t,{onClick:a[1]||(a[1]=e=>je()),loading:c(Ne),type:"primary"},{default:p((()=>a[7]||(a[7]=[y("Execute")]))),_:1},8,["loading"])])])),_:1}),g(c(ja),{title:"结果",name:"b",disabled:c(L)},{default:p((()=>[u("div",ot,[u("div",nt,[c(Ve).length>0||c(Me)?(s(),r("div",it,[g(n,{modelValue:c(F),"onUpdate:modelValue":a[2]||(a[2]=e=>te(F)?F.value=e:null)},{default:p((()=>[g(o,{name:"1"},{title:p((()=>[c(ce)?(s(),r("div",st,[u("img",{src:c(_),alt:"loading"},null,8,rt)])):(s(),r("div",ut,[g(l,null,{default:p((()=>[g(c(ae))])),_:1})])),y(" "+d(e.$t("tool.execution_progress")),1)])),default:p((()=>[(s(!0),r(m,null,f(c(Ve),((t,l)=>(s(),r("div",{key:l,class:"process"},[u("div",dt,d(t.title),1),a[8]||(a[8]=y("    ")),u("span",{style:{color:"#36b15e"},class:R(t.status?"":"loading-text")},d(t.status?e.$t("tool.completed"):e.$t("tool.loading")),3)])))),128))])),_:1}),a[9]||(a[9]=u("div",null,[u("div",{class:"process"})],-1)),c(Me)?(s(),v(o,{key:0,title:"推导过程",name:"2"},{title:p((()=>[c(Le)?(s(),r("div",ct,[u("img",{src:c(_),alt:"loading"},null,8,vt)])):(s(),r("div",pt,[g(l,null,{default:p((()=>[g(c(ae))])),_:1})])),y(" "+d(e.$t("tool.reasoning_process")),1)])),default:p((()=>[u("div",mt,[u("div",ft,d(c(Me)),1)])])),_:1})):b("",!0)])),_:1},8,["modelValue"])])):b("",!0),c(D)&&!c(De)?(s(),v(i,{key:1,text:c(D),id:"previewMd"},null,8,["text"])):b("",!0),c(De)?(s(),r("div",gt,[y(d(c(D))+" ",1),g(t,{type:"text",onClick:ke},{default:p((()=>[y(d(e.$t("market.subscribe")),1)])),_:1})])):b("",!0)])])])),_:1},8,["disabled"])])),_:1},8,["active"])]),c(me)?(s(),v(h,{key:0,modelValue:c(me),"onUpdate:modelValue":a[3]||(a[3]=e=>te(me)?me.value=e:null),class:"payPC","show-close":!1},{default:p((()=>[g(ea,{userInfo:c(B),appTypes:e.appTypes,currentItem:x.currentItem,onToAgreement:e.toAgreement,onClose:be,onSubscribe:we},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):b("",!0),c(me)?(s(),v(c(ta),{key:1,show:c(me),"onUpdate:show":a[4]||(a[4]=e=>te(me)?me.value=e:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:p((()=>[g(aa,{userInfo:c(B),appTypes:e.appTypes,currentItem:x.currentItem,onToAgreement:e.toAgreement,onClose:be},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])):b("",!0)])}}},[["__scopeId","data-v-8cdd9838"]]);export{ht as default};
