import{_ as e,a as l}from"./Editor-d0c40807.js";/* empty css                  *//* empty css                 */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css                    *//* empty css                  */import{a}from"./index-d3c6dae4.js";import{s as t}from"./index-0f75ba36.js";/* empty css                   */import{a as s,r as o,P as n,o as r,d as i,e as d,j as u,F as p,z as m,U as c,g as v,i as f,t as b,f as x,p as g,m as y,k as h,h as k,a8 as V,D as j,E as w,am as S,aQ as _,aR as C,aS as T}from"./index-3f661793.js";/* empty css                  */const U={class:"h-full flex overflow-auto"},M={class:"flex flex-col w-[160px] mr-4"},z=["onClick"],N={key:0,class:"w-[4px] h-full"},H={class:"flex-1"},$={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},L={class:"flex items-center my-8 overflow-hidden"},R={class:"flex items-center mr-4"},A={class:"flex items-center mr-2"},D={class:"flex items-center"},E={class:"relative flex-1"},I={class:"text-[#419eff] absolute font-bold -top-1.5 left-[30%]"},P={class:"mr-2"},Y=["innerHTML","onClick"],q=["innerHTML"],F=s({__name:"index",setup(s){const F=o("类风湿关节炎铁死亡特征基因图的构建"),G=o(""),K=o(1),Q=o(!0),W=o([]),B=o([1990,2023]),J=o({}),O=n({pageNo:1,pageSize:20}),X=o([{label:"Title",value:"title"},{label:"Keywords",value:"keyword"},{label:"Abstract",value:"abstract"},{label:"Introduction",value:"introduction"},{label:"Methods",value:"methods"},{label:"Results",value:"results"},{label:"Discussion",value:"discussion"},{label:"Acknowledge",value:"acknowleg"},{label:"全库检索(CNS)",value:"all"}]),Z=o("title"),ee=e=>{if(!F.value)return V.warning("请输入关键词或短句");1==e&&(O.pageNo=1);let l=W.value.map((e=>({field:"effect",opt:2,vals:[e],val:"",synonymsWordVos:[]})));a(Z.value,{key:F.value,page:O.pageNo-1,size:O.pageSize,allMySentence:0,allMyGroupSentence:0,synonymsHistory:0,mulSearchConditions:l,beginYear:B.value[0],endYear:B.value[1],mySentenceRange:0,sorts:[]}).then((e=>{e&&e.data&&(J.value=e.data,J.value.content=t(J.value.content))}))},le=()=>{K.value++},ae=()=>{ee()};return r((()=>{ee()})),(a,t)=>{const s=j,o=w,n=e,r=S,V=_,te=C,se=T,oe=l;return i(),d("div",U,[u("div",M,[(i(!0),d(p,null,m(v(X),((e,l)=>(i(),d("div",{class:c(["nav-item flex items-center font-bold text-[#2d3858] cursor-pointer border border-solid border-top-0 border-left-0 border-right-0 border-gray-200 h-[46px]",v(Z)==e.value?"bg-[#7e90b8]":"bg-[#f8f8f8]"]),key:l,onClick:l=>(e=>{Z.value=e.value,F.value="",J.value={},W.value=[]})(e)},[v(Z)!=e.value?(i(),d("div",N)):f("",!0),u("div",{class:c(["pl-10 h-full flex items-center",v(Z)==e.value?"border border-solid border-left-4 border-[#2b3858] border-top-0 border-bottom-0 border-right-0":""])},b(e.label),3)],10,z)))),128))]),u("div",H,[u("div",$,[x(s,{class:"h-full !text-[24px]",modelValue:v(F),"onUpdate:modelValue":t[0]||(t[0]=e=>g(F)?F.value=e:null),placeholder:"这里输入关键词或短句，中英文均可",clearable:""},null,8,["modelValue"]),x(o,{type:"primary",onClick:t[1]||(t[1]=e=>ee(1))},{default:y((()=>t[8]||(t[8]=[h("查 询")]))),_:1})]),(i(),k(n,{class:"h-[380px] mb-4",modelValue:v(G),"onUpdate:modelValue":t[2]||(t[2]=e=>g(G)?G.value=e:null),onClear:le,key:v(K)},null,8,["modelValue"])),u("div",L,[u("div",R,[u("div",{class:c(["mr-2",v(Q)?"text-[#409eff]":""])},"翻译",2),x(r,{modelValue:v(Q),"onUpdate:modelValue":t[3]||(t[3]=e=>g(Q)?Q.value=e:null)},null,8,["modelValue"])]),u("div",A,[t[12]||(t[12]=u("span",{class:"mr-2"},"影响因子：",-1)),x(te,{modelValue:v(W),"onUpdate:modelValue":t[4]||(t[4]=e=>g(W)?W.value=e:null)},{default:y((()=>[x(V,{label:"3"},{default:y((()=>t[9]||(t[9]=[h(b("<3分"))]))),_:1}),x(V,{label:"5"},{default:y((()=>t[10]||(t[10]=[h("3-10分")]))),_:1}),x(V,{label:"10"},{default:y((()=>t[11]||(t[11]=[h(b(">10分"))]))),_:1})])),_:1},8,["modelValue"])]),u("div",D,[t[13]||(t[13]=u("div",{class:"w-[60px]"},"年份范围",-1)),u("div",E,[x(se,{class:"ml-6 !w-[132px]",modelValue:v(B),"onUpdate:modelValue":t[5]||(t[5]=e=>g(B)?B.value=e:null),range:"",max:2023,min:1990,onChange:ae},null,8,["modelValue"]),u("span",I,b(`${v(B)[0]}-${v(B)[1]}`),1)])])]),u("div",null,[(i(!0),d(p,null,m(v(J).content,((e,l)=>(i(),d("div",{class:"flex mb-8",key:l},[u("div",P,b(l+1)+".",1),u("div",null,[u("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");G.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,Y),v(Q)?(i(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,q)):f("",!0)])])))),128))]),v(J)&&v(J).eleTotal?(i(),k(oe,{key:0,class:"pb-10",total:v(J).eleTotal,page:v(O).pageNo,"onUpdate:page":t[6]||(t[6]=e=>v(O).pageNo=e),limit:v(O).pageSize,"onUpdate:limit":t[7]||(t[7]=e=>v(O).pageSize=e),onPagination:ee},null,8,["total","page","limit"])):f("",!0)])])}}},[["__scopeId","data-v-1e15cf5a"]]);export{F as default};
