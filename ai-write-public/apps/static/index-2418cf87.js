import{a as e,_ as l}from"./Editor-9173a559.js";/* empty css                  *//* empty css                  *//* empty css                 */import{m as a}from"./index-cb2d0648.js";import{s as t}from"./index-2a1d440d.js";/* empty css                   */import{a as s,r as o,P as n,o as i,d as r,e as d,j as p,U as u,g as m,f as c,p as x,m as f,k as v,F as g,z as h,t as y,i as b,h as j,q as k,a8 as w,D as V,E as z,am as T}from"./index-426ff91e.js";/* empty css                  *//* empty css                  */const C={class:"flex relative h-full overflow-auto"},S={class:"w-[58%]"},U={class:"flex items-center my-4"},_={class:"flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2"},N={class:"flex items-center my-4"},q={class:"flex items-center mr-4"},H={class:"mr-2"},L=["innerHTML","onClick"],M=["innerHTML"],$={class:"flex-1 ml-4 box-right"},E=s({__name:"index",setup(s){const E=o("supplement data needed"),P=o(""),W=o(1),D=o(!0),F=o({}),I=o("请输入审稿人的意见关键词或短句（中/英）"),K=o(1),A=n({pageNo:1,pageSize:20}),B=o(344),G=e=>{E.value="",F.value={},K.value=e,I.value=1==e?"请输入审稿人的意见关键词或短句（中/英）":"请输入回复关键词或短句（中/英）"},J=e=>{if(!E.value)return w.warning("请输入文本");1==e&&(A.pageNo=1),a("sentence",{searchKey:E.value,page:A.pageNo-1,size:A.pageSize}).then((e=>{e&&e.data&&(F.value=e.data,F.value.content=t(F.value.content))}))},O=()=>{W.value++};return i((()=>{J(),B.value=document.querySelector(".box-right").offsetWidth,window.onresize=()=>{B.value=document.querySelector(".box-right").offsetWidth}})),(a,t)=>{const s=V,o=z,n=T,i=e,w=l;return r(),d("div",C,[p("div",S,[p("div",U,[p("div",{class:u(["px-4 py-1 rounded mr-4 font-bold cursor-pointer",1==m(K)?"bg-[#409eff] text-white":"bg-gray-200 text-[#606266]"]),onClick:t[0]||(t[0]=e=>G(1))}," 审稿意见 ",2),p("div",{class:u(["px-4 py-1 rounded mr-4 font-bold cursor-pointer",2==m(K)?"bg-[#409eff] text-white":"bg-gray-200 text-[#606266]"]),onClick:t[1]||(t[1]=e=>G(2))}," 回复内容 ",2)]),p("div",_,[c(s,{class:"h-full !text-[24px]",modelValue:m(E),"onUpdate:modelValue":t[2]||(t[2]=e=>x(E)?E.value=e:null),placeholder:m(I),clearable:""},null,8,["modelValue","placeholder"]),c(o,{type:"primary",onClick:t[3]||(t[3]=e=>J(1))},{default:f((()=>t[8]||(t[8]=[v("查 询")]))),_:1})]),p("div",N,[p("div",q,[p("span",{class:u(["mr-2",m(D)?"text-[#409eff]":""])},"翻译",2),c(n,{modelValue:m(D),"onUpdate:modelValue":t[4]||(t[4]=e=>x(D)?D.value=e:null)},null,8,["modelValue"])])]),p("div",null,[(r(!0),d(g,null,h(m(F).content,((e,l)=>(r(),d("div",{class:"flex mb-8",key:l},[p("div",H,y(l+1)+".",1),p("div",null,[p("div",{class:"text-[18px] text-[#5e5e5e] cursor-pointer html-value",innerHTML:e.text,onClick:a=>((e,l)=>{let a=`${e}.${l}`.replace(/\n/g,"");P.value+=`<p>${a}</p>`})(l+1,e.text)},null,8,L),m(D)?(r(),d("div",{key:0,class:"text-[16px] text-[#0a76f5] mt-4",innerHTML:e.translateText},null,8,M)):b("",!0)])])))),128))]),m(F)&&m(F).eleTotal?(r(),j(i,{key:0,class:"pb-10",total:m(F).eleTotal,page:m(A).pageNo,"onUpdate:page":t[5]||(t[5]=e=>m(A).pageNo=e),limit:m(A).pageSize,"onUpdate:limit":t[6]||(t[6]=e=>m(A).pageSize=e),onPagination:J},null,8,["total","page","limit"])):b("",!0)]),p("div",$,[(r(),j(w,{class:"h-[380px] fixed z-99",style:k({width:m(B)+"px"}),modelValue:m(P),"onUpdate:modelValue":t[7]||(t[7]=e=>x(P)?P.value=e:null),onClear:O,key:m(W)},null,8,["style","modelValue"]))])])}}},[["__scopeId","data-v-27bf6704"]]);export{E as default};
