/* empty css                  *//* empty css                     *//* empty css                 */import{a as e,b as o,u as s,Y as l,r as t,N as n,x as a,o as i,aF as c,d as r,e as d,j as u,t as p,f as m,m as g,k as f,g as I,aG as _,aH as v,B as k,aI as h,E as B,aJ as b,aK as y}from"./index-4aed3b59.js";const w={class:"bg-background text-foreground antialiased min-h-screen flex items-center justify-center"},x={class:"cl-rootBox cl-signUp-root justify-center"},S={class:"cl-cardBox cl-signUp-start"},T={class:"cl-card cl-signUp-start"},U={class:"cl-header"},j={class:"cl-headerTitle"},$={class:"cl-headerSubtitle"},A={class:"cl-main"},J={class:"cl-socialButtonsRoot"},N={class:"cl-socialButtons"},O={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},z={class:"cl-socialButtonsBlockButton-d"},C={class:"cl-socialButtons"},V={class:"cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google"},q={class:"cl-socialButtonsBlockButton-d"},L={class:"cl-dividerRow"},P={class:"cl-dividerText"},R={class:"cl-socialButtonsRoot"},G={class:"cl-internal-1pnppin"},E={class:"cl-internal-742eeh"},F={class:"cl-internal-2iusy0"},H={class:"cl-footer cl-internal-4x6jej"},M={class:"cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70"},W={class:"cl-footerActionText cl-internal-kyvqj0","data-localization-key":"signUp.start.actionText"},Z=e({__name:"index",setup(e){var Z;const{t:K}=o(),Q=s(),Y=l(),D=Q.params.socialType,X=Q.query.authCode,ee=Q.query.authState,oe=t({email:"",password:""}),se=(null==location?void 0:location.origin.includes("medon.com.cn"))||(null==location?void 0:location.origin.includes("medsci.cn")),le=t(),te=n({password:[{required:!0,message:K("tool.password_cannot_be_empty"),trigger:"blur"},{min:8,max:20,message:K("tool.pwdLength"),trigger:"blur"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,message:K("tool.password_includes_uppercase_lowercase_numbers_and_symbols"),trigger:"blur"}],email:[{required:!0,message:K("tool.email_address_cannot_be_empty"),trigger:"blur"},{type:"email",message:K("tool.please_enter_a_valid_email_address"),trigger:"blur"}]}),ne=a.get("userInfo")?null==(Z=JSON.parse(a.get("userInfo")))?void 0:Z.userId:"",ae=e=>{_(e).then((e=>{window.location.href=e}))},ie=async e=>{e&&await e.validate(((e,o)=>{e&&v(oe.value).then((e=>{if((null==e?void 0:e.token)&&(null==e?void 0:e.htoken)){localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):a.set("userInfo",JSON.stringify(e.userInfo),{expires:365});const o=window.sessionStorage.getItem("redirectUrl");o?(window.location.href=o,window.sessionStorage.removeItem("redirectUrl")):this.$router.push("/")}}))}))};return i((()=>{ne?Y.push("/"):D&&X&&ee&&c(D,X,ee).then((e=>{(null==e?void 0:e.token)&&(null==e?void 0:e.htoken)&&(localStorage.setItem("yudaoToken",e.token),localStorage.setItem("hasuraToken",e.htoken),localStorage.setItem("openid",e.openid),localStorage.setItem("socialUserId",e.socialUserId),localStorage.setItem("socialType",e.socialType),e.userInfo.userId=e.userInfo.openid,e.userInfo.plaintextUserId=e.userInfo.socialUserId,e.userInfo.token={accessToken:e.userInfo.openid,accessTokenExpireTime:63072e4,refreshToken:e.userInfo.openid},location.origin.includes(".medsci.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medsci.cn"}):location.origin.includes(".medon.com.cn")?a.set("userInfo",JSON.stringify(e.userInfo),{expires:365,domain:".medon.com.cn"}):a.set("userInfo",JSON.stringify(e.userInfo),{expires:365}),Y.push("/"))}))})),(e,o)=>{const s=k,l=h,t=B,n=b,a=y;return r(),d("div",w,[u("div",x,[u("div",S,[u("div",T,[u("div",U,[u("div",null,[u("h1",j,p(e.$t("tool.login_to_MedSci_xAI")),1),u("p",$,p(e.$t("tool.welcome_back_please_login_to_continue")),1)])]),u("div",A,[u("div",J,[u("div",N,[u("button",O,[u("span",z,[o[5]||(o[5]=u("span",null,[u("img",{src:"https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),u("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[0]||(o[0]=e=>ae(35))},p(e.$t("tool.continue_with_google")),1)])])]),u("div",C,[u("button",V,[u("span",q,[o[6]||(o[6]=u("span",null,[u("img",{src:"https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png",class:"cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google",alt:"Sign in with Google"})],-1)),u("span",{class:"cl-socialButtonsBlockButtonText",onClick:o[1]||(o[1]=e=>ae(36))},p(e.$t("tool.continue_with_facebook")),1)])])])]),u("div",L,[o[7]||(o[7]=u("div",{class:"cl-dividerLine"},null,-1)),u("p",P,p(e.$t("tool.or")),1),o[8]||(o[8]=u("div",{class:"cl-dividerLine"},null,-1))]),u("div",R,[m(n,{ref_key:"ruleFormRef",ref:le,style:{"max-width":"600px"},model:oe.value,rules:te,"label-width":"auto",class:"demo-ruleForm","label-position":"left",size:e.formSize,"status-icon":""},{default:g((()=>[m(l,{label:e.$t("tool.email"),prop:"email"},{default:g((()=>[m(s,{modelValue:oe.value.email,"onUpdate:modelValue":o[2]||(o[2]=e=>oe.value.email=e)},null,8,["modelValue"])])),_:1},8,["label"]),m(l,{label:e.$t("tool.password"),prop:"password",style:{"padding-bottom":"20px"}},{default:g((()=>[m(s,{modelValue:oe.value.password,"onUpdate:modelValue":o[3]||(o[3]=e=>oe.value.password=e),"show-password":"true"},null,8,["modelValue"])])),_:1},8,["label"]),m(l,null,{default:g((()=>[u("div",G,[o[10]||(o[10]=u("div",{id:"clerk-captcha",class:"cl-internal-3s7k9k"},null,-1)),u("div",E,[m(t,{class:"cl-formButtonPrimary cl-button cl-internal-ttumny",onClick:o[4]||(o[4]=e=>ie(le.value))},{default:g((()=>[u("span",F,[f(p(e.$t("tool.continue")),1),o[9]||(o[9]=u("svg",{class:"cl-buttonArrowIcon cl-internal-1c4ikgf"},[u("path",{fill:"currentColor",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})],-1))])])),_:1})])])])),_:1})])),_:1},8,["model","rules","size"])])])]),u("div",H,[u("div",M,[u("span",W,p(e.$t("tool.no_account_yet")),1),m(a,{href:I(se)?"/apps/sign-up":"/sign-up",class:"cl-footerActionLink"},{default:g((()=>[f(p(e.$t("tool.signUp")),1)])),_:1},8,["href"])])])])])])}}},[["__scopeId","data-v-********"]]);export{Z as default};
