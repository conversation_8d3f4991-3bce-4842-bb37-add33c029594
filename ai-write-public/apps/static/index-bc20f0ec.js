/* empty css                  *//* empty css                 */import{s as e}from"./index-c012bab4.js";import{r as a,aT as l,d as s,e as t,f as u,g as r,p as i,j as n,m as o,k as v,F as m,y as p,W as d,h as c,aV as f,i as x,a8 as y,C as j,E as h,H as g}from"./index-fd078fce.js";import{s as k}from"./index-730d9b0b.js";/* empty css                   */const w={class:"pt-10 overflow-auto h-full"},F={class:"flex justify-center my-10"},_={key:0},V=["innerHTML"],b={class:"flex justify-center mt-10"},C={__name:"index",setup(C){const E=a(""),H=a([]),S=a([]),T=a(5),z=()=>{if(a=E.value,/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi.exec(a)||!E.value)return y.warning("请输入英文内容");var a;e({text:E.value}).then((e=>{e&&e&&e.data&&(H.value=e.data,H.value=k(H.value),T.value=5,A())}))},A=()=>{let e=[];5==T.value?(e=[0,5],T.value=3):3==T.value?(e=[5,8],T.value=2):2==T.value&&(e=[8,10],T.value=5),S.value=JSON.parse(JSON.stringify(H.value)).slice(e[0],e[1])};return(e,a)=>{const y=j,k=h,C=g,H=l("copy");return s(),t("div",w,[u(y,{modelValue:r(E),"onUpdate:modelValue":a[0]||(a[0]=e=>i(E)?E.value=e:null),rows:8,type:"textarea",placeholder:"请输入不超过250单词的段落","show-word-limit":"",resize:"none",maxlength:250},null,8,["modelValue"]),n("div",F,[u(k,{type:"primary",onClick:z},{default:o((()=>a[1]||(a[1]=[v("改 写")]))),_:1})]),r(S)&&r(S).length?(s(),t("div",_,[(s(!0),t(m,null,p(r(S),((e,a)=>(s(),t("div",{class:"flex justify-center mb-6 relative",key:a},[n("span",{innerHTML:e.textShow,class:"text-[18px]"},null,8,V),d((s(),c(C,{title:"复制",size:"16",color:"#909399",class:"cursor-pointer ml-2 absolute"},{default:o((()=>[u(r(f))])),_:2},1024)),[[H,e.text]])])))),128)),n("div",b,[u(k,{type:"primary",link:"",onClick:A},{default:o((()=>a[2]||(a[2]=[v("换一换")]))),_:1})])])):x("",!0)])}}};export{C as default};
