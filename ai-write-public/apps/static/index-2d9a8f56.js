/* empty css                  */import{g as s}from"./index-22e42bb7.js";import{a as e,d as t,e as a,j as i,i as l,t as n,k as c,b as o,a1 as r,u as m,y as p,r as d,N as u,o as g,x as f,aa as w,aq as h,ar as b,s as v,l as k,a4 as _,f as y,m as x,g as I,p as S,q as F,F as T,z as A,U as C,h as j,as as U,ak as $,a3 as B,B as N,a7 as M,a8 as O,at as D,I as E,D as z,E as L,au as H,av as J,aw as q,v as V}from"./index-f8c9f3e5.js";/* empty css                 */import{_ as P}from"./index-60ab2b19.js";import{c as Q}from"./index-eff5e5fb.js";import{O as G,P as R,Q as W}from"./index-8b357fea.js";/* empty css                   */const Z={class:"bg-[#F7F7F7] bg"},K={key:0,id:"footer"},X={class:"footer-copyright1 ms-footer-copy w-footer-copy"},Y={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},ss={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};const es=e({name:"FooterNavZH",data:()=>({showFooter:!1}),head(){},computed:{},mounted(){jQuery(document).ready((function(){jQuery("#footOwl").owlCarousel({items:3,margin:17,responsiveClass:!0,dots:!0,loop:!0,autoplay:!0,autoplayTimeout:1e4,autoplayHoverPause:!0,responsive:{}})}))},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},[["render",function(s,e,c,o,r,m){return t(),a("footer",Z,[r.showFooter?(t(),a("div",K,e[0]||(e[0]=[i("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[i("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[i("div",{class:"widget-split item phone-hidden"},[i("div",{class:"widget ms-footer-img"},[i("div",null,[i("p",null,[i("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),i("p",{class:"bold w-footer-bold"}," 梅斯医学MedSci-临床医生发展平台 "),i("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"关于我们"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的业务"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的产品"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),i("div",{class:"w-footer-right phone-hidden"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"新媒体矩阵"),i("div",{id:"footOwl",class:"owl-carousel"},[i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),i("span",null,"梅斯医学")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),i("span",null,"肿瘤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),i("span",null,"血液新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),i("span",null,"风湿新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),i("span",null,"呼吸新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),i("span",null,"皮肤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),i("span",null,"神经新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),i("span",null,"消化新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),i("span",null,"心血管新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),i("span",null,"生物谷")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),i("span",null,"MedSci App")])])])])])],-1)]))):l("",!0),i("div",X,[i("p",null,[i("a",Y,n(s.$t("market.privacyPolicy")),1),e[1]||(e[1]=i("span",{style:{margin:"0px 20px"}},"|",-1)),i("a",ss,n(s.$t("market.termService")),1)])])])}],["__scopeId","data-v-3f35ee11"]]),ts={class:"bg-[#F7F7F7] bg"},as={key:0,id:"footer"},is={class:"footer-copyright ms-footer-copy w-footer-copy"},ls={href:"https://www.medsci.cn/about/index.do?id=18",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},ns={href:"https://www.medsci.cn/about/index.do?id=9",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"};const cs=e({name:"FooterNav",data:()=>({showFooter:!1}),head(){},computed:{},mounted(){jQuery(document).ready((function(){jQuery("#footOwl").owlCarousel({items:3,margin:17,responsiveClass:!0,dots:!0,loop:!0,autoplay:!0,autoplayTimeout:1e4,autoplayHoverPause:!0,responsive:{}})}))},methods:{userFeedBack(){window.open("https://www.medsci.cn/message/list.do","_blank")}}},[["render",function(s,e,o,r,m,p){return t(),a("footer",ts,[m.showFooter?(t(),a("div",as,e[1]||(e[1]=[i("div",{class:"wrapper mobile-b w-footer-wrapper",id:"foot"},[i("div",{class:"footer-widgets w-footer-widgets lets-do-4"},[i("div",{class:"widget-split item phone-hidden"},[i("div",{class:"widget ms-footer-img"},[i("div",null,[i("p",null,[i("a",{href:"https://www.medsci.cn",class:"ms-statis","ms-statis":"link"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/416a6450-b970-11ec-a1b8-6123b3ff61ea_MSLOGO1.png",alt:""})])]),i("p",{class:"bold w-footer-bold"}," 梅斯医学MedSci-临床医生发展平台 "),i("p",{class:"text-justify w-footer-des"}," 梅斯医学是面向医生的综合互联网平台，应用大数据和人工智能技术链接医生、患者、药械企业等，提供精准数字化医学传播解决方案，优化医疗生态，改善医疗质量，共创美好生活。 ")])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"关于我们"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=9",class:"ms-statis","ms-statis":"link"},"关于我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/recruit/job-list",class:"ms-statis","ms-statis":"link"},"加入我们")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=21",class:"ms-statis","ms-statis":"link"},"版权合作")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://ir.medsci.cn/zh_cn/",class:"ms-statis","ms-statis":"link"},"投资者关系")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"http://medscihealthcare.com/",class:"ms-statis","ms-statis":"link"},"MedSci Healthcare")]),i("li",{class:"ms-link iconfont"},[i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=19",class:"ms-statis","ms-statis":"link"},"友情链接")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的业务"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"真实世界研究")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"科研数智化")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/service/tree_list.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"数字化学术传播")])])])])]),i("div",{class:"widget-split item ms-footer-item phone-hidden w-footer-item"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"我们的产品"),i("div",{class:"clearfix"},[i("ul",{class:"menu left"},[i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/index.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"期刊智能查询")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/sci/nsfc.do",target:"_blank",class:"ms-statis","ms-statis":"link"},"国自然查询分析")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://www.medsci.cn/guideline/search",target:"_blank",class:"ms-statis","ms-statis":"link"},"临床指南")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://m.medsci.cn/scale",target:"_blank",class:"ms-statis","ms-statis":"link"},"医学公式计算")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://dict.bioon.com/",target:"_blank",class:"ms-statis","ms-statis":"link"},"医药生物大词典")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://class.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯精品课")]),i("li",{class:"ms-link iconfont"},[i("a",{href:"https://open.medsci.cn/",target:"_blank",class:"ms-statis","ms-statis":"link"},"梅斯公开课")])])])])]),i("div",{class:"w-footer-right phone-hidden"},[i("div",{class:"widget"},[i("h3",{class:"w-footer-h3"},"新媒体矩阵"),i("div",{id:"footOwl",class:"owl-carousel"},[i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png",alt:""}),i("span",null,"梅斯医学")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_zhongliu.png",alt:""}),i("span",null,"肿瘤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/e7b99850-9538-11ec-bca5-7f892b5df5d6_ms_xueye.jpg",alt:""}),i("span",null,"血液新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_medsci_fengshi.jpg",alt:""}),i("span",null,"风湿新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_xqy.png",alt:""}),i("span",null,"呼吸新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_pifu.jpg",alt:""}),i("span",null,"皮肤新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/bce723f0-609d-11ed-b66b-937b834e3ef9_sjqianyan.jpg",alt:""}),i("span",null,"神经新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xiaohua.jpg",alt:""}),i("span",null,"消化新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ce8fd670-93a6-11ec-bca5-7f892b5df5d6_ms_xinxueguan.jpg",alt:""}),i("span",null,"心血管新前沿")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/dc22e340-957c-11ec-bca5-7f892b5df5d6_ms_shengwugu.png",alt:""}),i("span",null,"生物谷")]),i("div",{class:"item w-owl-item"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_app.png",alt:""}),i("span",null,"MedSci App")])])])])])],-1)]))):l("",!0),i("div",is,[e[4]||(e[4]=i("p",{class:"!border-0"},"©Copyright 2012-至今 梅斯（MedSci）",-1)),i("p",null,[i("a",ls,n(s.$t("market.privacyPolicy")),1),e[2]||(e[2]=i("span",{style:{margin:"0px 20px"}},"|",-1)),i("a",ns,n(s.$t("market.termService")),1)]),e[5]||(e[5]=i("p",null,[i("a",{href:"https://www.medsci.cn/about/index.do?id=30",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},"增值电信业务经营许可证"),i("span",null,"|"),i("a",{href:"https://beian.miit.gov.cn/#/Integrated/index",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},"备案号 沪ICP备14018916号-1"),i("span",null,"|"),i("a",{href:"https://img.medsci.cn/web/prod/img/drug_licence.jpg",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},"互联网药品信息服务资格证书((沪)-非经营性-2020-0033)"),i("span",null,"|"),i("a",{href:"https://img.medsci.cn/web/prod/img/business_license.png",target:"_blank",class:"ms-link ms-statis","ms-statis":"link"},"出版物经营许可证")],-1)),e[6]||(e[6]=i("p",null,[i("a",{target:"_blank",href:"http://sh.gsxt.gov.cn/%7B4132946A291335B8DA2453690BCFB7F40AE634566C8044B6102A69977B52DD2354B87C8E2812C04DD1921F835126F4CE0B75B4DA7FFBC98F0BCAACEA6EE4A9AE849284928483A9BF95B9AFB9AFB9AF83AFB993BF95BFA9CDDBCDE7A61FBD552D044DEE8C4830F780C3A1765C767167F426AB37E5097113C4C3E9FFE9FFE9-1645622427920%7D",class:"ms-link ms-statis","ms-statis":"link"},"上海工商"),i("span",null,"|"),i("a",{target:"_blank",href:"http://cyberpolice.mps.gov.cn/wfjb/html/index.shtml",class:"ms-link ms-statis","ms-statis":"link"},"上海网警网络110"),i("span",null,"|"),i("a",{target:"_blank",href:"http://www.zx110.org/",class:"ms-link ms-statis","ms-statis":"link"},"网络社会征信网"),i("span",null,"|"),i("a",{target:"_blank",href:"http://www.12377.cn/",class:"ms-link ms-statis","ms-statis":"link"},"违法和不良信息举报中心"),i("span",null,"|"),i("a",{target:"_blank",href:"http://www.shjbzx.cn/",class:"ms-link ms-statis","ms-statis":"link"},"信息举报中心"),i("span",null,"|违法举报：021-54481353"),i("span",null,"|"),i("img",{src:"https://static.medsci.cn/public-image/ms-image/caaaa270-93a5-11ec-bca5-7f892b5df5d6_medsci_w01.png",alt:""}),i("a",{target:"_blank",href:"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402000380",class:"ms-link"},"沪公网安备 31010402000380")],-1)),e[7]||(e[7]=i("p",null," 本站旨在介绍医药健康研究进展和信息，不作为诊疗方案推荐。如需获得诊断或治疗方面指导，请前往正规医院就诊。 ",-1)),i("p",null,[e[3]||(e[3]=c(" 用户应遵守著作权法，尊重著作权人合法权益，不违法上传、存储并分享他人作品。投诉、举报、维权邮箱：<EMAIL>，")),i("a",{href:"javascript: void(0)",onClick:e[0]||(e[0]=s=>p.userFeedBack()),class:"ms-link strong"},"或在此留言")]),e[8]||(e[8]=i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=18",class:"ms-link"},"隐私保护",-1)),e[9]||(e[9]=i("a",{target:"_blank",href:"https://www.medsci.cn/about/index.do?id=14",class:"ms-link"},"免责声明",-1))])])}],["__scopeId","data-v-f745a64e"]]);function os(s,e){const t=Date.now();localStorage.setItem(s+"_value",e),localStorage.setItem(s+"_timestamp",t)}function rs(s,e){const t=s+"_value",a=s+"_timestamp",i=localStorage.getItem(t),l=localStorage.getItem(a);if(null!==i&&null!==l){const s=new Date(l);return(new Date-s)/864e5>e?(localStorage.removeItem(t),localStorage.removeItem(a),null):i}return null}function ms(){let s=rs("current_langs_pack",7),e=rs("current_langs_pack_umo",7);if(!s||!e){fetch("https://ai.medon.com.cn/dev-api/ai-base/index/getConfigPage").then((s=>{if(!s.ok)throw new Error("Network response was not ok");return s.json()})).then((e=>{if(0!==e.data.list.length){s=JSON.stringify(function(s){const e={};return s.forEach((s=>{const[t]=s.key.split("."),a=JSON.parse(s.value);e[t]||(e[t]={}),e[t]={...e[t],...a}})),e}(e.data.list)),os("current_langs_pack",s);let t=e.data.list.filter((s=>"{}"!=s.value&&s.key.includes(".dify"))).reduce(((s,e)=>(s[e.key.substr(0,e.key.indexOf("."))]||(s[e.key.substr(0,e.key.indexOf("."))]={}),s[e.key.substr(0,e.key.indexOf("."))]=JSON.parse(e.value),s)),{});os("current_langs_pack_umo",JSON.stringify(t))}})).catch((s=>{}))}}const ps={class:"bg-[#F9F9F9] overflow-auto"},ds={class:"pt-[75px] text-white mb-[30px] font-bold"},us={class:"flex justify-center"},gs={class:"content"},fs={class:"flex justify-center my-8 bg-[#F9F9F9]"},ws={class:"flex items-center"},hs=["onClick"],bs={class:"mr-2 px-4 py-1 cursor-pointer m_font"},vs={style:{color:"#757575"},href:"https://aisite.medsci.cn/",target:"_blank"},ks={key:0,class:"menu-box flex flex-wrap justify-between"},_s={class:"flex mb-1 card-item"},ys={class:"flex",style:{width:"75%","align-items":"center"}},xs=["src"],Is=["title","innerHTML"],Ss={style:{width:"30%","text-align":"right","font-size":"14px"}},Fs=["title","innerHTML"],Ts={class:"flex justify-between items-center"},As={class:"text-[#B0B0B0]"},Cs={key:0,class:"during_order"},js={key:1,class:"delay_order"},Us={key:1,class:"tab_box"},$s={class:"menu-box flex flex-wrap justify-between"},Bs={class:"flex mb-1 card-item"},Ns={class:"flex",style:{width:"75%","align-items":"center"}},Ms=["src"],Os=["title","innerHTML"],Ds={style:{width:"30%","text-align":"right"}},Es=["innerHTML"],zs={class:"flex justify-between items-center"},Ls={class:"text-[#B0B0B0]"},Hs={key:0,class:"during_order"},Js={key:1,class:"delay_order"},qs=e({__name:"index",setup(e){const{t:Z}=o();r({title:()=>"梅斯小智--梅斯医学AI智能体",meta:[{name:"keywords",content:"AI研究助手 - 您的智能研究伙伴"},{name:"description",content:"MdSci(梅斯医学)旗下梅斯小智是医药领域专属的AI智能体，包括医药写作，翻译，患者诊疗，疑难疾病诊断治疗，医药策略，在线智能医疗，中医大师，药物相互作用，心理咨询，体检报告解读等智能体。"}]});(null==location?void 0:location.origin.includes("medon.com.cn"))||null==location||location.origin.includes("medsci.cn");const K=s("基于AI的写作文本加工.png"),X=s("基于AI的写作文本加工In.png"),Y=m(),ss=p(),ts=d(""),as=d([]),is=d([{value:"我的应用",remark:"我的应用"},{value:"",remark:"全部"}]),ls=d(!1),ns=d(1),os=d(null),rs=d(null),qs=d("first"),Vs=d(null),Ps=d(!1),Qs=d({appType:"",socialUserId:"",appLang:"",order:2,isMine:2}),Gs=()=>{Ps.value=!1},Rs=async(s,e)=>{var t;let a=N();if(null==(t=rs.value)?void 0:t.userId){const t={appUuid:e,priceId:s.priceId,monthNum:s.monthNum};let a=await M(t);a&&(O({type:"success",message:Z("tool.sS")}),setTimeout((()=>{location.href=a}),1e3))}else a&&"zh-CN"!=a?ss.push("/login"):window.addLoginDom()},Ws=s=>{let e=[],t=[];1==ns.value?e=JSON.parse(JSON.stringify(Xs)):0!=ns.value?e=JSON.parse(JSON.stringify(Xs)).filter((s=>s.appType===is.value[ns.value].value)):0==ns.value&&(e=JSON.parse(JSON.stringify(as.value))),t=e.filter((e=>{if(e.appName.includes(s)||e.appDescription.includes(s)||e.mapType.includes(s))return e})),as.value.forEach((s=>{s.appName=s.appName.replace(/<[^>]+>/g,""),s.appDescription=s.appDescription.replace(/<[^>]+>/g,""),s.mapType=s.mapType.replace(/<[^>]+>/g,"")}));let a=new RegExp(s,"gi");as.value=t.map((e=>(s&&(e.appName=e.appName.replace(a,`<span style="color: #409eff">${s}</span>`),e.appDescription=e.appDescription.replace(a,`<span style="color: #409eff">${s}</span>`),e.mapType=e.mapType.replace(a,`<span style="color: #409eff">${s}</span>`)),e)))},Zs=setInterval((()=>{const s=f.get("userInfo");s!==rs.value&&(rs.value=s?JSON.parse(s):""),rs.value&&(localStorage.getItem("yudaoToken")&&clearInterval(Zs),ie())}),1e3);u((()=>clearInterval(Zs)));const Ks=async s=>{if(!(null==s?void 0:s.dAppUuid))return void O({message:"请先至后台绑定应用实例",type:"warning"});D(s.appUuid,localStorage.getItem("openid")),sessionStorage.setItem("nodeInfo",JSON.stringify(s));const e=window.location.origin.includes("medsci.cn")||window.location.origin.includes("medon.com.cn")?window.location.origin+"/apps":window.location.origin;"工具"!==s.appType?"问答"!==s.appType?"写作"===s.appType&&(await ms(),localStorage.setItem("appWrite-"+s.appUuid,JSON.stringify({appUuid:s.appUuid,directoryMd:s.directoryMd})),window.open(`${window.location.origin}/ai-write/write/${s.appUuid}`)):window.open(`${e}/chat/${null==s?void 0:s.appUuid}`,"_blank"):window.open(`${e}/tool/${null==s?void 0:s.appUuid}`,"_blank")};let Xs=[];const Ys=d(["https://img.medsci.cn/medsci-ai/bg1.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg2.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg3.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg4.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg5.png?imageMogr2/format/webp","https://img.medsci.cn/medsci-ai/bg6.png?imageMogr2/format/webp"]);g((async()=>{var s,e,t;const a=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${a}px`);["zh-CN","en","es","ja","zh-TW","vi","ko","pt","ar","id","ms"].includes(null==(s=Y.params)?void 0:s.lang)?window.localStorage.setItem("ai_apps_lang",null==(e=Y.params)?void 0:e.lang):window.localStorage.setItem("ai_apps_lang",localStorage.getItem("ai_apps_lang")?localStorage.getItem("ai_apps_lang"):navigator.browserLanguage||navigator.language),"zh-CN"==localStorage.getItem("ai_apps_lang")&&(ls.value=!0),rs.value=f.get("userInfo")?JSON.parse(f.get("userInfo")):null,rs.value||(localStorage.removeItem("yudaoToken"),localStorage.removeItem("hasuraToken")),Y.query.lang?Qs.value.appLang=h[Y.query.lang]:Qs.value.appLang=w();let i=Math.floor(6*Math.random());os.value=Ys.value[i],(null==(t=rs.value)?void 0:t.userId)?(Qs.value.socialUserId=rs.value.plaintextUserId,Qs.value.appLang=w()||location.pathname.replaceAll("/",""),ae(),ie()):(Qs.value.socialUserId=0,w()?Qs.value.appLang=w():te(location.pathname.replaceAll("/","")),ae()),await se(),(async()=>{var s,e;let t=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:(new Date).getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:null==(s=rs.value)?void 0:s.userId,userToken:"",channel:"MedSci_xAI",appId:"",userUuid:null==(e=rs.value)?void 0:e.openid}];await B.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",t)})()}));const se=()=>{b().then((s=>{is.value.push(...s)})).catch()},ee=s=>{ls.value=s},te=s=>{Qs.value.appLang=h[s],ae()},ae=()=>{v(Qs.value).then((s=>{var e,t;as.value=null==s?void 0:s.map((s=>({...s,mapType:k[s.appType]}))),""==Qs.value.appType&&(Xs=[...as.value]),1==Qs.value.isMine&&("first"==qs.value&&(as.value=null==(e=as.value)?void 0:e.filter((s=>{var e;return 1==(null==(e=s.appUser)?void 0:e.status)}))),"second"==qs.value&&(as.value=null==(t=as.value)?void 0:t.filter((s=>{var e;return 2==(null==(e=s.appUser)?void 0:e.status)}))))})).catch((s=>{}))},ie=()=>{if(localStorage.getItem("yudaoToken"))return void ae();const s=f.get("userInfo");if(s){const t=JSON.parse(s);try{_({userId:t.userId,userName:t.userName,realName:t.realName,avatar:t.avatar,plaintextUserId:t.plaintextUserId,mobile:t.mobile,email:t.email}).then((s=>{(null==s?void 0:s.token)&&(localStorage.setItem("yudaoToken",s.token),localStorage.setItem("hasuraToken",s.htoken),localStorage.setItem("openid",s.openid),localStorage.setItem("socialUserId",s.socialUserId),localStorage.setItem("socialType",s.socialType),ae())}))}catch(e){}}},le=async s=>{Vs.value=s,Ps.value=!0},ne=()=>{ae()};return(s,e)=>{const o=P,r=E,m=z,p=L,d=H,u=J,g=q,f=cs,w=es,h=V;return t(),a("div",ps,[y(o,{onGetAppLang:te,onIsZHChange:ee}),i("div",{class:"flex flex-col items-center h-[246px] relative min-w-[980px]",style:F({background:`url(${I(os)}) no-repeat center`,backgroundSize:"cover"})},[i("h1",ds,n(s.$t("faq.xAI")),1),i("div",us,[y(m,{class:"!w-[888px] !h-[54px]",modelValue:I(ts),"onUpdate:modelValue":e[0]||(e[0]=s=>S(ts)?ts.value=s:null),placeholder:s.$t("market.keywords"),clearable:"",onInput:Ws},{prefix:x((()=>[y(r,{size:"24",class:"cursor-pointer mt-[2px]"},{default:x((()=>e[4]||(e[4]=[i("img",{class:"w-[24px] h-[24px]",src:"/apps/static/搜索-8381f2c3.svg",alt:""},null,-1)]))),_:1})])),_:1},8,["modelValue","placeholder"])])],4),i("main",null,[i("div",gs,[i("div",fs,[i("div",ws,[(t(!0),a(T,null,A(I(is),((e,i)=>(t(),a("div",{class:C(["mr-2 px-4 py-1 cursor-pointer m_font",I(ns)==i?"bg-[#409eff] text-white rounded-4xl":""]),key:i,onClick:s=>(async(s,e)=>{var t;let a=await N();if(ns.value=s,ts.value="",ts.value&&Ws(ts.value),!(null==(t=rs.value)?void 0:t.userId)&&0==ns.value)return as.value=[],void(a&&"zh-CN"!=a?ss.push("/login"):window.addLoginDom());0!=ns.value?(Qs.value.isMine=2,Qs.value.order=2,"全部"==e.remark?Qs.value.appType="":Qs.value.appType=e.value,Qs.value.socialUserId=rs.value.plaintextUserId):(qs.value="first",Qs.value.appType="",Qs.value.isMine=1,Qs.value.order=1,Qs.value.socialUserId=rs.value.plaintextUserId),ae()})(i,e)},n(s.$t(`${I(k)[e.remark]}`)),11,hs)))),128)),i("div",bs,[i("a",vs,n(s.$t("tool.AINavigationSite")),1)])])]),0!=I(ns)?(t(),a("div",ks,[(t(!0),a(T,null,A(I(as),((e,o)=>(t(),j(d,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:F({background:`url(${1==e.isInternalUser?I(X):I(K)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)"}),key:o,onClick:s=>Ks(e)},{default:x((()=>{var o,m,d,u;return[i("div",_s,[i("div",ys,[i("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:e.appIcon,alt:"icon"},null,8,xs),i("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:e.appName,innerHTML:e.appName},null,8,Is)]),i("div",Ss,[y(p,{style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:$((s=>Ks(e)),["stop"])},{default:x((()=>[c(n(s.$t("market.open")),1),y(r,null,{default:x((()=>[y(I(U),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])])]),i("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",title:e.appDescription,innerHTML:e.appDescription},null,8,Fs),i("div",Ts,[i("div",As,n(s.$t(`${I(k)[e.appType]}`)),1),1==(null==(o=e.appUser)?void 0:o.status)?(t(),a("div",Cs,n(s.$t("market.subUntil"))+n(null==(m=e.appUser)?void 0:m.expireAt)+n(s.$t("market.expiredOn")),1)):l("",!0),2==(null==(d=e.appUser)?void 0:d.status)?(t(),a("div",js,n(s.$t("market.haveBeen"))+n(null==(u=e.appUser)?void 0:u.expireAt)+n(s.$t("market.expiredOn")),1)):l("",!0)])]})),_:2},1032,["style","onClick"])))),128))])):(t(),a("div",Us,[y(g,{modelValue:I(qs),"onUpdate:modelValue":e[1]||(e[1]=s=>S(qs)?qs.value=s:null),class:"demo-tabs",onTabChange:ne},{default:x((()=>[y(u,{label:s.$t("market.subscribed"),name:"first"},null,8,["label"]),y(u,{label:s.$t("market.expired"),name:"second"},null,8,["label"])])),_:1},8,["modelValue"]),i("div",$s,[(t(!0),a(T,null,A(I(as),((e,o)=>(t(),j(d,{shadow:"hover",class:"max-w-[369px] cursor-pointer mb-[16px] !bg-[#FBFBFB] !border-0 cursor-item",style:F({background:`url(${I(K)}) no-repeat center`,backgroundSize:"cover",width:"calc(33.33% - 8px)",maxHeight:"189.5px"}),key:o,onClick:s=>(s=>{var e;1==(null==(e=s.appUser)?void 0:e.status)?Ks(s):le(s)})(e)},{default:x((()=>{var o,m,d,u,g,f;return[i("div",Bs,[i("div",Ns,[i("img",{class:"w-[40px] h-[40px] block mr-2",style:{"border-radius":"10px"},src:e.appIcon,alt:"icon"},null,8,Ms),i("div",{class:"text-[16px] font-bold text-dark-200 two_lines",style:{width:"calc(100% - 40px)"},title:e.appName,innerHTML:e.appName},null,8,Os)]),i("div",Ds,[1==(null==(o=e.appUser)?void 0:o.status)?(t(),j(p,{key:0,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#2F92EE",plain:"",round:"",onClick:$((s=>Ks(e)),["stop"])},{default:x((()=>[c(n(s.$t("market.open")),1),y(r,null,{default:x((()=>[y(I(U),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):l("",!0),2==(null==(m=e.appUser)?void 0:m.status)?(t(),j(p,{key:1,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:$((s=>le(e)),["stop"])},{default:x((()=>[c(n(s.$t("market.renew")),1),y(r,null,{default:x((()=>[y(I(U),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"])):l("",!0),e.appUser?l("",!0):(t(),j(p,{key:2,style:{"--el-button-bg-color":"#fff"},size:"small",color:"#FF9A45",plain:"",round:"",onClick:$((s=>le(e)),["stop"])},{default:x((()=>[c(n(s.$t("market.subscribe")),1),y(r,null,{default:x((()=>[y(I(U),{style:{"margin-left":"4px"}})])),_:1})])),_:2},1032,["onClick"]))])]),i("div",{class:"textOverflowFour tex-[#838383] leading-[18px] h-[72px] mb-[12px]",innerHTML:e.appDescription},null,8,Es),i("div",zs,[i("div",Ls,n(s.$t(`${I(k)[e.appType]}`)),1),1==(null==(d=e.appUser)?void 0:d.status)?(t(),a("div",Hs,n(s.$t("market.subUntil"))+n(null==(u=e.appUser)?void 0:u.expireAt)+n(s.$t("market.expiredOn")),1)):l("",!0),2==(null==(g=e.appUser)?void 0:g.status)?(t(),a("div",Js,n(s.$t("market.haveBeen"))+n(null==(f=e.appUser)?void 0:f.expireAt)+n(s.$t("market.expiredOn")),1)):l("",!0)])]})),_:2},1032,["style","onClick"])))),128))])]))])]),I(ls)?(t(),j(Q,{key:0})):l("",!0),I(ls)?(t(),j(f,{key:1,class:"mobile_footer"})):l("",!0),I(ls)?l("",!0):(t(),j(w,{key:2,class:"mobile_footer"})),I(Ps)?(t(),j(h,{key:3,modelValue:I(Ps),"onUpdate:modelValue":e[2]||(e[2]=s=>S(Ps)?Ps.value=s:null),class:"payPC","show-close":!1},{default:x((()=>[y(G,{userInfo:I(rs),appTypes:I(k),currentItem:I(Vs),onToAgreement:s.toAgreement,onClose:Gs,onSubscribe:Rs},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["modelValue"])):l("",!0),y(I(W),{show:I(Ps),"onUpdate:show":e[3]||(e[3]=s=>S(Ps)?Ps.value=s:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:x((()=>[y(R,{userInfo:I(rs),appTypes:I(k),currentItem:I(Vs),onToAgreement:s.toAgreement,onClose:Gs},null,8,["userInfo","appTypes","currentItem","onToAgreement"])])),_:1},8,["show"])])}}},[["__scopeId","data-v-45777684"]]);export{qs as default};
