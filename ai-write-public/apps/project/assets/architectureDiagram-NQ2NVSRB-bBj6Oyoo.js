import{aL as Je,_ as nt,g as Qe,s as Ke,b as je,c as _e,t as tr,q as er,I as rr,$ as ir,E as ar,F as nr,G as or,z as sr,l as Pe,ao as Ne,d as Le,aM as Ee,e as hr,aN as lr,aO as fr}from"./index-7BgHCHvm.js";import{p as cr}from"./chunk-353BL4L5-DFVVyPpX.js";import{I as gr}from"./chunk-AACKK3MU-BqsOwJbr.js";import{p as ur}from"./treemap-FKARHQ26-ypv3uYu1.js";import{c as Ge}from"./cytoscape.esm-BY7v1xhs.js";import"./_baseUniq-DHIrjp6z.js";import"./_basePickBy-CQby-Lu8.js";import"./clone-DQL0PpFl.js";var le={exports:{}},fe={exports:{}},ce={exports:{}},dr=ce.exports,xe;function vr(){return xe||(xe=1,function(C,X){(function(G,L){C.exports=L()})(dr,function(){return function(A){var G={};function L(g){if(G[g])return G[g].exports;var l=G[g]={i:g,l:!1,exports:{}};return A[g].call(l.exports,l,l.exports,L),l.l=!0,l.exports}return L.m=A,L.c=G,L.i=function(g){return g},L.d=function(g,l,a){L.o(g,l)||Object.defineProperty(g,l,{configurable:!1,enumerable:!0,get:a})},L.n=function(g){var l=g&&g.__esModule?function(){return g.default}:function(){return g};return L.d(l,"a",l),l},L.o=function(g,l){return Object.prototype.hasOwnProperty.call(g,l)},L.p="",L(L.s=28)}([function(A,G,L){function g(){}g.QUALITY=1,g.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,g.DEFAULT_INCREMENTAL=!1,g.DEFAULT_ANIMATION_ON_LAYOUT=!0,g.DEFAULT_ANIMATION_DURING_LAYOUT=!1,g.DEFAULT_ANIMATION_PERIOD=50,g.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,g.DEFAULT_GRAPH_MARGIN=15,g.NODE_DIMENSIONS_INCLUDE_LABELS=!1,g.SIMPLE_NODE_SIZE=40,g.SIMPLE_NODE_HALF_SIZE=g.SIMPLE_NODE_SIZE/2,g.EMPTY_COMPOUND_NODE_SIZE=40,g.MIN_EDGE_LENGTH=1,g.WORLD_BOUNDARY=1e6,g.INITIAL_WORLD_BOUNDARY=g.WORLD_BOUNDARY/1e3,g.WORLD_CENTER_X=1200,g.WORLD_CENTER_Y=900,A.exports=g},function(A,G,L){var g=L(2),l=L(8),a=L(9);function r(f,i,u){g.call(this,u),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=u,this.bendpoints=[],this.source=f,this.target=i}r.prototype=Object.create(g.prototype);for(var e in g)r[e]=g[e];r.prototype.getSource=function(){return this.source},r.prototype.getTarget=function(){return this.target},r.prototype.isInterGraph=function(){return this.isInterGraph},r.prototype.getLength=function(){return this.length},r.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},r.prototype.getBendpoints=function(){return this.bendpoints},r.prototype.getLca=function(){return this.lca},r.prototype.getSourceInLca=function(){return this.sourceInLca},r.prototype.getTargetInLca=function(){return this.targetInLca},r.prototype.getOtherEnd=function(f){if(this.source===f)return this.target;if(this.target===f)return this.source;throw"Node is not incident with this edge"},r.prototype.getOtherEndInGraph=function(f,i){for(var u=this.getOtherEnd(f),t=i.getGraphManager().getRoot();;){if(u.getOwner()==i)return u;if(u.getOwner()==t)break;u=u.getOwner().getParent()}return null},r.prototype.updateLength=function(){var f=new Array(4);this.isOverlapingSourceAndTarget=l.getIntersection(this.target.getRect(),this.source.getRect(),f),this.isOverlapingSourceAndTarget||(this.lengthX=f[0]-f[2],this.lengthY=f[1]-f[3],Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},r.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=a.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=a.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},A.exports=r},function(A,G,L){function g(l){this.vGraphObject=l}A.exports=g},function(A,G,L){var g=L(2),l=L(10),a=L(13),r=L(0),e=L(16),f=L(5);function i(t,s,o,c){o==null&&c==null&&(c=s),g.call(this,c),t.graphManager!=null&&(t=t.graphManager),this.estimatedSize=l.MIN_VALUE,this.inclusionTreeDepth=l.MAX_VALUE,this.vGraphObject=c,this.edges=[],this.graphManager=t,o!=null&&s!=null?this.rect=new a(s.x,s.y,o.width,o.height):this.rect=new a}i.prototype=Object.create(g.prototype);for(var u in g)i[u]=g[u];i.prototype.getEdges=function(){return this.edges},i.prototype.getChild=function(){return this.child},i.prototype.getOwner=function(){return this.owner},i.prototype.getWidth=function(){return this.rect.width},i.prototype.setWidth=function(t){this.rect.width=t},i.prototype.getHeight=function(){return this.rect.height},i.prototype.setHeight=function(t){this.rect.height=t},i.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},i.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},i.prototype.getCenter=function(){return new f(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},i.prototype.getLocation=function(){return new f(this.rect.x,this.rect.y)},i.prototype.getRect=function(){return this.rect},i.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},i.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},i.prototype.setRect=function(t,s){this.rect.x=t.x,this.rect.y=t.y,this.rect.width=s.width,this.rect.height=s.height},i.prototype.setCenter=function(t,s){this.rect.x=t-this.rect.width/2,this.rect.y=s-this.rect.height/2},i.prototype.setLocation=function(t,s){this.rect.x=t,this.rect.y=s},i.prototype.moveBy=function(t,s){this.rect.x+=t,this.rect.y+=s},i.prototype.getEdgeListToNode=function(t){var s=[],o=this;return o.edges.forEach(function(c){if(c.target==t){if(c.source!=o)throw"Incorrect edge source!";s.push(c)}}),s},i.prototype.getEdgesBetween=function(t){var s=[],o=this;return o.edges.forEach(function(c){if(!(c.source==o||c.target==o))throw"Incorrect edge source and/or target";(c.target==t||c.source==t)&&s.push(c)}),s},i.prototype.getNeighborsList=function(){var t=new Set,s=this;return s.edges.forEach(function(o){if(o.source==s)t.add(o.target);else{if(o.target!=s)throw"Incorrect incidency!";t.add(o.source)}}),t},i.prototype.withChildren=function(){var t=new Set,s,o;if(t.add(this),this.child!=null)for(var c=this.child.getNodes(),h=0;h<c.length;h++)s=c[h],o=s.withChildren(),o.forEach(function(T){t.add(T)});return t},i.prototype.getNoOfChildren=function(){var t=0,s;if(this.child==null)t=1;else for(var o=this.child.getNodes(),c=0;c<o.length;c++)s=o[c],t+=s.getNoOfChildren();return t==0&&(t=1),t},i.prototype.getEstimatedSize=function(){if(this.estimatedSize==l.MIN_VALUE)throw"assert failed";return this.estimatedSize},i.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},i.prototype.scatter=function(){var t,s,o=-r.INITIAL_WORLD_BOUNDARY,c=r.INITIAL_WORLD_BOUNDARY;t=r.WORLD_CENTER_X+e.nextDouble()*(c-o)+o;var h=-r.INITIAL_WORLD_BOUNDARY,T=r.INITIAL_WORLD_BOUNDARY;s=r.WORLD_CENTER_Y+e.nextDouble()*(T-h)+h,this.rect.x=t,this.rect.y=s},i.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var t=this.getChild();if(t.updateBounds(!0),this.rect.x=t.getLeft(),this.rect.y=t.getTop(),this.setWidth(t.getRight()-t.getLeft()),this.setHeight(t.getBottom()-t.getTop()),r.NODE_DIMENSIONS_INCLUDE_LABELS){var s=t.getRight()-t.getLeft(),o=t.getBottom()-t.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(s+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>s?(this.rect.x-=(this.labelWidth-s)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(s+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(o+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>o?(this.rect.y-=(this.labelHeight-o)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(o+this.labelHeight))}}},i.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==l.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},i.prototype.transform=function(t){var s=this.rect.x;s>r.WORLD_BOUNDARY?s=r.WORLD_BOUNDARY:s<-r.WORLD_BOUNDARY&&(s=-r.WORLD_BOUNDARY);var o=this.rect.y;o>r.WORLD_BOUNDARY?o=r.WORLD_BOUNDARY:o<-r.WORLD_BOUNDARY&&(o=-r.WORLD_BOUNDARY);var c=new f(s,o),h=t.inverseTransformPoint(c);this.setLocation(h.x,h.y)},i.prototype.getLeft=function(){return this.rect.x},i.prototype.getRight=function(){return this.rect.x+this.rect.width},i.prototype.getTop=function(){return this.rect.y},i.prototype.getBottom=function(){return this.rect.y+this.rect.height},i.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},A.exports=i},function(A,G,L){var g=L(0);function l(){}for(var a in g)l[a]=g[a];l.MAX_ITERATIONS=2500,l.DEFAULT_EDGE_LENGTH=50,l.DEFAULT_SPRING_STRENGTH=.45,l.DEFAULT_REPULSION_STRENGTH=4500,l.DEFAULT_GRAVITY_STRENGTH=.4,l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,l.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,l.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,l.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,l.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,l.COOLING_ADAPTATION_FACTOR=.33,l.ADAPTATION_LOWER_NODE_LIMIT=1e3,l.ADAPTATION_UPPER_NODE_LIMIT=5e3,l.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,l.MAX_NODE_DISPLACEMENT=l.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,l.MIN_REPULSION_DIST=l.DEFAULT_EDGE_LENGTH/10,l.CONVERGENCE_CHECK_PERIOD=100,l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,l.MIN_EDGE_LENGTH=1,l.GRID_CALCULATION_CHECK_PERIOD=10,A.exports=l},function(A,G,L){function g(l,a){l==null&&a==null?(this.x=0,this.y=0):(this.x=l,this.y=a)}g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.setX=function(l){this.x=l},g.prototype.setY=function(l){this.y=l},g.prototype.getDifference=function(l){return new DimensionD(this.x-l.x,this.y-l.y)},g.prototype.getCopy=function(){return new g(this.x,this.y)},g.prototype.translate=function(l){return this.x+=l.width,this.y+=l.height,this},A.exports=g},function(A,G,L){var g=L(2),l=L(10),a=L(0),r=L(7),e=L(3),f=L(1),i=L(13),u=L(12),t=L(11);function s(c,h,T){g.call(this,T),this.estimatedSize=l.MIN_VALUE,this.margin=a.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=c,h!=null&&h instanceof r?this.graphManager=h:h!=null&&h instanceof Layout&&(this.graphManager=h.graphManager)}s.prototype=Object.create(g.prototype);for(var o in g)s[o]=g[o];s.prototype.getNodes=function(){return this.nodes},s.prototype.getEdges=function(){return this.edges},s.prototype.getGraphManager=function(){return this.graphManager},s.prototype.getParent=function(){return this.parent},s.prototype.getLeft=function(){return this.left},s.prototype.getRight=function(){return this.right},s.prototype.getTop=function(){return this.top},s.prototype.getBottom=function(){return this.bottom},s.prototype.isConnected=function(){return this.isConnected},s.prototype.add=function(c,h,T){if(h==null&&T==null){var v=c;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(v)>-1)throw"Node already in graph!";return v.owner=this,this.getNodes().push(v),v}else{var d=c;if(!(this.getNodes().indexOf(h)>-1&&this.getNodes().indexOf(T)>-1))throw"Source or target not in graph!";if(!(h.owner==T.owner&&h.owner==this))throw"Both owners must be this graph!";return h.owner!=T.owner?null:(d.source=h,d.target=T,d.isInterGraph=!1,this.getEdges().push(d),h.edges.push(d),T!=h&&T.edges.push(d),d)}},s.prototype.remove=function(c){var h=c;if(c instanceof e){if(h==null)throw"Node is null!";if(!(h.owner!=null&&h.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var T=h.edges.slice(),v,d=T.length,N=0;N<d;N++)v=T[N],v.isInterGraph?this.graphManager.remove(v):v.source.owner.remove(v);var S=this.nodes.indexOf(h);if(S==-1)throw"Node not in owner node list!";this.nodes.splice(S,1)}else if(c instanceof f){var v=c;if(v==null)throw"Edge is null!";if(!(v.source!=null&&v.target!=null))throw"Source and/or target is null!";if(!(v.source.owner!=null&&v.target.owner!=null&&v.source.owner==this&&v.target.owner==this))throw"Source and/or target owner is invalid!";var M=v.source.edges.indexOf(v),P=v.target.edges.indexOf(v);if(!(M>-1&&P>-1))throw"Source and/or target doesn't know this edge!";v.source.edges.splice(M,1),v.target!=v.source&&v.target.edges.splice(P,1);var S=v.source.owner.getEdges().indexOf(v);if(S==-1)throw"Not in owner's edge list!";v.source.owner.getEdges().splice(S,1)}},s.prototype.updateLeftTop=function(){for(var c=l.MAX_VALUE,h=l.MAX_VALUE,T,v,d,N=this.getNodes(),S=N.length,M=0;M<S;M++){var P=N[M];T=P.getTop(),v=P.getLeft(),c>T&&(c=T),h>v&&(h=v)}return c==l.MAX_VALUE?null:(N[0].getParent().paddingLeft!=null?d=N[0].getParent().paddingLeft:d=this.margin,this.left=h-d,this.top=c-d,new u(this.left,this.top))},s.prototype.updateBounds=function(c){for(var h=l.MAX_VALUE,T=-l.MAX_VALUE,v=l.MAX_VALUE,d=-l.MAX_VALUE,N,S,M,P,K,Y=this.nodes,k=Y.length,D=0;D<k;D++){var rt=Y[D];c&&rt.child!=null&&rt.updateBounds(),N=rt.getLeft(),S=rt.getRight(),M=rt.getTop(),P=rt.getBottom(),h>N&&(h=N),T<S&&(T=S),v>M&&(v=M),d<P&&(d=P)}var n=new i(h,v,T-h,d-v);h==l.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),Y[0].getParent().paddingLeft!=null?K=Y[0].getParent().paddingLeft:K=this.margin,this.left=n.x-K,this.right=n.x+n.width+K,this.top=n.y-K,this.bottom=n.y+n.height+K},s.calculateBounds=function(c){for(var h=l.MAX_VALUE,T=-l.MAX_VALUE,v=l.MAX_VALUE,d=-l.MAX_VALUE,N,S,M,P,K=c.length,Y=0;Y<K;Y++){var k=c[Y];N=k.getLeft(),S=k.getRight(),M=k.getTop(),P=k.getBottom(),h>N&&(h=N),T<S&&(T=S),v>M&&(v=M),d<P&&(d=P)}var D=new i(h,v,T-h,d-v);return D},s.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},s.prototype.getEstimatedSize=function(){if(this.estimatedSize==l.MIN_VALUE)throw"assert failed";return this.estimatedSize},s.prototype.calcEstimatedSize=function(){for(var c=0,h=this.nodes,T=h.length,v=0;v<T;v++){var d=h[v];c+=d.calcEstimatedSize()}return c==0?this.estimatedSize=a.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=c/Math.sqrt(this.nodes.length),this.estimatedSize},s.prototype.updateConnected=function(){var c=this;if(this.nodes.length==0){this.isConnected=!0;return}var h=new t,T=new Set,v=this.nodes[0],d,N,S=v.withChildren();for(S.forEach(function(D){h.push(D),T.add(D)});h.length!==0;){v=h.shift(),d=v.getEdges();for(var M=d.length,P=0;P<M;P++){var K=d[P];if(N=K.getOtherEndInGraph(v,this),N!=null&&!T.has(N)){var Y=N.withChildren();Y.forEach(function(D){h.push(D),T.add(D)})}}}if(this.isConnected=!1,T.size>=this.nodes.length){var k=0;T.forEach(function(D){D.owner==c&&k++}),k==this.nodes.length&&(this.isConnected=!0)}},A.exports=s},function(A,G,L){var g,l=L(1);function a(r){g=L(6),this.layout=r,this.graphs=[],this.edges=[]}a.prototype.addRoot=function(){var r=this.layout.newGraph(),e=this.layout.newNode(null),f=this.add(r,e);return this.setRootGraph(f),this.rootGraph},a.prototype.add=function(r,e,f,i,u){if(f==null&&i==null&&u==null){if(r==null)throw"Graph is null!";if(e==null)throw"Parent node is null!";if(this.graphs.indexOf(r)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(r),r.parent!=null)throw"Already has a parent!";if(e.child!=null)throw"Already has a child!";return r.parent=e,e.child=r,r}else{u=f,i=e,f=r;var t=i.getOwner(),s=u.getOwner();if(!(t!=null&&t.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(s!=null&&s.getGraphManager()==this))throw"Target not in this graph mgr!";if(t==s)return f.isInterGraph=!1,t.add(f,i,u);if(f.isInterGraph=!0,f.source=i,f.target=u,this.edges.indexOf(f)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(f),!(f.source!=null&&f.target!=null))throw"Edge source and/or target is null!";if(!(f.source.edges.indexOf(f)==-1&&f.target.edges.indexOf(f)==-1))throw"Edge already in source and/or target incidency list!";return f.source.edges.push(f),f.target.edges.push(f),f}},a.prototype.remove=function(r){if(r instanceof g){var e=r;if(e.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(e==this.rootGraph||e.parent!=null&&e.parent.graphManager==this))throw"Invalid parent node!";var f=[];f=f.concat(e.getEdges());for(var i,u=f.length,t=0;t<u;t++)i=f[t],e.remove(i);var s=[];s=s.concat(e.getNodes());var o;u=s.length;for(var t=0;t<u;t++)o=s[t],e.remove(o);e==this.rootGraph&&this.setRootGraph(null);var c=this.graphs.indexOf(e);this.graphs.splice(c,1),e.parent=null}else if(r instanceof l){if(i=r,i==null)throw"Edge is null!";if(!i.isInterGraph)throw"Not an inter-graph edge!";if(!(i.source!=null&&i.target!=null))throw"Source and/or target is null!";if(!(i.source.edges.indexOf(i)!=-1&&i.target.edges.indexOf(i)!=-1))throw"Source and/or target doesn't know this edge!";var c=i.source.edges.indexOf(i);if(i.source.edges.splice(c,1),c=i.target.edges.indexOf(i),i.target.edges.splice(c,1),!(i.source.owner!=null&&i.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(i.source.owner.getGraphManager().edges.indexOf(i)==-1)throw"Not in owner graph manager's edge list!";var c=i.source.owner.getGraphManager().edges.indexOf(i);i.source.owner.getGraphManager().edges.splice(c,1)}},a.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},a.prototype.getGraphs=function(){return this.graphs},a.prototype.getAllNodes=function(){if(this.allNodes==null){for(var r=[],e=this.getGraphs(),f=e.length,i=0;i<f;i++)r=r.concat(e[i].getNodes());this.allNodes=r}return this.allNodes},a.prototype.resetAllNodes=function(){this.allNodes=null},a.prototype.resetAllEdges=function(){this.allEdges=null},a.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},a.prototype.getAllEdges=function(){if(this.allEdges==null){var r=[],e=this.getGraphs();e.length;for(var f=0;f<e.length;f++)r=r.concat(e[f].getEdges());r=r.concat(this.edges),this.allEdges=r}return this.allEdges},a.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},a.prototype.setAllNodesToApplyGravitation=function(r){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=r},a.prototype.getRoot=function(){return this.rootGraph},a.prototype.setRootGraph=function(r){if(r.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=r,r.parent==null&&(r.parent=this.layout.newNode("Root node"))},a.prototype.getLayout=function(){return this.layout},a.prototype.isOneAncestorOfOther=function(r,e){if(!(r!=null&&e!=null))throw"assert failed";if(r==e)return!0;var f=r.getOwner(),i;do{if(i=f.getParent(),i==null)break;if(i==e)return!0;if(f=i.getOwner(),f==null)break}while(!0);f=e.getOwner();do{if(i=f.getParent(),i==null)break;if(i==r)return!0;if(f=i.getOwner(),f==null)break}while(!0);return!1},a.prototype.calcLowestCommonAncestors=function(){for(var r,e,f,i,u,t=this.getAllEdges(),s=t.length,o=0;o<s;o++){if(r=t[o],e=r.source,f=r.target,r.lca=null,r.sourceInLca=e,r.targetInLca=f,e==f){r.lca=e.getOwner();continue}for(i=e.getOwner();r.lca==null;){for(r.targetInLca=f,u=f.getOwner();r.lca==null;){if(u==i){r.lca=u;break}if(u==this.rootGraph)break;if(r.lca!=null)throw"assert failed";r.targetInLca=u.getParent(),u=r.targetInLca.getOwner()}if(i==this.rootGraph)break;r.lca==null&&(r.sourceInLca=i.getParent(),i=r.sourceInLca.getOwner())}if(r.lca==null)throw"assert failed"}},a.prototype.calcLowestCommonAncestor=function(r,e){if(r==e)return r.getOwner();var f=r.getOwner();do{if(f==null)break;var i=e.getOwner();do{if(i==null)break;if(i==f)return i;i=i.getParent().getOwner()}while(!0);f=f.getParent().getOwner()}while(!0);return f},a.prototype.calcInclusionTreeDepths=function(r,e){r==null&&e==null&&(r=this.rootGraph,e=1);for(var f,i=r.getNodes(),u=i.length,t=0;t<u;t++)f=i[t],f.inclusionTreeDepth=e,f.child!=null&&this.calcInclusionTreeDepths(f.child,e+1)},a.prototype.includesInvalidEdge=function(){for(var r,e=[],f=this.edges.length,i=0;i<f;i++)r=this.edges[i],this.isOneAncestorOfOther(r.source,r.target)&&e.push(r);for(var i=0;i<e.length;i++)this.remove(e[i]);return!1},A.exports=a},function(A,G,L){var g=L(12);function l(){}l.calcSeparationAmount=function(a,r,e,f){if(!a.intersects(r))throw"assert failed";var i=new Array(2);this.decideDirectionsForOverlappingNodes(a,r,i),e[0]=Math.min(a.getRight(),r.getRight())-Math.max(a.x,r.x),e[1]=Math.min(a.getBottom(),r.getBottom())-Math.max(a.y,r.y),a.getX()<=r.getX()&&a.getRight()>=r.getRight()?e[0]+=Math.min(r.getX()-a.getX(),a.getRight()-r.getRight()):r.getX()<=a.getX()&&r.getRight()>=a.getRight()&&(e[0]+=Math.min(a.getX()-r.getX(),r.getRight()-a.getRight())),a.getY()<=r.getY()&&a.getBottom()>=r.getBottom()?e[1]+=Math.min(r.getY()-a.getY(),a.getBottom()-r.getBottom()):r.getY()<=a.getY()&&r.getBottom()>=a.getBottom()&&(e[1]+=Math.min(a.getY()-r.getY(),r.getBottom()-a.getBottom()));var u=Math.abs((r.getCenterY()-a.getCenterY())/(r.getCenterX()-a.getCenterX()));r.getCenterY()===a.getCenterY()&&r.getCenterX()===a.getCenterX()&&(u=1);var t=u*e[0],s=e[1]/u;e[0]<s?s=e[0]:t=e[1],e[0]=-1*i[0]*(s/2+f),e[1]=-1*i[1]*(t/2+f)},l.decideDirectionsForOverlappingNodes=function(a,r,e){a.getCenterX()<r.getCenterX()?e[0]=-1:e[0]=1,a.getCenterY()<r.getCenterY()?e[1]=-1:e[1]=1},l.getIntersection2=function(a,r,e){var f=a.getCenterX(),i=a.getCenterY(),u=r.getCenterX(),t=r.getCenterY();if(a.intersects(r))return e[0]=f,e[1]=i,e[2]=u,e[3]=t,!0;var s=a.getX(),o=a.getY(),c=a.getRight(),h=a.getX(),T=a.getBottom(),v=a.getRight(),d=a.getWidthHalf(),N=a.getHeightHalf(),S=r.getX(),M=r.getY(),P=r.getRight(),K=r.getX(),Y=r.getBottom(),k=r.getRight(),D=r.getWidthHalf(),rt=r.getHeightHalf(),n=!1,m=!1;if(f===u){if(i>t)return e[0]=f,e[1]=o,e[2]=u,e[3]=Y,!1;if(i<t)return e[0]=f,e[1]=T,e[2]=u,e[3]=M,!1}else if(i===t){if(f>u)return e[0]=s,e[1]=i,e[2]=P,e[3]=t,!1;if(f<u)return e[0]=c,e[1]=i,e[2]=S,e[3]=t,!1}else{var p=a.height/a.width,E=r.height/r.width,y=(t-i)/(u-f),I=void 0,w=void 0,R=void 0,W=void 0,x=void 0,q=void 0;if(-p===y?f>u?(e[0]=h,e[1]=T,n=!0):(e[0]=c,e[1]=o,n=!0):p===y&&(f>u?(e[0]=s,e[1]=o,n=!0):(e[0]=v,e[1]=T,n=!0)),-E===y?u>f?(e[2]=K,e[3]=Y,m=!0):(e[2]=P,e[3]=M,m=!0):E===y&&(u>f?(e[2]=S,e[3]=M,m=!0):(e[2]=k,e[3]=Y,m=!0)),n&&m)return!1;if(f>u?i>t?(I=this.getCardinalDirection(p,y,4),w=this.getCardinalDirection(E,y,2)):(I=this.getCardinalDirection(-p,y,3),w=this.getCardinalDirection(-E,y,1)):i>t?(I=this.getCardinalDirection(-p,y,1),w=this.getCardinalDirection(-E,y,3)):(I=this.getCardinalDirection(p,y,2),w=this.getCardinalDirection(E,y,4)),!n)switch(I){case 1:W=o,R=f+-N/y,e[0]=R,e[1]=W;break;case 2:R=v,W=i+d*y,e[0]=R,e[1]=W;break;case 3:W=T,R=f+N/y,e[0]=R,e[1]=W;break;case 4:R=h,W=i+-d*y,e[0]=R,e[1]=W;break}if(!m)switch(w){case 1:q=M,x=u+-rt/y,e[2]=x,e[3]=q;break;case 2:x=k,q=t+D*y,e[2]=x,e[3]=q;break;case 3:q=Y,x=u+rt/y,e[2]=x,e[3]=q;break;case 4:x=K,q=t+-D*y,e[2]=x,e[3]=q;break}}return!1},l.getCardinalDirection=function(a,r,e){return a>r?e:1+e%4},l.getIntersection=function(a,r,e,f){if(f==null)return this.getIntersection2(a,r,e);var i=a.x,u=a.y,t=r.x,s=r.y,o=e.x,c=e.y,h=f.x,T=f.y,v=void 0,d=void 0,N=void 0,S=void 0,M=void 0,P=void 0,K=void 0,Y=void 0,k=void 0;return N=s-u,M=i-t,K=t*u-i*s,S=T-c,P=o-h,Y=h*c-o*T,k=N*P-S*M,k===0?null:(v=(M*Y-P*K)/k,d=(S*K-N*Y)/k,new g(v,d))},l.angleOfVector=function(a,r,e,f){var i=void 0;return a!==e?(i=Math.atan((f-r)/(e-a)),e<a?i+=Math.PI:f<r&&(i+=this.TWO_PI)):f<r?i=this.ONE_AND_HALF_PI:i=this.HALF_PI,i},l.doIntersect=function(a,r,e,f){var i=a.x,u=a.y,t=r.x,s=r.y,o=e.x,c=e.y,h=f.x,T=f.y,v=(t-i)*(T-c)-(h-o)*(s-u);if(v===0)return!1;var d=((T-c)*(h-i)+(o-h)*(T-u))/v,N=((u-s)*(h-i)+(t-i)*(T-u))/v;return 0<d&&d<1&&0<N&&N<1},l.findCircleLineIntersections=function(a,r,e,f,i,u,t){var s=(e-a)*(e-a)+(f-r)*(f-r),o=2*((a-i)*(e-a)+(r-u)*(f-r)),c=(a-i)*(a-i)+(r-u)*(r-u)-t*t,h=o*o-4*s*c;if(h>=0){var T=(-o+Math.sqrt(o*o-4*s*c))/(2*s),v=(-o-Math.sqrt(o*o-4*s*c))/(2*s),d=null;return T>=0&&T<=1?[T]:v>=0&&v<=1?[v]:d}else return null},l.HALF_PI=.5*Math.PI,l.ONE_AND_HALF_PI=1.5*Math.PI,l.TWO_PI=2*Math.PI,l.THREE_PI=3*Math.PI,A.exports=l},function(A,G,L){function g(){}g.sign=function(l){return l>0?1:l<0?-1:0},g.floor=function(l){return l<0?Math.ceil(l):Math.floor(l)},g.ceil=function(l){return l<0?Math.floor(l):Math.ceil(l)},A.exports=g},function(A,G,L){function g(){}g.MAX_VALUE=2147483647,g.MIN_VALUE=-2147483648,A.exports=g},function(A,G,L){var g=function(){function i(u,t){for(var s=0;s<t.length;s++){var o=t[s];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(u,o.key,o)}}return function(u,t,s){return t&&i(u.prototype,t),s&&i(u,s),u}}();function l(i,u){if(!(i instanceof u))throw new TypeError("Cannot call a class as a function")}var a=function(u){return{value:u,next:null,prev:null}},r=function(u,t,s,o){return u!==null?u.next=t:o.head=t,s!==null?s.prev=t:o.tail=t,t.prev=u,t.next=s,o.length++,t},e=function(u,t){var s=u.prev,o=u.next;return s!==null?s.next=o:t.head=o,o!==null?o.prev=s:t.tail=s,u.prev=u.next=null,t.length--,u},f=function(){function i(u){var t=this;l(this,i),this.length=0,this.head=null,this.tail=null,u!=null&&u.forEach(function(s){return t.push(s)})}return g(i,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(t,s){return r(s.prev,a(t),s,this)}},{key:"insertAfter",value:function(t,s){return r(s,a(t),s.next,this)}},{key:"insertNodeBefore",value:function(t,s){return r(s.prev,t,s,this)}},{key:"insertNodeAfter",value:function(t,s){return r(s,t,s.next,this)}},{key:"push",value:function(t){return r(this.tail,a(t),null,this)}},{key:"unshift",value:function(t){return r(null,a(t),this.head,this)}},{key:"remove",value:function(t){return e(t,this)}},{key:"pop",value:function(){return e(this.tail,this).value}},{key:"popNode",value:function(){return e(this.tail,this)}},{key:"shift",value:function(){return e(this.head,this).value}},{key:"shiftNode",value:function(){return e(this.head,this)}},{key:"get_object_at",value:function(t){if(t<=this.length()){for(var s=1,o=this.head;s<t;)o=o.next,s++;return o.value}}},{key:"set_object_at",value:function(t,s){if(t<=this.length()){for(var o=1,c=this.head;o<t;)c=c.next,o++;c.value=s}}}]),i}();A.exports=f},function(A,G,L){function g(l,a,r){this.x=null,this.y=null,l==null&&a==null&&r==null?(this.x=0,this.y=0):typeof l=="number"&&typeof a=="number"&&r==null?(this.x=l,this.y=a):l.constructor.name=="Point"&&a==null&&r==null&&(r=l,this.x=r.x,this.y=r.y)}g.prototype.getX=function(){return this.x},g.prototype.getY=function(){return this.y},g.prototype.getLocation=function(){return new g(this.x,this.y)},g.prototype.setLocation=function(l,a,r){l.constructor.name=="Point"&&a==null&&r==null?(r=l,this.setLocation(r.x,r.y)):typeof l=="number"&&typeof a=="number"&&r==null&&(parseInt(l)==l&&parseInt(a)==a?this.move(l,a):(this.x=Math.floor(l+.5),this.y=Math.floor(a+.5)))},g.prototype.move=function(l,a){this.x=l,this.y=a},g.prototype.translate=function(l,a){this.x+=l,this.y+=a},g.prototype.equals=function(l){if(l.constructor.name=="Point"){var a=l;return this.x==a.x&&this.y==a.y}return this==l},g.prototype.toString=function(){return new g().constructor.name+"[x="+this.x+",y="+this.y+"]"},A.exports=g},function(A,G,L){function g(l,a,r,e){this.x=0,this.y=0,this.width=0,this.height=0,l!=null&&a!=null&&r!=null&&e!=null&&(this.x=l,this.y=a,this.width=r,this.height=e)}g.prototype.getX=function(){return this.x},g.prototype.setX=function(l){this.x=l},g.prototype.getY=function(){return this.y},g.prototype.setY=function(l){this.y=l},g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(l){this.width=l},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(l){this.height=l},g.prototype.getRight=function(){return this.x+this.width},g.prototype.getBottom=function(){return this.y+this.height},g.prototype.intersects=function(l){return!(this.getRight()<l.x||this.getBottom()<l.y||l.getRight()<this.x||l.getBottom()<this.y)},g.prototype.getCenterX=function(){return this.x+this.width/2},g.prototype.getMinX=function(){return this.getX()},g.prototype.getMaxX=function(){return this.getX()+this.width},g.prototype.getCenterY=function(){return this.y+this.height/2},g.prototype.getMinY=function(){return this.getY()},g.prototype.getMaxY=function(){return this.getY()+this.height},g.prototype.getWidthHalf=function(){return this.width/2},g.prototype.getHeightHalf=function(){return this.height/2},A.exports=g},function(A,G,L){var g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a};function l(){}l.lastID=0,l.createID=function(a){return l.isPrimitive(a)?a:(a.uniqueID!=null||(a.uniqueID=l.getString(),l.lastID++),a.uniqueID)},l.getString=function(a){return a==null&&(a=l.lastID),"Object#"+a},l.isPrimitive=function(a){var r=typeof a>"u"?"undefined":g(a);return a==null||r!="object"&&r!="function"},A.exports=l},function(A,G,L){function g(o){if(Array.isArray(o)){for(var c=0,h=Array(o.length);c<o.length;c++)h[c]=o[c];return h}else return Array.from(o)}var l=L(0),a=L(7),r=L(3),e=L(1),f=L(6),i=L(5),u=L(17),t=L(29);function s(o){t.call(this),this.layoutQuality=l.QUALITY,this.createBendsAsNeeded=l.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=l.DEFAULT_INCREMENTAL,this.animationOnLayout=l.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=l.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=l.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=l.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new a(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,o!=null&&(this.isRemoteUse=o)}s.RANDOM_SEED=1,s.prototype=Object.create(t.prototype),s.prototype.getGraphManager=function(){return this.graphManager},s.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},s.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},s.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},s.prototype.newGraphManager=function(){var o=new a(this);return this.graphManager=o,o},s.prototype.newGraph=function(o){return new f(null,this.graphManager,o)},s.prototype.newNode=function(o){return new r(this.graphManager,o)},s.prototype.newEdge=function(o){return new e(null,null,o)},s.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},s.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var o;return this.checkLayoutSuccess()?o=!1:o=this.layout(),l.ANIMATE==="during"?!1:(o&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,o)},s.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},s.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var o=this.graphManager.getAllEdges(),c=0;c<o.length;c++)o[c];for(var h=this.graphManager.getRoot().getNodes(),c=0;c<h.length;c++)h[c];this.update(this.graphManager.getRoot())}},s.prototype.update=function(o){if(o==null)this.update2();else if(o instanceof r){var c=o;if(c.getChild()!=null)for(var h=c.getChild().getNodes(),T=0;T<h.length;T++)update(h[T]);if(c.vGraphObject!=null){var v=c.vGraphObject;v.update(c)}}else if(o instanceof e){var d=o;if(d.vGraphObject!=null){var N=d.vGraphObject;N.update(d)}}else if(o instanceof f){var S=o;if(S.vGraphObject!=null){var M=S.vGraphObject;M.update(S)}}},s.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=l.QUALITY,this.animationDuringLayout=l.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=l.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=l.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=l.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=l.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=l.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},s.prototype.transform=function(o){if(o==null)this.transform(new i(0,0));else{var c=new u,h=this.graphManager.getRoot().updateLeftTop();if(h!=null){c.setWorldOrgX(o.x),c.setWorldOrgY(o.y),c.setDeviceOrgX(h.x),c.setDeviceOrgY(h.y);for(var T=this.getAllNodes(),v,d=0;d<T.length;d++)v=T[d],v.transform(c)}}},s.prototype.positionNodesRandomly=function(o){if(o==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var c,h,T=o.getNodes(),v=0;v<T.length;v++)c=T[v],h=c.getChild(),h==null||h.getNodes().length==0?c.scatter():(this.positionNodesRandomly(h),c.updateBounds())},s.prototype.getFlatForest=function(){for(var o=[],c=!0,h=this.graphManager.getRoot().getNodes(),T=!0,v=0;v<h.length;v++)h[v].getChild()!=null&&(T=!1);if(!T)return o;var d=new Set,N=[],S=new Map,M=[];for(M=M.concat(h);M.length>0&&c;){for(N.push(M[0]);N.length>0&&c;){var P=N[0];N.splice(0,1),d.add(P);for(var K=P.getEdges(),v=0;v<K.length;v++){var Y=K[v].getOtherEnd(P);if(S.get(P)!=Y)if(!d.has(Y))N.push(Y),S.set(Y,P);else{c=!1;break}}}if(!c)o=[];else{var k=[].concat(g(d));o.push(k);for(var v=0;v<k.length;v++){var D=k[v],rt=M.indexOf(D);rt>-1&&M.splice(rt,1)}d=new Set,S=new Map}}return o},s.prototype.createDummyNodesForBendpoints=function(o){for(var c=[],h=o.source,T=this.graphManager.calcLowestCommonAncestor(o.source,o.target),v=0;v<o.bendpoints.length;v++){var d=this.newNode(null);d.setRect(new Point(0,0),new Dimension(1,1)),T.add(d);var N=this.newEdge(null);this.graphManager.add(N,h,d),c.add(d),h=d}var N=this.newEdge(null);return this.graphManager.add(N,h,o.target),this.edgeToDummyNodes.set(o,c),o.isInterGraph()?this.graphManager.remove(o):T.remove(o),c},s.prototype.createBendpointsFromDummyNodes=function(){var o=[];o=o.concat(this.graphManager.getAllEdges()),o=[].concat(g(this.edgeToDummyNodes.keys())).concat(o);for(var c=0;c<o.length;c++){var h=o[c];if(h.bendpoints.length>0){for(var T=this.edgeToDummyNodes.get(h),v=0;v<T.length;v++){var d=T[v],N=new i(d.getCenterX(),d.getCenterY()),S=h.bendpoints.get(v);S.x=N.x,S.y=N.y,d.getOwner().remove(d)}this.graphManager.add(h,h.source,h.target)}}},s.transform=function(o,c,h,T){if(h!=null&&T!=null){var v=c;if(o<=50){var d=c/h;v-=(c-d)/50*(50-o)}else{var N=c*T;v+=(N-c)/50*(o-50)}return v}else{var S,M;return o<=50?(S=9*c/500,M=c/10):(S=9*c/50,M=-8*c),S*o+M}},s.findCenterOfTree=function(o){var c=[];c=c.concat(o);var h=[],T=new Map,v=!1,d=null;(c.length==1||c.length==2)&&(v=!0,d=c[0]);for(var N=0;N<c.length;N++){var S=c[N],M=S.getNeighborsList().size;T.set(S,S.getNeighborsList().size),M==1&&h.push(S)}var P=[];for(P=P.concat(h);!v;){var K=[];K=K.concat(P),P=[];for(var N=0;N<c.length;N++){var S=c[N],Y=c.indexOf(S);Y>=0&&c.splice(Y,1);var k=S.getNeighborsList();k.forEach(function(n){if(h.indexOf(n)<0){var m=T.get(n),p=m-1;p==1&&P.push(n),T.set(n,p)}})}h=h.concat(P),(c.length==1||c.length==2)&&(v=!0,d=c[0])}return d},s.prototype.setGraphManager=function(o){this.graphManager=o},A.exports=s},function(A,G,L){function g(){}g.seed=1,g.x=0,g.nextDouble=function(){return g.x=Math.sin(g.seed++)*1e4,g.x-Math.floor(g.x)},A.exports=g},function(A,G,L){var g=L(5);function l(a,r){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}l.prototype.getWorldOrgX=function(){return this.lworldOrgX},l.prototype.setWorldOrgX=function(a){this.lworldOrgX=a},l.prototype.getWorldOrgY=function(){return this.lworldOrgY},l.prototype.setWorldOrgY=function(a){this.lworldOrgY=a},l.prototype.getWorldExtX=function(){return this.lworldExtX},l.prototype.setWorldExtX=function(a){this.lworldExtX=a},l.prototype.getWorldExtY=function(){return this.lworldExtY},l.prototype.setWorldExtY=function(a){this.lworldExtY=a},l.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},l.prototype.setDeviceOrgX=function(a){this.ldeviceOrgX=a},l.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},l.prototype.setDeviceOrgY=function(a){this.ldeviceOrgY=a},l.prototype.getDeviceExtX=function(){return this.ldeviceExtX},l.prototype.setDeviceExtX=function(a){this.ldeviceExtX=a},l.prototype.getDeviceExtY=function(){return this.ldeviceExtY},l.prototype.setDeviceExtY=function(a){this.ldeviceExtY=a},l.prototype.transformX=function(a){var r=0,e=this.lworldExtX;return e!=0&&(r=this.ldeviceOrgX+(a-this.lworldOrgX)*this.ldeviceExtX/e),r},l.prototype.transformY=function(a){var r=0,e=this.lworldExtY;return e!=0&&(r=this.ldeviceOrgY+(a-this.lworldOrgY)*this.ldeviceExtY/e),r},l.prototype.inverseTransformX=function(a){var r=0,e=this.ldeviceExtX;return e!=0&&(r=this.lworldOrgX+(a-this.ldeviceOrgX)*this.lworldExtX/e),r},l.prototype.inverseTransformY=function(a){var r=0,e=this.ldeviceExtY;return e!=0&&(r=this.lworldOrgY+(a-this.ldeviceOrgY)*this.lworldExtY/e),r},l.prototype.inverseTransformPoint=function(a){var r=new g(this.inverseTransformX(a.x),this.inverseTransformY(a.y));return r},A.exports=l},function(A,G,L){function g(t){if(Array.isArray(t)){for(var s=0,o=Array(t.length);s<t.length;s++)o[s]=t[s];return o}else return Array.from(t)}var l=L(15),a=L(4),r=L(0),e=L(8),f=L(9);function i(){l.call(this),this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=a.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=a.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=a.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=a.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=a.MAX_ITERATIONS}i.prototype=Object.create(l.prototype);for(var u in l)i[u]=l[u];i.prototype.initParameters=function(){l.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=a.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},i.prototype.calcIdealEdgeLengths=function(){for(var t,s,o,c,h,T,v,d=this.getGraphManager().getAllEdges(),N=0;N<d.length;N++)t=d[N],s=t.idealLength,t.isInterGraph&&(c=t.getSource(),h=t.getTarget(),T=t.getSourceInLca().getEstimatedSize(),v=t.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(t.idealLength+=T+v-2*r.SIMPLE_NODE_SIZE),o=t.getLca().getInclusionTreeDepth(),t.idealLength+=s*a.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(c.getInclusionTreeDepth()+h.getInclusionTreeDepth()-2*o))},i.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;this.incremental?(t>a.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*a.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-a.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT_INCREMENTAL):(t>a.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(a.COOLING_ADAPTATION_FACTOR,1-(t-a.ADAPTATION_LOWER_NODE_LIMIT)/(a.ADAPTATION_UPPER_NODE_LIMIT-a.ADAPTATION_LOWER_NODE_LIMIT)*(1-a.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=a.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.displacementThresholdPerNode=3*a.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},i.prototype.calcSpringForces=function(){for(var t=this.getAllEdges(),s,o=0;o<t.length;o++)s=t[o],this.calcSpringForce(s,s.idealLength)},i.prototype.calcRepulsionForces=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,o,c,h,T,v=this.getAllNodes(),d;if(this.useFRGridVariant)for(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&t&&this.updateGrid(),d=new Set,o=0;o<v.length;o++)h=v[o],this.calculateRepulsionForceOfANode(h,d,t,s),d.add(h);else for(o=0;o<v.length;o++)for(h=v[o],c=o+1;c<v.length;c++)T=v[c],h.getOwner()==T.getOwner()&&this.calcRepulsionForce(h,T)},i.prototype.calcGravitationalForces=function(){for(var t,s=this.getAllNodesToApplyGravitation(),o=0;o<s.length;o++)t=s[o],this.calcGravitationalForce(t)},i.prototype.moveNodes=function(){for(var t=this.getAllNodes(),s,o=0;o<t.length;o++)s=t[o],s.move()},i.prototype.calcSpringForce=function(t,s){var o=t.getSource(),c=t.getTarget(),h,T,v,d;if(this.uniformLeafNodeSizes&&o.getChild()==null&&c.getChild()==null)t.updateLengthSimple();else if(t.updateLength(),t.isOverlapingSourceAndTarget)return;h=t.getLength(),h!=0&&(T=t.edgeElasticity*(h-s),v=T*(t.lengthX/h),d=T*(t.lengthY/h),o.springForceX+=v,o.springForceY+=d,c.springForceX-=v,c.springForceY-=d)},i.prototype.calcRepulsionForce=function(t,s){var o=t.getRect(),c=s.getRect(),h=new Array(2),T=new Array(4),v,d,N,S,M,P,K;if(o.intersects(c)){e.calcSeparationAmount(o,c,h,a.DEFAULT_EDGE_LENGTH/2),P=2*h[0],K=2*h[1];var Y=t.noOfChildren*s.noOfChildren/(t.noOfChildren+s.noOfChildren);t.repulsionForceX-=Y*P,t.repulsionForceY-=Y*K,s.repulsionForceX+=Y*P,s.repulsionForceY+=Y*K}else this.uniformLeafNodeSizes&&t.getChild()==null&&s.getChild()==null?(v=c.getCenterX()-o.getCenterX(),d=c.getCenterY()-o.getCenterY()):(e.getIntersection(o,c,T),v=T[2]-T[0],d=T[3]-T[1]),Math.abs(v)<a.MIN_REPULSION_DIST&&(v=f.sign(v)*a.MIN_REPULSION_DIST),Math.abs(d)<a.MIN_REPULSION_DIST&&(d=f.sign(d)*a.MIN_REPULSION_DIST),N=v*v+d*d,S=Math.sqrt(N),M=(t.nodeRepulsion/2+s.nodeRepulsion/2)*t.noOfChildren*s.noOfChildren/N,P=M*v/S,K=M*d/S,t.repulsionForceX-=P,t.repulsionForceY-=K,s.repulsionForceX+=P,s.repulsionForceY+=K},i.prototype.calcGravitationalForce=function(t){var s,o,c,h,T,v,d,N;s=t.getOwner(),o=(s.getRight()+s.getLeft())/2,c=(s.getTop()+s.getBottom())/2,h=t.getCenterX()-o,T=t.getCenterY()-c,v=Math.abs(h)+t.getWidth()/2,d=Math.abs(T)+t.getHeight()/2,t.getOwner()==this.graphManager.getRoot()?(N=s.getEstimatedSize()*this.gravityRangeFactor,(v>N||d>N)&&(t.gravitationForceX=-this.gravityConstant*h,t.gravitationForceY=-this.gravityConstant*T)):(N=s.getEstimatedSize()*this.compoundGravityRangeFactor,(v>N||d>N)&&(t.gravitationForceX=-this.gravityConstant*h*this.compoundGravityConstant,t.gravitationForceY=-this.gravityConstant*T*this.compoundGravityConstant))},i.prototype.isConverged=function(){var t,s=!1;return this.totalIterations>this.maxIterations/3&&(s=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),t=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,t||s},i.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},i.prototype.calcNoOfChildrenForAllNodes=function(){for(var t,s=this.graphManager.getAllNodes(),o=0;o<s.length;o++)t=s[o],t.noOfChildren=t.getNoOfChildren()},i.prototype.calcGrid=function(t){var s=0,o=0;s=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange)),o=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));for(var c=new Array(s),h=0;h<s;h++)c[h]=new Array(o);for(var h=0;h<s;h++)for(var T=0;T<o;T++)c[h][T]=new Array;return c},i.prototype.addNodeToGrid=function(t,s,o){var c=0,h=0,T=0,v=0;c=parseInt(Math.floor((t.getRect().x-s)/this.repulsionRange)),h=parseInt(Math.floor((t.getRect().width+t.getRect().x-s)/this.repulsionRange)),T=parseInt(Math.floor((t.getRect().y-o)/this.repulsionRange)),v=parseInt(Math.floor((t.getRect().height+t.getRect().y-o)/this.repulsionRange));for(var d=c;d<=h;d++)for(var N=T;N<=v;N++)this.grid[d][N].push(t),t.setGridCoordinates(c,h,T,v)},i.prototype.updateGrid=function(){var t,s,o=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),t=0;t<o.length;t++)s=o[t],this.addNodeToGrid(s,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},i.prototype.calculateRepulsionForceOfANode=function(t,s,o,c){if(this.totalIterations%a.GRID_CALCULATION_CHECK_PERIOD==1&&o||c){var h=new Set;t.surrounding=new Array;for(var T,v=this.grid,d=t.startX-1;d<t.finishX+2;d++)for(var N=t.startY-1;N<t.finishY+2;N++)if(!(d<0||N<0||d>=v.length||N>=v[0].length)){for(var S=0;S<v[d][N].length;S++)if(T=v[d][N][S],!(t.getOwner()!=T.getOwner()||t==T)&&!s.has(T)&&!h.has(T)){var M=Math.abs(t.getCenterX()-T.getCenterX())-(t.getWidth()/2+T.getWidth()/2),P=Math.abs(t.getCenterY()-T.getCenterY())-(t.getHeight()/2+T.getHeight()/2);M<=this.repulsionRange&&P<=this.repulsionRange&&h.add(T)}}t.surrounding=[].concat(g(h))}for(d=0;d<t.surrounding.length;d++)this.calcRepulsionForce(t,t.surrounding[d])},i.prototype.calcRepulsionRange=function(){return 0},A.exports=i},function(A,G,L){var g=L(1),l=L(4);function a(e,f,i){g.call(this,e,f,i),this.idealLength=l.DEFAULT_EDGE_LENGTH,this.edgeElasticity=l.DEFAULT_SPRING_STRENGTH}a.prototype=Object.create(g.prototype);for(var r in g)a[r]=g[r];A.exports=a},function(A,G,L){var g=L(3),l=L(4);function a(e,f,i,u){g.call(this,e,f,i,u),this.nodeRepulsion=l.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}a.prototype=Object.create(g.prototype);for(var r in g)a[r]=g[r];a.prototype.setGridCoordinates=function(e,f,i,u){this.startX=e,this.finishX=f,this.startY=i,this.finishY=u},A.exports=a},function(A,G,L){function g(l,a){this.width=0,this.height=0,l!==null&&a!==null&&(this.height=a,this.width=l)}g.prototype.getWidth=function(){return this.width},g.prototype.setWidth=function(l){this.width=l},g.prototype.getHeight=function(){return this.height},g.prototype.setHeight=function(l){this.height=l},A.exports=g},function(A,G,L){var g=L(14);function l(){this.map={},this.keys=[]}l.prototype.put=function(a,r){var e=g.createID(a);this.contains(e)||(this.map[e]=r,this.keys.push(a))},l.prototype.contains=function(a){return g.createID(a),this.map[a]!=null},l.prototype.get=function(a){var r=g.createID(a);return this.map[r]},l.prototype.keySet=function(){return this.keys},A.exports=l},function(A,G,L){var g=L(14);function l(){this.set={}}l.prototype.add=function(a){var r=g.createID(a);this.contains(r)||(this.set[r]=a)},l.prototype.remove=function(a){delete this.set[g.createID(a)]},l.prototype.clear=function(){this.set={}},l.prototype.contains=function(a){return this.set[g.createID(a)]==a},l.prototype.isEmpty=function(){return this.size()===0},l.prototype.size=function(){return Object.keys(this.set).length},l.prototype.addAllTo=function(a){for(var r=Object.keys(this.set),e=r.length,f=0;f<e;f++)a.push(this.set[r[f]])},l.prototype.size=function(){return Object.keys(this.set).length},l.prototype.addAll=function(a){for(var r=a.length,e=0;e<r;e++){var f=a[e];this.add(f)}},A.exports=l},function(A,G,L){function g(){}g.multMat=function(l,a){for(var r=[],e=0;e<l.length;e++){r[e]=[];for(var f=0;f<a[0].length;f++){r[e][f]=0;for(var i=0;i<l[0].length;i++)r[e][f]+=l[e][i]*a[i][f]}}return r},g.transpose=function(l){for(var a=[],r=0;r<l[0].length;r++){a[r]=[];for(var e=0;e<l.length;e++)a[r][e]=l[e][r]}return a},g.multCons=function(l,a){for(var r=[],e=0;e<l.length;e++)r[e]=l[e]*a;return r},g.minusOp=function(l,a){for(var r=[],e=0;e<l.length;e++)r[e]=l[e]-a[e];return r},g.dotProduct=function(l,a){for(var r=0,e=0;e<l.length;e++)r+=l[e]*a[e];return r},g.mag=function(l){return Math.sqrt(this.dotProduct(l,l))},g.normalize=function(l){for(var a=[],r=this.mag(l),e=0;e<l.length;e++)a[e]=l[e]/r;return a},g.multGamma=function(l){for(var a=[],r=0,e=0;e<l.length;e++)r+=l[e];r*=-1/l.length;for(var f=0;f<l.length;f++)a[f]=r+l[f];return a},g.multL=function(l,a,r){for(var e=[],f=[],i=[],u=0;u<a[0].length;u++){for(var t=0,s=0;s<a.length;s++)t+=-.5*a[s][u]*l[s];f[u]=t}for(var o=0;o<r.length;o++){for(var c=0,h=0;h<r.length;h++)c+=r[o][h]*f[h];i[o]=c}for(var T=0;T<a.length;T++){for(var v=0,d=0;d<a[0].length;d++)v+=a[T][d]*i[d];e[T]=v}return e},A.exports=g},function(A,G,L){var g=function(){function e(f,i){for(var u=0;u<i.length;u++){var t=i[u];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(f,t.key,t)}}return function(f,i,u){return i&&e(f.prototype,i),u&&e(f,u),f}}();function l(e,f){if(!(e instanceof f))throw new TypeError("Cannot call a class as a function")}var a=L(11),r=function(){function e(f,i){l(this,e),(i!==null||i!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var u=void 0;f instanceof a?u=f.size():u=f.length,this._quicksort(f,0,u-1)}return g(e,[{key:"_quicksort",value:function(i,u,t){if(u<t){var s=this._partition(i,u,t);this._quicksort(i,u,s),this._quicksort(i,s+1,t)}}},{key:"_partition",value:function(i,u,t){for(var s=this._get(i,u),o=u,c=t;;){for(;this.compareFunction(s,this._get(i,c));)c--;for(;this.compareFunction(this._get(i,o),s);)o++;if(o<c)this._swap(i,o,c),o++,c--;else return c}}},{key:"_get",value:function(i,u){return i instanceof a?i.get_object_at(u):i[u]}},{key:"_set",value:function(i,u,t){i instanceof a?i.set_object_at(u,t):i[u]=t}},{key:"_swap",value:function(i,u,t){var s=this._get(i,u);this._set(i,u,this._get(i,t)),this._set(i,t,s)}},{key:"_defaultCompareFunction",value:function(i,u){return u>i}}]),e}();A.exports=r},function(A,G,L){function g(){}g.svd=function(l){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=l.length,this.n=l[0].length;var a=Math.min(this.m,this.n);this.s=function(Nt){for(var Mt=[];Nt-- >0;)Mt.push(0);return Mt}(Math.min(this.m+1,this.n)),this.U=function(Nt){var Mt=function Zt(Gt){if(Gt.length==0)return 0;for(var $t=[],Ft=0;Ft<Gt[0];Ft++)$t.push(Zt(Gt.slice(1)));return $t};return Mt(Nt)}([this.m,a]),this.V=function(Nt){var Mt=function Zt(Gt){if(Gt.length==0)return 0;for(var $t=[],Ft=0;Ft<Gt[0];Ft++)$t.push(Zt(Gt.slice(1)));return $t};return Mt(Nt)}([this.n,this.n]);for(var r=function(Nt){for(var Mt=[];Nt-- >0;)Mt.push(0);return Mt}(this.n),e=function(Nt){for(var Mt=[];Nt-- >0;)Mt.push(0);return Mt}(this.m),f=!0,i=Math.min(this.m-1,this.n),u=Math.max(0,Math.min(this.n-2,this.m)),t=0;t<Math.max(i,u);t++){if(t<i){this.s[t]=0;for(var s=t;s<this.m;s++)this.s[t]=g.hypot(this.s[t],l[s][t]);if(this.s[t]!==0){l[t][t]<0&&(this.s[t]=-this.s[t]);for(var o=t;o<this.m;o++)l[o][t]/=this.s[t];l[t][t]+=1}this.s[t]=-this.s[t]}for(var c=t+1;c<this.n;c++){if(function(Nt,Mt){return Nt&&Mt}(t<i,this.s[t]!==0)){for(var h=0,T=t;T<this.m;T++)h+=l[T][t]*l[T][c];h=-h/l[t][t];for(var v=t;v<this.m;v++)l[v][c]+=h*l[v][t]}r[c]=l[t][c]}if(function(Nt,Mt){return Mt}(f,t<i))for(var d=t;d<this.m;d++)this.U[d][t]=l[d][t];if(t<u){r[t]=0;for(var N=t+1;N<this.n;N++)r[t]=g.hypot(r[t],r[N]);if(r[t]!==0){r[t+1]<0&&(r[t]=-r[t]);for(var S=t+1;S<this.n;S++)r[S]/=r[t];r[t+1]+=1}if(r[t]=-r[t],function(Nt,Mt){return Nt&&Mt}(t+1<this.m,r[t]!==0)){for(var M=t+1;M<this.m;M++)e[M]=0;for(var P=t+1;P<this.n;P++)for(var K=t+1;K<this.m;K++)e[K]+=r[P]*l[K][P];for(var Y=t+1;Y<this.n;Y++)for(var k=-r[Y]/r[t+1],D=t+1;D<this.m;D++)l[D][Y]+=k*e[D]}for(var rt=t+1;rt<this.n;rt++)this.V[rt][t]=r[rt]}}var n=Math.min(this.n,this.m+1);i<this.n&&(this.s[i]=l[i][i]),this.m<n&&(this.s[n-1]=0),u+1<n&&(r[u]=l[u][n-1]),r[n-1]=0;{for(var m=i;m<a;m++){for(var p=0;p<this.m;p++)this.U[p][m]=0;this.U[m][m]=1}for(var E=i-1;E>=0;E--)if(this.s[E]!==0){for(var y=E+1;y<a;y++){for(var I=0,w=E;w<this.m;w++)I+=this.U[w][E]*this.U[w][y];I=-I/this.U[E][E];for(var R=E;R<this.m;R++)this.U[R][y]+=I*this.U[R][E]}for(var W=E;W<this.m;W++)this.U[W][E]=-this.U[W][E];this.U[E][E]=1+this.U[E][E];for(var x=0;x<E-1;x++)this.U[x][E]=0}else{for(var q=0;q<this.m;q++)this.U[q][E]=0;this.U[E][E]=1}}for(var V=this.n-1;V>=0;V--){if(function(Nt,Mt){return Nt&&Mt}(V<u,r[V]!==0))for(var U=V+1;U<a;U++){for(var et=0,z=V+1;z<this.n;z++)et+=this.V[z][V]*this.V[z][U];et=-et/this.V[V+1][V];for(var O=V+1;O<this.n;O++)this.V[O][U]+=et*this.V[O][V]}for(var H=0;H<this.n;H++)this.V[H][V]=0;this.V[V][V]=1}for(var B=n-1,_=Math.pow(2,-52),lt=Math.pow(2,-966);n>0;){var J=void 0,Rt=void 0;for(J=n-2;J>=-1&&J!==-1;J--)if(Math.abs(r[J])<=lt+_*(Math.abs(this.s[J])+Math.abs(this.s[J+1]))){r[J]=0;break}if(J===n-2)Rt=4;else{var Lt=void 0;for(Lt=n-1;Lt>=J&&Lt!==J;Lt--){var vt=(Lt!==n?Math.abs(r[Lt]):0)+(Lt!==J+1?Math.abs(r[Lt-1]):0);if(Math.abs(this.s[Lt])<=lt+_*vt){this.s[Lt]=0;break}}Lt===J?Rt=3:Lt===n-1?Rt=1:(Rt=2,J=Lt)}switch(J++,Rt){case 1:{var it=r[n-2];r[n-2]=0;for(var ut=n-2;ut>=J;ut--){var Tt=g.hypot(this.s[ut],it),At=this.s[ut]/Tt,Dt=it/Tt;this.s[ut]=Tt,ut!==J&&(it=-Dt*r[ut-1],r[ut-1]=At*r[ut-1]);for(var mt=0;mt<this.n;mt++)Tt=At*this.V[mt][ut]+Dt*this.V[mt][n-1],this.V[mt][n-1]=-Dt*this.V[mt][ut]+At*this.V[mt][n-1],this.V[mt][ut]=Tt}}break;case 2:{var xt=r[J-1];r[J-1]=0;for(var St=J;St<n;St++){var Vt=g.hypot(this.s[St],xt),Xt=this.s[St]/Vt,Ut=xt/Vt;this.s[St]=Vt,xt=-Ut*r[St],r[St]=Xt*r[St];for(var bt=0;bt<this.m;bt++)Vt=Xt*this.U[bt][St]+Ut*this.U[bt][J-1],this.U[bt][J-1]=-Ut*this.U[bt][St]+Xt*this.U[bt][J-1],this.U[bt][St]=Vt}}break;case 3:{var Ht=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[n-1]),Math.abs(this.s[n-2])),Math.abs(r[n-2])),Math.abs(this.s[J])),Math.abs(r[J])),Bt=this.s[n-1]/Ht,F=this.s[n-2]/Ht,b=r[n-2]/Ht,$=this.s[J]/Ht,Q=r[J]/Ht,Z=((F+Bt)*(F-Bt)+b*b)/2,at=Bt*b*(Bt*b),gt=0;(function(Nt,Mt){return Nt||Mt})(Z!==0,at!==0)&&(gt=Math.sqrt(Z*Z+at),Z<0&&(gt=-gt),gt=at/(Z+gt));for(var ot=($+Bt)*($-Bt)+gt,tt=$*Q,j=J;j<n-1;j++){var dt=g.hypot(ot,tt),wt=ot/dt,yt=tt/dt;j!==J&&(r[j-1]=dt),ot=wt*this.s[j]+yt*r[j],r[j]=wt*r[j]-yt*this.s[j],tt=yt*this.s[j+1],this.s[j+1]=wt*this.s[j+1];for(var It=0;It<this.n;It++)dt=wt*this.V[It][j]+yt*this.V[It][j+1],this.V[It][j+1]=-yt*this.V[It][j]+wt*this.V[It][j+1],this.V[It][j]=dt;if(dt=g.hypot(ot,tt),wt=ot/dt,yt=tt/dt,this.s[j]=dt,ot=wt*r[j]+yt*this.s[j+1],this.s[j+1]=-yt*r[j]+wt*this.s[j+1],tt=yt*r[j+1],r[j+1]=wt*r[j+1],j<this.m-1)for(var ft=0;ft<this.m;ft++)dt=wt*this.U[ft][j]+yt*this.U[ft][j+1],this.U[ft][j+1]=-yt*this.U[ft][j]+wt*this.U[ft][j+1],this.U[ft][j]=dt}r[n-2]=ot}break;case 4:{if(this.s[J]<=0){this.s[J]=this.s[J]<0?-this.s[J]:0;for(var st=0;st<=B;st++)this.V[st][J]=-this.V[st][J]}for(;J<B&&!(this.s[J]>=this.s[J+1]);){var Ct=this.s[J];if(this.s[J]=this.s[J+1],this.s[J+1]=Ct,J<this.n-1)for(var ct=0;ct<this.n;ct++)Ct=this.V[ct][J+1],this.V[ct][J+1]=this.V[ct][J],this.V[ct][J]=Ct;if(J<this.m-1)for(var ht=0;ht<this.m;ht++)Ct=this.U[ht][J+1],this.U[ht][J+1]=this.U[ht][J],this.U[ht][J]=Ct;J++}n--}break}}var Wt={U:this.U,V:this.V,S:this.s};return Wt},g.hypot=function(l,a){var r=void 0;return Math.abs(l)>Math.abs(a)?(r=a/l,r=Math.abs(l)*Math.sqrt(1+r*r)):a!=0?(r=l/a,r=Math.abs(a)*Math.sqrt(1+r*r)):r=0,r},A.exports=g},function(A,G,L){var g=function(){function r(e,f){for(var i=0;i<f.length;i++){var u=f[i];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(e,u.key,u)}}return function(e,f,i){return f&&r(e.prototype,f),i&&r(e,i),e}}();function l(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}var a=function(){function r(e,f){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,u=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;l(this,r),this.sequence1=e,this.sequence2=f,this.match_score=i,this.mismatch_penalty=u,this.gap_penalty=t,this.iMax=e.length+1,this.jMax=f.length+1,this.grid=new Array(this.iMax);for(var s=0;s<this.iMax;s++){this.grid[s]=new Array(this.jMax);for(var o=0;o<this.jMax;o++)this.grid[s][o]=0}this.tracebackGrid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.tracebackGrid[c]=new Array(this.jMax);for(var h=0;h<this.jMax;h++)this.tracebackGrid[c][h]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return g(r,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var f=1;f<this.jMax;f++)this.grid[0][f]=this.grid[0][f-1]+this.gap_penalty,this.tracebackGrid[0][f]=[!1,!1,!0];for(var i=1;i<this.iMax;i++)this.grid[i][0]=this.grid[i-1][0]+this.gap_penalty,this.tracebackGrid[i][0]=[!1,!0,!1];for(var u=1;u<this.iMax;u++)for(var t=1;t<this.jMax;t++){var s=void 0;this.sequence1[u-1]===this.sequence2[t-1]?s=this.grid[u-1][t-1]+this.match_score:s=this.grid[u-1][t-1]+this.mismatch_penalty;var o=this.grid[u-1][t]+this.gap_penalty,c=this.grid[u][t-1]+this.gap_penalty,h=[s,o,c],T=this.arrayAllMaxIndexes(h);this.grid[u][t]=h[T[0]],this.tracebackGrid[u][t]=[T.includes(0),T.includes(1),T.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var f=[];for(f.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});f[0];){var i=f[0],u=this.tracebackGrid[i.pos[0]][i.pos[1]];u[0]&&f.push({pos:[i.pos[0]-1,i.pos[1]-1],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),u[1]&&f.push({pos:[i.pos[0]-1,i.pos[1]],seq1:this.sequence1[i.pos[0]-1]+i.seq1,seq2:"-"+i.seq2}),u[2]&&f.push({pos:[i.pos[0],i.pos[1]-1],seq1:"-"+i.seq1,seq2:this.sequence2[i.pos[1]-1]+i.seq2}),i.pos[0]===0&&i.pos[1]===0&&this.alignments.push({sequence1:i.seq1,sequence2:i.seq2}),f.shift()}return this.alignments}},{key:"getAllIndexes",value:function(f,i){for(var u=[],t=-1;(t=f.indexOf(i,t+1))!==-1;)u.push(t);return u}},{key:"arrayAllMaxIndexes",value:function(f){return this.getAllIndexes(f,Math.max.apply(null,f))}}]),r}();A.exports=a},function(A,G,L){var g=function(){};g.FDLayout=L(18),g.FDLayoutConstants=L(4),g.FDLayoutEdge=L(19),g.FDLayoutNode=L(20),g.DimensionD=L(21),g.HashMap=L(22),g.HashSet=L(23),g.IGeometry=L(8),g.IMath=L(9),g.Integer=L(10),g.Point=L(12),g.PointD=L(5),g.RandomSeed=L(16),g.RectangleD=L(13),g.Transform=L(17),g.UniqueIDGeneretor=L(14),g.Quicksort=L(25),g.LinkedList=L(11),g.LGraphObject=L(2),g.LGraph=L(6),g.LEdge=L(1),g.LGraphManager=L(7),g.LNode=L(3),g.Layout=L(15),g.LayoutConstants=L(0),g.NeedlemanWunsch=L(27),g.Matrix=L(24),g.SVD=L(26),A.exports=g},function(A,G,L){function g(){this.listeners=[]}var l=g.prototype;l.addListener=function(a,r){this.listeners.push({event:a,callback:r})},l.removeListener=function(a,r){for(var e=this.listeners.length;e>=0;e--){var f=this.listeners[e];f.event===a&&f.callback===r&&this.listeners.splice(e,1)}},l.emit=function(a,r){for(var e=0;e<this.listeners.length;e++){var f=this.listeners[e];a===f.event&&f.callback(r)}},A.exports=g}])})}(ce)),ce.exports}var pr=fe.exports,Ie;function yr(){return Ie||(Ie=1,function(C,X){(function(G,L){C.exports=L(vr())})(pr,function(A){return(()=>{var G={45:(a,r,e)=>{var f={};f.layoutBase=e(551),f.CoSEConstants=e(806),f.CoSEEdge=e(767),f.CoSEGraph=e(880),f.CoSEGraphManager=e(578),f.CoSELayout=e(765),f.CoSENode=e(991),f.ConstraintHandler=e(902),a.exports=f},806:(a,r,e)=>{var f=e(551).FDLayoutConstants;function i(){}for(var u in f)i[u]=f[u];i.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,i.DEFAULT_RADIAL_SEPARATION=f.DEFAULT_EDGE_LENGTH,i.DEFAULT_COMPONENT_SEPERATION=60,i.TILE=!0,i.TILING_PADDING_VERTICAL=10,i.TILING_PADDING_HORIZONTAL=10,i.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,i.ENFORCE_CONSTRAINTS=!0,i.APPLY_LAYOUT=!0,i.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,i.TREE_REDUCTION_ON_INCREMENTAL=!0,i.PURE_INCREMENTAL=i.DEFAULT_INCREMENTAL,a.exports=i},767:(a,r,e)=>{var f=e(551).FDLayoutEdge;function i(t,s,o){f.call(this,t,s,o)}i.prototype=Object.create(f.prototype);for(var u in f)i[u]=f[u];a.exports=i},880:(a,r,e)=>{var f=e(551).LGraph;function i(t,s,o){f.call(this,t,s,o)}i.prototype=Object.create(f.prototype);for(var u in f)i[u]=f[u];a.exports=i},578:(a,r,e)=>{var f=e(551).LGraphManager;function i(t){f.call(this,t)}i.prototype=Object.create(f.prototype);for(var u in f)i[u]=f[u];a.exports=i},765:(a,r,e)=>{var f=e(551).FDLayout,i=e(578),u=e(880),t=e(991),s=e(767),o=e(806),c=e(902),h=e(551).FDLayoutConstants,T=e(551).LayoutConstants,v=e(551).Point,d=e(551).PointD,N=e(551).DimensionD,S=e(551).Layout,M=e(551).Integer,P=e(551).IGeometry,K=e(551).LGraph,Y=e(551).Transform,k=e(551).LinkedList;function D(){f.call(this),this.toBeTiled={},this.constraints={}}D.prototype=Object.create(f.prototype);for(var rt in f)D[rt]=f[rt];D.prototype.newGraphManager=function(){var n=new i(this);return this.graphManager=n,n},D.prototype.newGraph=function(n){return new u(null,this.graphManager,n)},D.prototype.newNode=function(n){return new t(this.graphManager,n)},D.prototype.newEdge=function(n){return new s(null,null,n)},D.prototype.initParameters=function(){f.prototype.initParameters.call(this,arguments),this.isSubLayout||(o.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=o.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=h.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=h.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=h.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},D.prototype.initSpringEmbedder=function(){f.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/h.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},D.prototype.layout=function(){var n=T.DEFAULT_CREATE_BENDS_AS_NEEDED;return n&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},D.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(o.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(I){return m.has(I)});this.graphManager.setAllNodesToApplyGravitation(p)}}else{var n=this.getFlatForest();if(n.length>0)this.positionNodesRadially(n);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var m=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(E){return m.has(E)});this.graphManager.setAllNodesToApplyGravitation(p),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(c.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),o.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},D.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%h.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var n=new Set(this.getAllNodes()),m=this.nodesWithGravity.filter(function(y){return n.has(y)});this.graphManager.setAllNodesToApplyGravitation(m),this.graphManager.updateBounds(),this.updateGrid(),o.PURE_INCREMENTAL?this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),o.PURE_INCREMENTAL?this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var p=!this.isTreeGrowing&&!this.isGrowthFinished,E=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(p,E),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},D.prototype.getPositionsData=function(){for(var n=this.graphManager.getAllNodes(),m={},p=0;p<n.length;p++){var E=n[p].rect,y=n[p].id;m[y]={id:y,x:E.getCenterX(),y:E.getCenterY(),w:E.width,h:E.height}}return m},D.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var n=!1;if(h.ANIMATE==="during")this.emit("layoutstarted");else{for(;!n;)n=this.tick();this.graphManager.updateBounds()}},D.prototype.moveNodes=function(){for(var n=this.getAllNodes(),m,p=0;p<n.length;p++)m=n[p],m.calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var p=0;p<n.length;p++)m=n[p],m.move()},D.prototype.initConstraintVariables=function(){var n=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var m=this.graphManager.getAllNodes(),p=0;p<m.length;p++){var E=m[p];this.idToNodeMap.set(E.id,E)}var y=function O(H){for(var B=H.getChild().getNodes(),_,lt=0,J=0;J<B.length;J++)_=B[J],_.getChild()==null?n.fixedNodeSet.has(_.id)&&(lt+=100):lt+=O(_);return lt};if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function(B){n.fixedNodeSet.add(B.nodeId)});for(var m=this.graphManager.getAllNodes(),E,p=0;p<m.length;p++)if(E=m[p],E.getChild()!=null){var I=y(E);I>0&&(E.fixedNodeWeight=I)}}if(this.constraints.relativePlacementConstraint){var w=new Map,R=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(O){n.fixedNodesOnHorizontal.add(O),n.fixedNodesOnVertical.add(O)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var W=this.constraints.alignmentConstraint.vertical,p=0;p<W.length;p++)this.dummyToNodeForVerticalAlignment.set("dummy"+p,[]),W[p].forEach(function(H){w.set(H,"dummy"+p),n.dummyToNodeForVerticalAlignment.get("dummy"+p).push(H),n.fixedNodeSet.has(H)&&n.fixedNodesOnHorizontal.add("dummy"+p)});if(this.constraints.alignmentConstraint.horizontal)for(var x=this.constraints.alignmentConstraint.horizontal,p=0;p<x.length;p++)this.dummyToNodeForHorizontalAlignment.set("dummy"+p,[]),x[p].forEach(function(H){R.set(H,"dummy"+p),n.dummyToNodeForHorizontalAlignment.get("dummy"+p).push(H),n.fixedNodeSet.has(H)&&n.fixedNodesOnVertical.add("dummy"+p)})}if(o.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(O){var H,B,_;for(_=O.length-1;_>=2*O.length/3;_--)H=Math.floor(Math.random()*(_+1)),B=O[_],O[_]=O[H],O[H]=B;return O},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var H=w.has(O.left)?w.get(O.left):O.left,B=w.has(O.right)?w.get(O.right):O.right;n.nodesInRelativeHorizontal.includes(H)||(n.nodesInRelativeHorizontal.push(H),n.nodeToRelativeConstraintMapHorizontal.set(H,[]),n.dummyToNodeForVerticalAlignment.has(H)?n.nodeToTempPositionMapHorizontal.set(H,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(H)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(H,n.idToNodeMap.get(H).getCenterX())),n.nodesInRelativeHorizontal.includes(B)||(n.nodesInRelativeHorizontal.push(B),n.nodeToRelativeConstraintMapHorizontal.set(B,[]),n.dummyToNodeForVerticalAlignment.has(B)?n.nodeToTempPositionMapHorizontal.set(B,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(B)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(B,n.idToNodeMap.get(B).getCenterX())),n.nodeToRelativeConstraintMapHorizontal.get(H).push({right:B,gap:O.gap}),n.nodeToRelativeConstraintMapHorizontal.get(B).push({left:H,gap:O.gap})}else{var _=R.has(O.top)?R.get(O.top):O.top,lt=R.has(O.bottom)?R.get(O.bottom):O.bottom;n.nodesInRelativeVertical.includes(_)||(n.nodesInRelativeVertical.push(_),n.nodeToRelativeConstraintMapVertical.set(_,[]),n.dummyToNodeForHorizontalAlignment.has(_)?n.nodeToTempPositionMapVertical.set(_,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(_)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(_,n.idToNodeMap.get(_).getCenterY())),n.nodesInRelativeVertical.includes(lt)||(n.nodesInRelativeVertical.push(lt),n.nodeToRelativeConstraintMapVertical.set(lt,[]),n.dummyToNodeForHorizontalAlignment.has(lt)?n.nodeToTempPositionMapVertical.set(lt,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(lt)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(lt,n.idToNodeMap.get(lt).getCenterY())),n.nodeToRelativeConstraintMapVertical.get(_).push({bottom:lt,gap:O.gap}),n.nodeToRelativeConstraintMapVertical.get(lt).push({top:_,gap:O.gap})}});else{var q=new Map,V=new Map;this.constraints.relativePlacementConstraint.forEach(function(O){if(O.left){var H=w.has(O.left)?w.get(O.left):O.left,B=w.has(O.right)?w.get(O.right):O.right;q.has(H)?q.get(H).push(B):q.set(H,[B]),q.has(B)?q.get(B).push(H):q.set(B,[H])}else{var _=R.has(O.top)?R.get(O.top):O.top,lt=R.has(O.bottom)?R.get(O.bottom):O.bottom;V.has(_)?V.get(_).push(lt):V.set(_,[lt]),V.has(lt)?V.get(lt).push(_):V.set(lt,[_])}});var U=function(H,B){var _=[],lt=[],J=new k,Rt=new Set,Lt=0;return H.forEach(function(vt,it){if(!Rt.has(it)){_[Lt]=[],lt[Lt]=!1;var ut=it;for(J.push(ut),Rt.add(ut),_[Lt].push(ut);J.length!=0;){ut=J.shift(),B.has(ut)&&(lt[Lt]=!0);var Tt=H.get(ut);Tt.forEach(function(At){Rt.has(At)||(J.push(At),Rt.add(At),_[Lt].push(At))})}Lt++}}),{components:_,isFixed:lt}},et=U(q,n.fixedNodesOnHorizontal);this.componentsOnHorizontal=et.components,this.fixedComponentsOnHorizontal=et.isFixed;var z=U(V,n.fixedNodesOnVertical);this.componentsOnVertical=z.components,this.fixedComponentsOnVertical=z.isFixed}}},D.prototype.updateDisplacements=function(){var n=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(z){var O=n.idToNodeMap.get(z.nodeId);O.displacementX=0,O.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var m=this.constraints.alignmentConstraint.vertical,p=0;p<m.length;p++){for(var E=0,y=0;y<m[p].length;y++){if(this.fixedNodeSet.has(m[p][y])){E=0;break}E+=this.idToNodeMap.get(m[p][y]).displacementX}for(var I=E/m[p].length,y=0;y<m[p].length;y++)this.idToNodeMap.get(m[p][y]).displacementX=I}if(this.constraints.alignmentConstraint.horizontal)for(var w=this.constraints.alignmentConstraint.horizontal,p=0;p<w.length;p++){for(var R=0,y=0;y<w[p].length;y++){if(this.fixedNodeSet.has(w[p][y])){R=0;break}R+=this.idToNodeMap.get(w[p][y]).displacementY}for(var W=R/w[p].length,y=0;y<w[p].length;y++)this.idToNodeMap.get(w[p][y]).displacementY=W}}if(this.constraints.relativePlacementConstraint)if(o.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(z){if(!n.fixedNodesOnHorizontal.has(z)){var O=0;n.dummyToNodeForVerticalAlignment.has(z)?O=n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(z)[0]).displacementX:O=n.idToNodeMap.get(z).displacementX,n.nodeToRelativeConstraintMapHorizontal.get(z).forEach(function(H){if(H.right){var B=n.nodeToTempPositionMapHorizontal.get(H.right)-n.nodeToTempPositionMapHorizontal.get(z)-O;B<H.gap&&(O-=H.gap-B)}else{var B=n.nodeToTempPositionMapHorizontal.get(z)-n.nodeToTempPositionMapHorizontal.get(H.left)+O;B<H.gap&&(O+=H.gap-B)}}),n.nodeToTempPositionMapHorizontal.set(z,n.nodeToTempPositionMapHorizontal.get(z)+O),n.dummyToNodeForVerticalAlignment.has(z)?n.dummyToNodeForVerticalAlignment.get(z).forEach(function(H){n.idToNodeMap.get(H).displacementX=O}):n.idToNodeMap.get(z).displacementX=O}}),this.nodesInRelativeVertical.forEach(function(z){if(!n.fixedNodesOnHorizontal.has(z)){var O=0;n.dummyToNodeForHorizontalAlignment.has(z)?O=n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(z)[0]).displacementY:O=n.idToNodeMap.get(z).displacementY,n.nodeToRelativeConstraintMapVertical.get(z).forEach(function(H){if(H.bottom){var B=n.nodeToTempPositionMapVertical.get(H.bottom)-n.nodeToTempPositionMapVertical.get(z)-O;B<H.gap&&(O-=H.gap-B)}else{var B=n.nodeToTempPositionMapVertical.get(z)-n.nodeToTempPositionMapVertical.get(H.top)+O;B<H.gap&&(O+=H.gap-B)}}),n.nodeToTempPositionMapVertical.set(z,n.nodeToTempPositionMapVertical.get(z)+O),n.dummyToNodeForHorizontalAlignment.has(z)?n.dummyToNodeForHorizontalAlignment.get(z).forEach(function(H){n.idToNodeMap.get(H).displacementY=O}):n.idToNodeMap.get(z).displacementY=O}});else{for(var p=0;p<this.componentsOnHorizontal.length;p++){var x=this.componentsOnHorizontal[p];if(this.fixedComponentsOnHorizontal[p])for(var y=0;y<x.length;y++)this.dummyToNodeForVerticalAlignment.has(x[y])?this.dummyToNodeForVerticalAlignment.get(x[y]).forEach(function(H){n.idToNodeMap.get(H).displacementX=0}):this.idToNodeMap.get(x[y]).displacementX=0;else{for(var q=0,V=0,y=0;y<x.length;y++)if(this.dummyToNodeForVerticalAlignment.has(x[y])){var U=this.dummyToNodeForVerticalAlignment.get(x[y]);q+=U.length*this.idToNodeMap.get(U[0]).displacementX,V+=U.length}else q+=this.idToNodeMap.get(x[y]).displacementX,V++;for(var et=q/V,y=0;y<x.length;y++)this.dummyToNodeForVerticalAlignment.has(x[y])?this.dummyToNodeForVerticalAlignment.get(x[y]).forEach(function(H){n.idToNodeMap.get(H).displacementX=et}):this.idToNodeMap.get(x[y]).displacementX=et}}for(var p=0;p<this.componentsOnVertical.length;p++){var x=this.componentsOnVertical[p];if(this.fixedComponentsOnVertical[p])for(var y=0;y<x.length;y++)this.dummyToNodeForHorizontalAlignment.has(x[y])?this.dummyToNodeForHorizontalAlignment.get(x[y]).forEach(function(B){n.idToNodeMap.get(B).displacementY=0}):this.idToNodeMap.get(x[y]).displacementY=0;else{for(var q=0,V=0,y=0;y<x.length;y++)if(this.dummyToNodeForHorizontalAlignment.has(x[y])){var U=this.dummyToNodeForHorizontalAlignment.get(x[y]);q+=U.length*this.idToNodeMap.get(U[0]).displacementY,V+=U.length}else q+=this.idToNodeMap.get(x[y]).displacementY,V++;for(var et=q/V,y=0;y<x.length;y++)this.dummyToNodeForHorizontalAlignment.has(x[y])?this.dummyToNodeForHorizontalAlignment.get(x[y]).forEach(function(J){n.idToNodeMap.get(J).displacementY=et}):this.idToNodeMap.get(x[y]).displacementY=et}}}},D.prototype.calculateNodesToApplyGravitationTo=function(){var n=[],m,p=this.graphManager.getGraphs(),E=p.length,y;for(y=0;y<E;y++)m=p[y],m.updateConnected(),m.isConnected||(n=n.concat(m.getNodes()));return n},D.prototype.createBendpoints=function(){var n=[];n=n.concat(this.graphManager.getAllEdges());var m=new Set,p;for(p=0;p<n.length;p++){var E=n[p];if(!m.has(E)){var y=E.getSource(),I=E.getTarget();if(y==I)E.getBendpoints().push(new d),E.getBendpoints().push(new d),this.createDummyNodesForBendpoints(E),m.add(E);else{var w=[];if(w=w.concat(y.getEdgeListToNode(I)),w=w.concat(I.getEdgeListToNode(y)),!m.has(w[0])){if(w.length>1){var R;for(R=0;R<w.length;R++){var W=w[R];W.getBendpoints().push(new d),this.createDummyNodesForBendpoints(W)}}w.forEach(function(x){m.add(x)})}}}if(m.size==n.length)break}},D.prototype.positionNodesRadially=function(n){for(var m=new v(0,0),p=Math.ceil(Math.sqrt(n.length)),E=0,y=0,I=0,w=new d(0,0),R=0;R<n.length;R++){R%p==0&&(I=0,y=E,R!=0&&(y+=o.DEFAULT_COMPONENT_SEPERATION),E=0);var W=n[R],x=S.findCenterOfTree(W);m.x=I,m.y=y,w=D.radialLayout(W,x,m),w.y>E&&(E=Math.floor(w.y)),I=Math.floor(w.x+o.DEFAULT_COMPONENT_SEPERATION)}this.transform(new d(T.WORLD_CENTER_X-w.x/2,T.WORLD_CENTER_Y-w.y/2))},D.radialLayout=function(n,m,p){var E=Math.max(this.maxDiagonalInTree(n),o.DEFAULT_RADIAL_SEPARATION);D.branchRadialLayout(m,null,0,359,0,E);var y=K.calculateBounds(n),I=new Y;I.setDeviceOrgX(y.getMinX()),I.setDeviceOrgY(y.getMinY()),I.setWorldOrgX(p.x),I.setWorldOrgY(p.y);for(var w=0;w<n.length;w++){var R=n[w];R.transform(I)}var W=new d(y.getMaxX(),y.getMaxY());return I.inverseTransformPoint(W)},D.branchRadialLayout=function(n,m,p,E,y,I){var w=(E-p+1)/2;w<0&&(w+=180);var R=(w+p)%360,W=R*P.TWO_PI/360,x=y*Math.cos(W),q=y*Math.sin(W);n.setCenter(x,q);var V=[];V=V.concat(n.getEdges());var U=V.length;m!=null&&U--;for(var et=0,z=V.length,O,H=n.getEdgesBetween(m);H.length>1;){var B=H[0];H.splice(0,1);var _=V.indexOf(B);_>=0&&V.splice(_,1),z--,U--}m!=null?O=(V.indexOf(H[0])+1)%z:O=0;for(var lt=Math.abs(E-p)/U,J=O;et!=U;J=++J%z){var Rt=V[J].getOtherEnd(n);if(Rt!=m){var Lt=(p+et*lt)%360,vt=(Lt+lt)%360;D.branchRadialLayout(Rt,n,Lt,vt,y+I,I),et++}}},D.maxDiagonalInTree=function(n){for(var m=M.MIN_VALUE,p=0;p<n.length;p++){var E=n[p],y=E.getDiagonal();y>m&&(m=y)}return m},D.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},D.prototype.groupZeroDegreeMembers=function(){var n=this,m={};this.memberGroups={},this.idToDummyNode={};for(var p=[],E=this.graphManager.getAllNodes(),y=0;y<E.length;y++){var I=E[y],w=I.getParent();this.getNodeDegreeWithChildren(I)===0&&(w.id==null||!this.getToBeTiled(w))&&p.push(I)}for(var y=0;y<p.length;y++){var I=p[y],R=I.getParent().id;typeof m[R]>"u"&&(m[R]=[]),m[R]=m[R].concat(I)}Object.keys(m).forEach(function(W){if(m[W].length>1){var x="DummyCompound_"+W;n.memberGroups[x]=m[W];var q=m[W][0].getParent(),V=new t(n.graphManager);V.id=x,V.paddingLeft=q.paddingLeft||0,V.paddingRight=q.paddingRight||0,V.paddingBottom=q.paddingBottom||0,V.paddingTop=q.paddingTop||0,n.idToDummyNode[x]=V;var U=n.getGraphManager().add(n.newGraph(),V),et=q.getChild();et.add(V);for(var z=0;z<m[W].length;z++){var O=m[W][z];et.remove(O),U.add(O)}}})},D.prototype.clearCompounds=function(){var n={},m={};this.performDFSOnCompounds();for(var p=0;p<this.compoundOrder.length;p++)m[this.compoundOrder[p].id]=this.compoundOrder[p],n[this.compoundOrder[p].id]=[].concat(this.compoundOrder[p].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[p].getChild()),this.compoundOrder[p].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(n,m)},D.prototype.clearZeroDegreeMembers=function(){var n=this,m=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(p){var E=n.idToDummyNode[p];if(m[p]=n.tileNodes(n.memberGroups[p],E.paddingLeft+E.paddingRight),E.rect.width=m[p].width,E.rect.height=m[p].height,E.setCenter(m[p].centerX,m[p].centerY),E.labelMarginLeft=0,E.labelMarginTop=0,o.NODE_DIMENSIONS_INCLUDE_LABELS){var y=E.rect.width,I=E.rect.height;E.labelWidth&&(E.labelPosHorizontal=="left"?(E.rect.x-=E.labelWidth,E.setWidth(y+E.labelWidth),E.labelMarginLeft=E.labelWidth):E.labelPosHorizontal=="center"&&E.labelWidth>y?(E.rect.x-=(E.labelWidth-y)/2,E.setWidth(E.labelWidth),E.labelMarginLeft=(E.labelWidth-y)/2):E.labelPosHorizontal=="right"&&E.setWidth(y+E.labelWidth)),E.labelHeight&&(E.labelPosVertical=="top"?(E.rect.y-=E.labelHeight,E.setHeight(I+E.labelHeight),E.labelMarginTop=E.labelHeight):E.labelPosVertical=="center"&&E.labelHeight>I?(E.rect.y-=(E.labelHeight-I)/2,E.setHeight(E.labelHeight),E.labelMarginTop=(E.labelHeight-I)/2):E.labelPosVertical=="bottom"&&E.setHeight(I+E.labelHeight))}})},D.prototype.repopulateCompounds=function(){for(var n=this.compoundOrder.length-1;n>=0;n--){var m=this.compoundOrder[n],p=m.id,E=m.paddingLeft,y=m.paddingTop,I=m.labelMarginLeft,w=m.labelMarginTop;this.adjustLocations(this.tiledMemberPack[p],m.rect.x,m.rect.y,E,y,I,w)}},D.prototype.repopulateZeroDegreeMembers=function(){var n=this,m=this.tiledZeroDegreePack;Object.keys(m).forEach(function(p){var E=n.idToDummyNode[p],y=E.paddingLeft,I=E.paddingTop,w=E.labelMarginLeft,R=E.labelMarginTop;n.adjustLocations(m[p],E.rect.x,E.rect.y,y,I,w,R)})},D.prototype.getToBeTiled=function(n){var m=n.id;if(this.toBeTiled[m]!=null)return this.toBeTiled[m];var p=n.getChild();if(p==null)return this.toBeTiled[m]=!1,!1;for(var E=p.getNodes(),y=0;y<E.length;y++){var I=E[y];if(this.getNodeDegree(I)>0)return this.toBeTiled[m]=!1,!1;if(I.getChild()==null){this.toBeTiled[I.id]=!1;continue}if(!this.getToBeTiled(I))return this.toBeTiled[m]=!1,!1}return this.toBeTiled[m]=!0,!0},D.prototype.getNodeDegree=function(n){n.id;for(var m=n.getEdges(),p=0,E=0;E<m.length;E++){var y=m[E];y.getSource().id!==y.getTarget().id&&(p=p+1)}return p},D.prototype.getNodeDegreeWithChildren=function(n){var m=this.getNodeDegree(n);if(n.getChild()==null)return m;for(var p=n.getChild().getNodes(),E=0;E<p.length;E++){var y=p[E];m+=this.getNodeDegreeWithChildren(y)}return m},D.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},D.prototype.fillCompexOrderByDFS=function(n){for(var m=0;m<n.length;m++){var p=n[m];p.getChild()!=null&&this.fillCompexOrderByDFS(p.getChild().getNodes()),this.getToBeTiled(p)&&this.compoundOrder.push(p)}},D.prototype.adjustLocations=function(n,m,p,E,y,I,w){m+=E+I,p+=y+w;for(var R=m,W=0;W<n.rows.length;W++){var x=n.rows[W];m=R;for(var q=0,V=0;V<x.length;V++){var U=x[V];U.rect.x=m,U.rect.y=p,m+=U.rect.width+n.horizontalPadding,U.rect.height>q&&(q=U.rect.height)}p+=q+n.verticalPadding}},D.prototype.tileCompoundMembers=function(n,m){var p=this;this.tiledMemberPack=[],Object.keys(n).forEach(function(E){var y=m[E];if(p.tiledMemberPack[E]=p.tileNodes(n[E],y.paddingLeft+y.paddingRight),y.rect.width=p.tiledMemberPack[E].width,y.rect.height=p.tiledMemberPack[E].height,y.setCenter(p.tiledMemberPack[E].centerX,p.tiledMemberPack[E].centerY),y.labelMarginLeft=0,y.labelMarginTop=0,o.NODE_DIMENSIONS_INCLUDE_LABELS){var I=y.rect.width,w=y.rect.height;y.labelWidth&&(y.labelPosHorizontal=="left"?(y.rect.x-=y.labelWidth,y.setWidth(I+y.labelWidth),y.labelMarginLeft=y.labelWidth):y.labelPosHorizontal=="center"&&y.labelWidth>I?(y.rect.x-=(y.labelWidth-I)/2,y.setWidth(y.labelWidth),y.labelMarginLeft=(y.labelWidth-I)/2):y.labelPosHorizontal=="right"&&y.setWidth(I+y.labelWidth)),y.labelHeight&&(y.labelPosVertical=="top"?(y.rect.y-=y.labelHeight,y.setHeight(w+y.labelHeight),y.labelMarginTop=y.labelHeight):y.labelPosVertical=="center"&&y.labelHeight>w?(y.rect.y-=(y.labelHeight-w)/2,y.setHeight(y.labelHeight),y.labelMarginTop=(y.labelHeight-w)/2):y.labelPosVertical=="bottom"&&y.setHeight(w+y.labelHeight))}})},D.prototype.tileNodes=function(n,m){var p=this.tileNodesByFavoringDim(n,m,!0),E=this.tileNodesByFavoringDim(n,m,!1),y=this.getOrgRatio(p),I=this.getOrgRatio(E),w;return I<y?w=E:w=p,w},D.prototype.getOrgRatio=function(n){var m=n.width,p=n.height,E=m/p;return E<1&&(E=1/E),E},D.prototype.calcIdealRowWidth=function(n,m){var p=o.TILING_PADDING_VERTICAL,E=o.TILING_PADDING_HORIZONTAL,y=n.length,I=0,w=0,R=0;n.forEach(function(z){I+=z.getWidth(),w+=z.getHeight(),z.getWidth()>R&&(R=z.getWidth())});var W=I/y,x=w/y,q=Math.pow(p-E,2)+4*(W+E)*(x+p)*y,V=(E-p+Math.sqrt(q))/(2*(W+E)),U;m?(U=Math.ceil(V),U==V&&U++):U=Math.floor(V);var et=U*(W+E)-E;return R>et&&(et=R),et+=E*2,et},D.prototype.tileNodesByFavoringDim=function(n,m,p){var E=o.TILING_PADDING_VERTICAL,y=o.TILING_PADDING_HORIZONTAL,I=o.TILING_COMPARE_BY,w={rows:[],rowWidth:[],rowHeight:[],width:0,height:m,verticalPadding:E,horizontalPadding:y,centerX:0,centerY:0};I&&(w.idealRowWidth=this.calcIdealRowWidth(n,p));var R=function(O){return O.rect.width*O.rect.height},W=function(O,H){return R(H)-R(O)};n.sort(function(z,O){var H=W;return w.idealRowWidth?(H=I,H(z.id,O.id)):H(z,O)});for(var x=0,q=0,V=0;V<n.length;V++){var U=n[V];x+=U.getCenterX(),q+=U.getCenterY()}w.centerX=x/n.length,w.centerY=q/n.length;for(var V=0;V<n.length;V++){var U=n[V];if(w.rows.length==0)this.insertNodeToRow(w,U,0,m);else if(this.canAddHorizontal(w,U.rect.width,U.rect.height)){var et=w.rows.length-1;w.idealRowWidth||(et=this.getShortestRowIndex(w)),this.insertNodeToRow(w,U,et,m)}else this.insertNodeToRow(w,U,w.rows.length,m);this.shiftToLastRow(w)}return w},D.prototype.insertNodeToRow=function(n,m,p,E){var y=E;if(p==n.rows.length){var I=[];n.rows.push(I),n.rowWidth.push(y),n.rowHeight.push(0)}var w=n.rowWidth[p]+m.rect.width;n.rows[p].length>0&&(w+=n.horizontalPadding),n.rowWidth[p]=w,n.width<w&&(n.width=w);var R=m.rect.height;p>0&&(R+=n.verticalPadding);var W=0;R>n.rowHeight[p]&&(W=n.rowHeight[p],n.rowHeight[p]=R,W=n.rowHeight[p]-W),n.height+=W,n.rows[p].push(m)},D.prototype.getShortestRowIndex=function(n){for(var m=-1,p=Number.MAX_VALUE,E=0;E<n.rows.length;E++)n.rowWidth[E]<p&&(m=E,p=n.rowWidth[E]);return m},D.prototype.getLongestRowIndex=function(n){for(var m=-1,p=Number.MIN_VALUE,E=0;E<n.rows.length;E++)n.rowWidth[E]>p&&(m=E,p=n.rowWidth[E]);return m},D.prototype.canAddHorizontal=function(n,m,p){if(n.idealRowWidth){var E=n.rows.length-1,y=n.rowWidth[E];return y+m+n.horizontalPadding<=n.idealRowWidth}var I=this.getShortestRowIndex(n);if(I<0)return!0;var w=n.rowWidth[I];if(w+n.horizontalPadding+m<=n.width)return!0;var R=0;n.rowHeight[I]<p&&I>0&&(R=p+n.verticalPadding-n.rowHeight[I]);var W;n.width-w>=m+n.horizontalPadding?W=(n.height+R)/(w+m+n.horizontalPadding):W=(n.height+R)/n.width,R=p+n.verticalPadding;var x;return n.width<m?x=(n.height+R)/m:x=(n.height+R)/n.width,x<1&&(x=1/x),W<1&&(W=1/W),W<x},D.prototype.shiftToLastRow=function(n){var m=this.getLongestRowIndex(n),p=n.rowWidth.length-1,E=n.rows[m],y=E[E.length-1],I=y.width+n.horizontalPadding;if(n.width-n.rowWidth[p]>I&&m!=p){E.splice(-1,1),n.rows[p].push(y),n.rowWidth[m]=n.rowWidth[m]-I,n.rowWidth[p]=n.rowWidth[p]+I,n.width=n.rowWidth[instance.getLongestRowIndex(n)];for(var w=Number.MIN_VALUE,R=0;R<E.length;R++)E[R].height>w&&(w=E[R].height);m>0&&(w+=n.verticalPadding);var W=n.rowHeight[m]+n.rowHeight[p];n.rowHeight[m]=w,n.rowHeight[p]<y.height+n.verticalPadding&&(n.rowHeight[p]=y.height+n.verticalPadding);var x=n.rowHeight[m]+n.rowHeight[p];n.height+=x-W,this.shiftToLastRow(n)}},D.prototype.tilingPreLayout=function(){o.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},D.prototype.tilingPostLayout=function(){o.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},D.prototype.reduceTrees=function(){for(var n=[],m=!0,p;m;){var E=this.graphManager.getAllNodes(),y=[];m=!1;for(var I=0;I<E.length;I++)if(p=E[I],p.getEdges().length==1&&!p.getEdges()[0].isInterGraph&&p.getChild()==null){if(o.PURE_INCREMENTAL){var w=p.getEdges()[0].getOtherEnd(p),R=new N(p.getCenterX()-w.getCenterX(),p.getCenterY()-w.getCenterY());y.push([p,p.getEdges()[0],p.getOwner(),R])}else y.push([p,p.getEdges()[0],p.getOwner()]);m=!0}if(m==!0){for(var W=[],x=0;x<y.length;x++)y[x][0].getEdges().length==1&&(W.push(y[x]),y[x][0].getOwner().remove(y[x][0]));n.push(W),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=n},D.prototype.growTree=function(n){for(var m=n.length,p=n[m-1],E,y=0;y<p.length;y++)E=p[y],this.findPlaceforPrunedNode(E),E[2].add(E[0]),E[2].add(E[1],E[1].source,E[1].target);n.splice(n.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},D.prototype.findPlaceforPrunedNode=function(n){var m,p,E=n[0];if(E==n[1].source?p=n[1].target:p=n[1].source,o.PURE_INCREMENTAL)E.setCenter(p.getCenterX()+n[3].getWidth(),p.getCenterY()+n[3].getHeight());else{var y=p.startX,I=p.finishX,w=p.startY,R=p.finishY,W=0,x=0,q=0,V=0,U=[W,q,x,V];if(w>0)for(var et=y;et<=I;et++)U[0]+=this.grid[et][w-1].length+this.grid[et][w].length-1;if(I<this.grid.length-1)for(var et=w;et<=R;et++)U[1]+=this.grid[I+1][et].length+this.grid[I][et].length-1;if(R<this.grid[0].length-1)for(var et=y;et<=I;et++)U[2]+=this.grid[et][R+1].length+this.grid[et][R].length-1;if(y>0)for(var et=w;et<=R;et++)U[3]+=this.grid[y-1][et].length+this.grid[y][et].length-1;for(var z=M.MAX_VALUE,O,H,B=0;B<U.length;B++)U[B]<z?(z=U[B],O=1,H=B):U[B]==z&&O++;if(O==3&&z==0)U[0]==0&&U[1]==0&&U[2]==0?m=1:U[0]==0&&U[1]==0&&U[3]==0?m=0:U[0]==0&&U[2]==0&&U[3]==0?m=3:U[1]==0&&U[2]==0&&U[3]==0&&(m=2);else if(O==2&&z==0){var _=Math.floor(Math.random()*2);U[0]==0&&U[1]==0?_==0?m=0:m=1:U[0]==0&&U[2]==0?_==0?m=0:m=2:U[0]==0&&U[3]==0?_==0?m=0:m=3:U[1]==0&&U[2]==0?_==0?m=1:m=2:U[1]==0&&U[3]==0?_==0?m=1:m=3:_==0?m=2:m=3}else if(O==4&&z==0){var _=Math.floor(Math.random()*4);m=_}else m=H;m==0?E.setCenter(p.getCenterX(),p.getCenterY()-p.getHeight()/2-h.DEFAULT_EDGE_LENGTH-E.getHeight()/2):m==1?E.setCenter(p.getCenterX()+p.getWidth()/2+h.DEFAULT_EDGE_LENGTH+E.getWidth()/2,p.getCenterY()):m==2?E.setCenter(p.getCenterX(),p.getCenterY()+p.getHeight()/2+h.DEFAULT_EDGE_LENGTH+E.getHeight()/2):E.setCenter(p.getCenterX()-p.getWidth()/2-h.DEFAULT_EDGE_LENGTH-E.getWidth()/2,p.getCenterY())}},a.exports=D},991:(a,r,e)=>{var f=e(551).FDLayoutNode,i=e(551).IMath;function u(s,o,c,h){f.call(this,s,o,c,h)}u.prototype=Object.create(f.prototype);for(var t in f)u[t]=f[t];u.prototype.calculateDisplacement=function(){var s=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=s.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=s.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=s.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=s.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>s.coolingFactor*s.maxNodeDisplacement&&(this.displacementX=s.coolingFactor*s.maxNodeDisplacement*i.sign(this.displacementX)),Math.abs(this.displacementY)>s.coolingFactor*s.maxNodeDisplacement&&(this.displacementY=s.coolingFactor*s.maxNodeDisplacement*i.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},u.prototype.propogateDisplacementToChildren=function(s,o){for(var c=this.getChild().getNodes(),h,T=0;T<c.length;T++)h=c[T],h.getChild()==null?(h.displacementX+=s,h.displacementY+=o):h.propogateDisplacementToChildren(s,o)},u.prototype.move=function(){var s=this.graphManager.getLayout();(this.child==null||this.child.getNodes().length==0)&&(this.moveBy(this.displacementX,this.displacementY),s.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},u.prototype.setPred1=function(s){this.pred1=s},u.prototype.getPred1=function(){return pred1},u.prototype.getPred2=function(){return pred2},u.prototype.setNext=function(s){this.next=s},u.prototype.getNext=function(){return next},u.prototype.setProcessed=function(s){this.processed=s},u.prototype.isProcessed=function(){return processed},a.exports=u},902:(a,r,e)=>{function f(c){if(Array.isArray(c)){for(var h=0,T=Array(c.length);h<c.length;h++)T[h]=c[h];return T}else return Array.from(c)}var i=e(806),u=e(551).LinkedList,t=e(551).Matrix,s=e(551).SVD;function o(){}o.handleConstraints=function(c){var h={};h.fixedNodeConstraint=c.constraints.fixedNodeConstraint,h.alignmentConstraint=c.constraints.alignmentConstraint,h.relativePlacementConstraint=c.constraints.relativePlacementConstraint;for(var T=new Map,v=new Map,d=[],N=[],S=c.getAllNodes(),M=0,P=0;P<S.length;P++){var K=S[P];K.getChild()==null&&(v.set(K.id,M++),d.push(K.getCenterX()),N.push(K.getCenterY()),T.set(K.id,K))}h.relativePlacementConstraint&&h.relativePlacementConstraint.forEach(function(F){!F.gap&&F.gap!=0&&(F.left?F.gap=i.DEFAULT_EDGE_LENGTH+T.get(F.left).getWidth()/2+T.get(F.right).getWidth()/2:F.gap=i.DEFAULT_EDGE_LENGTH+T.get(F.top).getHeight()/2+T.get(F.bottom).getHeight()/2)});var Y=function(b,$){return{x:b.x-$.x,y:b.y-$.y}},k=function(b){var $=0,Q=0;return b.forEach(function(Z){$+=d[v.get(Z)],Q+=N[v.get(Z)]}),{x:$/b.size,y:Q/b.size}},D=function(b,$,Q,Z,at){function gt(ft,st){var Ct=new Set(ft),ct=!0,ht=!1,Wt=void 0;try{for(var Nt=st[Symbol.iterator](),Mt;!(ct=(Mt=Nt.next()).done);ct=!0){var Zt=Mt.value;Ct.add(Zt)}}catch(Gt){ht=!0,Wt=Gt}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(ht)throw Wt}}return Ct}var ot=new Map;b.forEach(function(ft,st){ot.set(st,0)}),b.forEach(function(ft,st){ft.forEach(function(Ct){ot.set(Ct.id,ot.get(Ct.id)+1)})});var tt=new Map,j=new Map,dt=new u;ot.forEach(function(ft,st){ft==0?(dt.push(st),Q||($=="horizontal"?tt.set(st,v.has(st)?d[v.get(st)]:Z.get(st)):tt.set(st,v.has(st)?N[v.get(st)]:Z.get(st)))):tt.set(st,Number.NEGATIVE_INFINITY),Q&&j.set(st,new Set([st]))}),Q&&at.forEach(function(ft){var st=[];if(ft.forEach(function(ht){Q.has(ht)&&st.push(ht)}),st.length>0){var Ct=0;st.forEach(function(ht){$=="horizontal"?(tt.set(ht,v.has(ht)?d[v.get(ht)]:Z.get(ht)),Ct+=tt.get(ht)):(tt.set(ht,v.has(ht)?N[v.get(ht)]:Z.get(ht)),Ct+=tt.get(ht))}),Ct=Ct/st.length,ft.forEach(function(ht){Q.has(ht)||tt.set(ht,Ct)})}else{var ct=0;ft.forEach(function(ht){$=="horizontal"?ct+=v.has(ht)?d[v.get(ht)]:Z.get(ht):ct+=v.has(ht)?N[v.get(ht)]:Z.get(ht)}),ct=ct/ft.length,ft.forEach(function(ht){tt.set(ht,ct)})}});for(var wt=function(){var st=dt.shift(),Ct=b.get(st);Ct.forEach(function(ct){if(tt.get(ct.id)<tt.get(st)+ct.gap)if(Q&&Q.has(ct.id)){var ht=void 0;if($=="horizontal"?ht=v.has(ct.id)?d[v.get(ct.id)]:Z.get(ct.id):ht=v.has(ct.id)?N[v.get(ct.id)]:Z.get(ct.id),tt.set(ct.id,ht),ht<tt.get(st)+ct.gap){var Wt=tt.get(st)+ct.gap-ht;j.get(st).forEach(function(Nt){tt.set(Nt,tt.get(Nt)-Wt)})}}else tt.set(ct.id,tt.get(st)+ct.gap);ot.set(ct.id,ot.get(ct.id)-1),ot.get(ct.id)==0&&dt.push(ct.id),Q&&j.set(ct.id,gt(j.get(st),j.get(ct.id)))})};dt.length!=0;)wt();if(Q){var yt=new Set;b.forEach(function(ft,st){ft.length==0&&yt.add(st)});var It=[];j.forEach(function(ft,st){if(yt.has(st)){var Ct=!1,ct=!0,ht=!1,Wt=void 0;try{for(var Nt=ft[Symbol.iterator](),Mt;!(ct=(Mt=Nt.next()).done);ct=!0){var Zt=Mt.value;Q.has(Zt)&&(Ct=!0)}}catch(Ft){ht=!0,Wt=Ft}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(ht)throw Wt}}if(!Ct){var Gt=!1,$t=void 0;It.forEach(function(Ft,qt){Ft.has([].concat(f(ft))[0])&&(Gt=!0,$t=qt)}),Gt?ft.forEach(function(Ft){It[$t].add(Ft)}):It.push(new Set(ft))}}}),It.forEach(function(ft,st){var Ct=Number.POSITIVE_INFINITY,ct=Number.POSITIVE_INFINITY,ht=Number.NEGATIVE_INFINITY,Wt=Number.NEGATIVE_INFINITY,Nt=!0,Mt=!1,Zt=void 0;try{for(var Gt=ft[Symbol.iterator](),$t;!(Nt=($t=Gt.next()).done);Nt=!0){var Ft=$t.value,qt=void 0;$=="horizontal"?qt=v.has(Ft)?d[v.get(Ft)]:Z.get(Ft):qt=v.has(Ft)?N[v.get(Ft)]:Z.get(Ft);var _t=tt.get(Ft);qt<Ct&&(Ct=qt),qt>ht&&(ht=qt),_t<ct&&(ct=_t),_t>Wt&&(Wt=_t)}}catch(ie){Mt=!0,Zt=ie}finally{try{!Nt&&Gt.return&&Gt.return()}finally{if(Mt)throw Zt}}var ue=(Ct+ht)/2-(ct+Wt)/2,Kt=!0,te=!1,ee=void 0;try{for(var jt=ft[Symbol.iterator](),se;!(Kt=(se=jt.next()).done);Kt=!0){var re=se.value;tt.set(re,tt.get(re)+ue)}}catch(ie){te=!0,ee=ie}finally{try{!Kt&&jt.return&&jt.return()}finally{if(te)throw ee}}})}return tt},rt=function(b){var $=0,Q=0,Z=0,at=0;if(b.forEach(function(j){j.left?d[v.get(j.left)]-d[v.get(j.right)]>=0?$++:Q++:N[v.get(j.top)]-N[v.get(j.bottom)]>=0?Z++:at++}),$>Q&&Z>at)for(var gt=0;gt<v.size;gt++)d[gt]=-1*d[gt],N[gt]=-1*N[gt];else if($>Q)for(var ot=0;ot<v.size;ot++)d[ot]=-1*d[ot];else if(Z>at)for(var tt=0;tt<v.size;tt++)N[tt]=-1*N[tt]},n=function(b){var $=[],Q=new u,Z=new Set,at=0;return b.forEach(function(gt,ot){if(!Z.has(ot)){$[at]=[];var tt=ot;for(Q.push(tt),Z.add(tt),$[at].push(tt);Q.length!=0;){tt=Q.shift();var j=b.get(tt);j.forEach(function(dt){Z.has(dt.id)||(Q.push(dt.id),Z.add(dt.id),$[at].push(dt.id))})}at++}}),$},m=function(b){var $=new Map;return b.forEach(function(Q,Z){$.set(Z,[])}),b.forEach(function(Q,Z){Q.forEach(function(at){$.get(Z).push(at),$.get(at.id).push({id:Z,gap:at.gap,direction:at.direction})})}),$},p=function(b){var $=new Map;return b.forEach(function(Q,Z){$.set(Z,[])}),b.forEach(function(Q,Z){Q.forEach(function(at){$.get(at.id).push({id:Z,gap:at.gap,direction:at.direction})})}),$},E=[],y=[],I=!1,w=!1,R=new Set,W=new Map,x=new Map,q=[];if(h.fixedNodeConstraint&&h.fixedNodeConstraint.forEach(function(F){R.add(F.nodeId)}),h.relativePlacementConstraint&&(h.relativePlacementConstraint.forEach(function(F){F.left?(W.has(F.left)?W.get(F.left).push({id:F.right,gap:F.gap,direction:"horizontal"}):W.set(F.left,[{id:F.right,gap:F.gap,direction:"horizontal"}]),W.has(F.right)||W.set(F.right,[])):(W.has(F.top)?W.get(F.top).push({id:F.bottom,gap:F.gap,direction:"vertical"}):W.set(F.top,[{id:F.bottom,gap:F.gap,direction:"vertical"}]),W.has(F.bottom)||W.set(F.bottom,[]))}),x=m(W),q=n(x)),i.TRANSFORM_ON_CONSTRAINT_HANDLING){if(h.fixedNodeConstraint&&h.fixedNodeConstraint.length>1)h.fixedNodeConstraint.forEach(function(F,b){E[b]=[F.position.x,F.position.y],y[b]=[d[v.get(F.nodeId)],N[v.get(F.nodeId)]]}),I=!0;else if(h.alignmentConstraint)(function(){var F=0;if(h.alignmentConstraint.vertical){for(var b=h.alignmentConstraint.vertical,$=function(tt){var j=new Set;b[tt].forEach(function(yt){j.add(yt)});var dt=new Set([].concat(f(j)).filter(function(yt){return R.has(yt)})),wt=void 0;dt.size>0?wt=d[v.get(dt.values().next().value)]:wt=k(j).x,b[tt].forEach(function(yt){E[F]=[wt,N[v.get(yt)]],y[F]=[d[v.get(yt)],N[v.get(yt)]],F++})},Q=0;Q<b.length;Q++)$(Q);I=!0}if(h.alignmentConstraint.horizontal){for(var Z=h.alignmentConstraint.horizontal,at=function(tt){var j=new Set;Z[tt].forEach(function(yt){j.add(yt)});var dt=new Set([].concat(f(j)).filter(function(yt){return R.has(yt)})),wt=void 0;dt.size>0?wt=d[v.get(dt.values().next().value)]:wt=k(j).y,Z[tt].forEach(function(yt){E[F]=[d[v.get(yt)],wt],y[F]=[d[v.get(yt)],N[v.get(yt)]],F++})},gt=0;gt<Z.length;gt++)at(gt);I=!0}h.relativePlacementConstraint&&(w=!0)})();else if(h.relativePlacementConstraint){for(var V=0,U=0,et=0;et<q.length;et++)q[et].length>V&&(V=q[et].length,U=et);if(V<x.size/2)rt(h.relativePlacementConstraint),I=!1,w=!1;else{var z=new Map,O=new Map,H=[];q[U].forEach(function(F){W.get(F).forEach(function(b){b.direction=="horizontal"?(z.has(F)?z.get(F).push(b):z.set(F,[b]),z.has(b.id)||z.set(b.id,[]),H.push({left:F,right:b.id})):(O.has(F)?O.get(F).push(b):O.set(F,[b]),O.has(b.id)||O.set(b.id,[]),H.push({top:F,bottom:b.id}))})}),rt(H),w=!1;var B=D(z,"horizontal"),_=D(O,"vertical");q[U].forEach(function(F,b){y[b]=[d[v.get(F)],N[v.get(F)]],E[b]=[],B.has(F)?E[b][0]=B.get(F):E[b][0]=d[v.get(F)],_.has(F)?E[b][1]=_.get(F):E[b][1]=N[v.get(F)]}),I=!0}}if(I){for(var lt=void 0,J=t.transpose(E),Rt=t.transpose(y),Lt=0;Lt<J.length;Lt++)J[Lt]=t.multGamma(J[Lt]),Rt[Lt]=t.multGamma(Rt[Lt]);var vt=t.multMat(J,t.transpose(Rt)),it=s.svd(vt);lt=t.multMat(it.V,t.transpose(it.U));for(var ut=0;ut<v.size;ut++){var Tt=[d[ut],N[ut]],At=[lt[0][0],lt[1][0]],Dt=[lt[0][1],lt[1][1]];d[ut]=t.dotProduct(Tt,At),N[ut]=t.dotProduct(Tt,Dt)}w&&rt(h.relativePlacementConstraint)}}if(i.ENFORCE_CONSTRAINTS){if(h.fixedNodeConstraint&&h.fixedNodeConstraint.length>0){var mt={x:0,y:0};h.fixedNodeConstraint.forEach(function(F,b){var $={x:d[v.get(F.nodeId)],y:N[v.get(F.nodeId)]},Q=F.position,Z=Y(Q,$);mt.x+=Z.x,mt.y+=Z.y}),mt.x/=h.fixedNodeConstraint.length,mt.y/=h.fixedNodeConstraint.length,d.forEach(function(F,b){d[b]+=mt.x}),N.forEach(function(F,b){N[b]+=mt.y}),h.fixedNodeConstraint.forEach(function(F){d[v.get(F.nodeId)]=F.position.x,N[v.get(F.nodeId)]=F.position.y})}if(h.alignmentConstraint){if(h.alignmentConstraint.vertical)for(var xt=h.alignmentConstraint.vertical,St=function(b){var $=new Set;xt[b].forEach(function(at){$.add(at)});var Q=new Set([].concat(f($)).filter(function(at){return R.has(at)})),Z=void 0;Q.size>0?Z=d[v.get(Q.values().next().value)]:Z=k($).x,$.forEach(function(at){R.has(at)||(d[v.get(at)]=Z)})},Vt=0;Vt<xt.length;Vt++)St(Vt);if(h.alignmentConstraint.horizontal)for(var Xt=h.alignmentConstraint.horizontal,Ut=function(b){var $=new Set;Xt[b].forEach(function(at){$.add(at)});var Q=new Set([].concat(f($)).filter(function(at){return R.has(at)})),Z=void 0;Q.size>0?Z=N[v.get(Q.values().next().value)]:Z=k($).y,$.forEach(function(at){R.has(at)||(N[v.get(at)]=Z)})},bt=0;bt<Xt.length;bt++)Ut(bt)}h.relativePlacementConstraint&&function(){var F=new Map,b=new Map,$=new Map,Q=new Map,Z=new Map,at=new Map,gt=new Set,ot=new Set;if(R.forEach(function(Yt){gt.add(Yt),ot.add(Yt)}),h.alignmentConstraint){if(h.alignmentConstraint.vertical)for(var tt=h.alignmentConstraint.vertical,j=function(Et){$.set("dummy"+Et,[]),tt[Et].forEach(function(Ot){F.set(Ot,"dummy"+Et),$.get("dummy"+Et).push(Ot),R.has(Ot)&&gt.add("dummy"+Et)}),Z.set("dummy"+Et,d[v.get(tt[Et][0])])},dt=0;dt<tt.length;dt++)j(dt);if(h.alignmentConstraint.horizontal)for(var wt=h.alignmentConstraint.horizontal,yt=function(Et){Q.set("dummy"+Et,[]),wt[Et].forEach(function(Ot){b.set(Ot,"dummy"+Et),Q.get("dummy"+Et).push(Ot),R.has(Ot)&&ot.add("dummy"+Et)}),at.set("dummy"+Et,N[v.get(wt[Et][0])])},It=0;It<wt.length;It++)yt(It)}var ft=new Map,st=new Map,Ct=function(Et){W.get(Et).forEach(function(Ot){var Jt=void 0,kt=void 0;Ot.direction=="horizontal"?(Jt=F.get(Et)?F.get(Et):Et,F.get(Ot.id)?kt={id:F.get(Ot.id),gap:Ot.gap,direction:Ot.direction}:kt=Ot,ft.has(Jt)?ft.get(Jt).push(kt):ft.set(Jt,[kt]),ft.has(kt.id)||ft.set(kt.id,[])):(Jt=b.get(Et)?b.get(Et):Et,b.get(Ot.id)?kt={id:b.get(Ot.id),gap:Ot.gap,direction:Ot.direction}:kt=Ot,st.has(Jt)?st.get(Jt).push(kt):st.set(Jt,[kt]),st.has(kt.id)||st.set(kt.id,[]))})},ct=!0,ht=!1,Wt=void 0;try{for(var Nt=W.keys()[Symbol.iterator](),Mt;!(ct=(Mt=Nt.next()).done);ct=!0){var Zt=Mt.value;Ct(Zt)}}catch(Yt){ht=!0,Wt=Yt}finally{try{!ct&&Nt.return&&Nt.return()}finally{if(ht)throw Wt}}var Gt=m(ft),$t=m(st),Ft=n(Gt),qt=n($t),_t=p(ft),ue=p(st),Kt=[],te=[];Ft.forEach(function(Yt,Et){Kt[Et]=[],Yt.forEach(function(Ot){_t.get(Ot).length==0&&Kt[Et].push(Ot)})}),qt.forEach(function(Yt,Et){te[Et]=[],Yt.forEach(function(Ot){ue.get(Ot).length==0&&te[Et].push(Ot)})});var ee=D(ft,"horizontal",gt,Z,Kt),jt=D(st,"vertical",ot,at,te),se=function(Et){$.get(Et)?$.get(Et).forEach(function(Ot){d[v.get(Ot)]=ee.get(Et)}):d[v.get(Et)]=ee.get(Et)},re=!0,ie=!1,Me=void 0;try{for(var de=ee.keys()[Symbol.iterator](),Ae;!(re=(Ae=de.next()).done);re=!0){var ve=Ae.value;se(ve)}}catch(Yt){ie=!0,Me=Yt}finally{try{!re&&de.return&&de.return()}finally{if(ie)throw Me}}var qe=function(Et){Q.get(Et)?Q.get(Et).forEach(function(Ot){N[v.get(Ot)]=jt.get(Et)}):N[v.get(Et)]=jt.get(Et)},pe=!0,we=!1,Oe=void 0;try{for(var ye=jt.keys()[Symbol.iterator](),De;!(pe=(De=ye.next()).done);pe=!0){var ve=De.value;qe(ve)}}catch(Yt){we=!0,Oe=Yt}finally{try{!pe&&ye.return&&ye.return()}finally{if(we)throw Oe}}}()}for(var Ht=0;Ht<S.length;Ht++){var Bt=S[Ht];Bt.getChild()==null&&Bt.setCenter(d[v.get(Bt.id)],N[v.get(Bt.id)])}},a.exports=o},551:a=>{a.exports=A}},L={};function g(a){var r=L[a];if(r!==void 0)return r.exports;var e=L[a]={exports:{}};return G[a](e,e.exports,g),e.exports}var l=g(45);return l})()})}(fe)),fe.exports}var Er=le.exports,Re;function mr(){return Re||(Re=1,function(C,X){(function(G,L){C.exports=L(yr())})(Er,function(A){return(()=>{var G={658:a=>{a.exports=Object.assign!=null?Object.assign.bind(Object):function(r){for(var e=arguments.length,f=Array(e>1?e-1:0),i=1;i<e;i++)f[i-1]=arguments[i];return f.forEach(function(u){Object.keys(u).forEach(function(t){return r[t]=u[t]})}),r}},548:(a,r,e)=>{var f=function(){function t(s,o){var c=[],h=!0,T=!1,v=void 0;try{for(var d=s[Symbol.iterator](),N;!(h=(N=d.next()).done)&&(c.push(N.value),!(o&&c.length===o));h=!0);}catch(S){T=!0,v=S}finally{try{!h&&d.return&&d.return()}finally{if(T)throw v}}return c}return function(s,o){if(Array.isArray(s))return s;if(Symbol.iterator in Object(s))return t(s,o);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),i=e(140).layoutBase.LinkedList,u={};u.getTopMostNodes=function(t){for(var s={},o=0;o<t.length;o++)s[t[o].id()]=!0;var c=t.filter(function(h,T){typeof h=="number"&&(h=T);for(var v=h.parent()[0];v!=null;){if(s[v.id()])return!1;v=v.parent()[0]}return!0});return c},u.connectComponents=function(t,s,o,c){var h=new i,T=new Set,v=[],d=void 0,N=void 0,S=void 0,M=!1,P=1,K=[],Y=[],k=function(){var rt=t.collection();Y.push(rt);var n=o[0],m=t.collection();m.merge(n).merge(n.descendants().intersection(s)),v.push(n),m.forEach(function(y){h.push(y),T.add(y),rt.merge(y)});for(var p=function(){n=h.shift();var I=t.collection();n.neighborhood().nodes().forEach(function(x){s.intersection(n.edgesWith(x)).length>0&&I.merge(x)});for(var w=0;w<I.length;w++){var R=I[w];if(d=o.intersection(R.union(R.ancestors())),d!=null&&!T.has(d[0])){var W=d.union(d.descendants());W.forEach(function(x){h.push(x),T.add(x),rt.merge(x),o.has(x)&&v.push(x)})}}};h.length!=0;)p();if(rt.forEach(function(y){s.intersection(y.connectedEdges()).forEach(function(I){rt.has(I.source())&&rt.has(I.target())&&rt.merge(I)})}),v.length==o.length&&(M=!0),!M||M&&P>1){N=v[0],S=N.connectedEdges().length,v.forEach(function(y){y.connectedEdges().length<S&&(S=y.connectedEdges().length,N=y)}),K.push(N.id());var E=t.collection();E.merge(v[0]),v.forEach(function(y){E.merge(y)}),v=[],o=o.difference(E),P++}};do k();while(!M);return c&&K.length>0&&c.set("dummy"+(c.size+1),K),Y},u.relocateComponent=function(t,s,o){if(!o.fixedNodeConstraint){var c=Number.POSITIVE_INFINITY,h=Number.NEGATIVE_INFINITY,T=Number.POSITIVE_INFINITY,v=Number.NEGATIVE_INFINITY;if(o.quality=="draft"){var d=!0,N=!1,S=void 0;try{for(var M=s.nodeIndexes[Symbol.iterator](),P;!(d=(P=M.next()).done);d=!0){var K=P.value,Y=f(K,2),k=Y[0],D=Y[1],rt=o.cy.getElementById(k);if(rt){var n=rt.boundingBox(),m=s.xCoords[D]-n.w/2,p=s.xCoords[D]+n.w/2,E=s.yCoords[D]-n.h/2,y=s.yCoords[D]+n.h/2;m<c&&(c=m),p>h&&(h=p),E<T&&(T=E),y>v&&(v=y)}}}catch(x){N=!0,S=x}finally{try{!d&&M.return&&M.return()}finally{if(N)throw S}}var I=t.x-(h+c)/2,w=t.y-(v+T)/2;s.xCoords=s.xCoords.map(function(x){return x+I}),s.yCoords=s.yCoords.map(function(x){return x+w})}else{Object.keys(s).forEach(function(x){var q=s[x],V=q.getRect().x,U=q.getRect().x+q.getRect().width,et=q.getRect().y,z=q.getRect().y+q.getRect().height;V<c&&(c=V),U>h&&(h=U),et<T&&(T=et),z>v&&(v=z)});var R=t.x-(h+c)/2,W=t.y-(v+T)/2;Object.keys(s).forEach(function(x){var q=s[x];q.setCenter(q.getCenterX()+R,q.getCenterY()+W)})}}},u.calcBoundingBox=function(t,s,o,c){for(var h=Number.MAX_SAFE_INTEGER,T=Number.MIN_SAFE_INTEGER,v=Number.MAX_SAFE_INTEGER,d=Number.MIN_SAFE_INTEGER,N=void 0,S=void 0,M=void 0,P=void 0,K=t.descendants().not(":parent"),Y=K.length,k=0;k<Y;k++){var D=K[k];N=s[c.get(D.id())]-D.width()/2,S=s[c.get(D.id())]+D.width()/2,M=o[c.get(D.id())]-D.height()/2,P=o[c.get(D.id())]+D.height()/2,h>N&&(h=N),T<S&&(T=S),v>M&&(v=M),d<P&&(d=P)}var rt={};return rt.topLeftX=h,rt.topLeftY=v,rt.width=T-h,rt.height=d-v,rt},u.calcParentsWithoutChildren=function(t,s){var o=t.collection();return s.nodes(":parent").forEach(function(c){var h=!1;c.children().forEach(function(T){T.css("display")!="none"&&(h=!0)}),h||o.merge(c)}),o},a.exports=u},816:(a,r,e)=>{var f=e(548),i=e(140).CoSELayout,u=e(140).CoSENode,t=e(140).layoutBase.PointD,s=e(140).layoutBase.DimensionD,o=e(140).layoutBase.LayoutConstants,c=e(140).layoutBase.FDLayoutConstants,h=e(140).CoSEConstants,T=function(d,N){var S=d.cy,M=d.eles,P=M.nodes(),K=M.edges(),Y=void 0,k=void 0,D=void 0,rt={};d.randomize&&(Y=N.nodeIndexes,k=N.xCoords,D=N.yCoords);var n=function(x){return typeof x=="function"},m=function(x,q){return n(x)?x(q):x},p=f.calcParentsWithoutChildren(S,M),E=function W(x,q,V,U){for(var et=q.length,z=0;z<et;z++){var O=q[z],H=null;O.intersection(p).length==0&&(H=O.children());var B=void 0,_=O.layoutDimensions({nodeDimensionsIncludeLabels:U.nodeDimensionsIncludeLabels});if(O.outerWidth()!=null&&O.outerHeight()!=null)if(U.randomize)if(!O.isParent())B=x.add(new u(V.graphManager,new t(k[Y.get(O.id())]-_.w/2,D[Y.get(O.id())]-_.h/2),new s(parseFloat(_.w),parseFloat(_.h))));else{var lt=f.calcBoundingBox(O,k,D,Y);O.intersection(p).length==0?B=x.add(new u(V.graphManager,new t(lt.topLeftX,lt.topLeftY),new s(lt.width,lt.height))):B=x.add(new u(V.graphManager,new t(lt.topLeftX,lt.topLeftY),new s(parseFloat(_.w),parseFloat(_.h))))}else B=x.add(new u(V.graphManager,new t(O.position("x")-_.w/2,O.position("y")-_.h/2),new s(parseFloat(_.w),parseFloat(_.h))));else B=x.add(new u(this.graphManager));if(B.id=O.data("id"),B.nodeRepulsion=m(U.nodeRepulsion,O),B.paddingLeft=parseInt(O.css("padding")),B.paddingTop=parseInt(O.css("padding")),B.paddingRight=parseInt(O.css("padding")),B.paddingBottom=parseInt(O.css("padding")),U.nodeDimensionsIncludeLabels&&(B.labelWidth=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,B.labelHeight=O.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,B.labelPosVertical=O.css("text-valign"),B.labelPosHorizontal=O.css("text-halign")),rt[O.data("id")]=B,isNaN(B.rect.x)&&(B.rect.x=0),isNaN(B.rect.y)&&(B.rect.y=0),H!=null&&H.length>0){var J=void 0;J=V.getGraphManager().add(V.newGraph(),B),W(J,H,V,U)}}},y=function(x,q,V){for(var U=0,et=0,z=0;z<V.length;z++){var O=V[z],H=rt[O.data("source")],B=rt[O.data("target")];if(H&&B&&H!==B&&H.getEdgesBetween(B).length==0){var _=q.add(x.newEdge(),H,B);_.id=O.id(),_.idealLength=m(d.idealEdgeLength,O),_.edgeElasticity=m(d.edgeElasticity,O),U+=_.idealLength,et++}}d.idealEdgeLength!=null&&(et>0?h.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=U/et:n(d.idealEdgeLength)?h.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=50:h.DEFAULT_EDGE_LENGTH=c.DEFAULT_EDGE_LENGTH=d.idealEdgeLength,h.MIN_REPULSION_DIST=c.MIN_REPULSION_DIST=c.DEFAULT_EDGE_LENGTH/10,h.DEFAULT_RADIAL_SEPARATION=c.DEFAULT_EDGE_LENGTH)},I=function(x,q){q.fixedNodeConstraint&&(x.constraints.fixedNodeConstraint=q.fixedNodeConstraint),q.alignmentConstraint&&(x.constraints.alignmentConstraint=q.alignmentConstraint),q.relativePlacementConstraint&&(x.constraints.relativePlacementConstraint=q.relativePlacementConstraint)};d.nestingFactor!=null&&(h.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=c.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=d.nestingFactor),d.gravity!=null&&(h.DEFAULT_GRAVITY_STRENGTH=c.DEFAULT_GRAVITY_STRENGTH=d.gravity),d.numIter!=null&&(h.MAX_ITERATIONS=c.MAX_ITERATIONS=d.numIter),d.gravityRange!=null&&(h.DEFAULT_GRAVITY_RANGE_FACTOR=c.DEFAULT_GRAVITY_RANGE_FACTOR=d.gravityRange),d.gravityCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_STRENGTH=c.DEFAULT_COMPOUND_GRAVITY_STRENGTH=d.gravityCompound),d.gravityRangeCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=c.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=d.gravityRangeCompound),d.initialEnergyOnIncremental!=null&&(h.DEFAULT_COOLING_FACTOR_INCREMENTAL=c.DEFAULT_COOLING_FACTOR_INCREMENTAL=d.initialEnergyOnIncremental),d.tilingCompareBy!=null&&(h.TILING_COMPARE_BY=d.tilingCompareBy),d.quality=="proof"?o.QUALITY=2:o.QUALITY=0,h.NODE_DIMENSIONS_INCLUDE_LABELS=c.NODE_DIMENSIONS_INCLUDE_LABELS=o.NODE_DIMENSIONS_INCLUDE_LABELS=d.nodeDimensionsIncludeLabels,h.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=o.DEFAULT_INCREMENTAL=!d.randomize,h.ANIMATE=c.ANIMATE=o.ANIMATE=d.animate,h.TILE=d.tile,h.TILING_PADDING_VERTICAL=typeof d.tilingPaddingVertical=="function"?d.tilingPaddingVertical.call():d.tilingPaddingVertical,h.TILING_PADDING_HORIZONTAL=typeof d.tilingPaddingHorizontal=="function"?d.tilingPaddingHorizontal.call():d.tilingPaddingHorizontal,h.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=o.DEFAULT_INCREMENTAL=!0,h.PURE_INCREMENTAL=!d.randomize,o.DEFAULT_UNIFORM_LEAF_NODE_SIZES=d.uniformNodeDimensions,d.step=="transformed"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,h.ENFORCE_CONSTRAINTS=!1,h.APPLY_LAYOUT=!1),d.step=="enforced"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!0,h.APPLY_LAYOUT=!1),d.step=="cose"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!1,h.APPLY_LAYOUT=!0),d.step=="all"&&(d.randomize?h.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!0,h.APPLY_LAYOUT=!0),d.fixedNodeConstraint||d.alignmentConstraint||d.relativePlacementConstraint?h.TREE_REDUCTION_ON_INCREMENTAL=!1:h.TREE_REDUCTION_ON_INCREMENTAL=!0;var w=new i,R=w.newGraphManager();return E(R.addRoot(),f.getTopMostNodes(P),w,d),y(w,R,K),I(w,d),w.runLayout(),rt};a.exports={coseLayout:T}},212:(a,r,e)=>{var f=function(){function d(N,S){for(var M=0;M<S.length;M++){var P=S[M];P.enumerable=P.enumerable||!1,P.configurable=!0,"value"in P&&(P.writable=!0),Object.defineProperty(N,P.key,P)}}return function(N,S,M){return S&&d(N.prototype,S),M&&d(N,M),N}}();function i(d,N){if(!(d instanceof N))throw new TypeError("Cannot call a class as a function")}var u=e(658),t=e(548),s=e(657),o=s.spectralLayout,c=e(816),h=c.coseLayout,T=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(N){return 4500},idealEdgeLength:function(N){return 50},edgeElasticity:function(N){return .45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}}),v=function(){function d(N){i(this,d),this.options=u({},T,N)}return f(d,[{key:"run",value:function(){var S=this,M=this.options,P=M.cy,K=M.eles,Y=[],k=[],D=void 0,rt=[];M.fixedNodeConstraint&&(!Array.isArray(M.fixedNodeConstraint)||M.fixedNodeConstraint.length==0)&&(M.fixedNodeConstraint=void 0),M.alignmentConstraint&&(M.alignmentConstraint.vertical&&(!Array.isArray(M.alignmentConstraint.vertical)||M.alignmentConstraint.vertical.length==0)&&(M.alignmentConstraint.vertical=void 0),M.alignmentConstraint.horizontal&&(!Array.isArray(M.alignmentConstraint.horizontal)||M.alignmentConstraint.horizontal.length==0)&&(M.alignmentConstraint.horizontal=void 0)),M.relativePlacementConstraint&&(!Array.isArray(M.relativePlacementConstraint)||M.relativePlacementConstraint.length==0)&&(M.relativePlacementConstraint=void 0);var n=M.fixedNodeConstraint||M.alignmentConstraint||M.relativePlacementConstraint;n&&(M.tile=!1,M.packComponents=!1);var m=void 0,p=!1;if(P.layoutUtilities&&M.packComponents&&(m=P.layoutUtilities("get"),m||(m=P.layoutUtilities()),p=!0),K.nodes().length>0)if(p){var I=t.getTopMostNodes(M.eles.nodes());if(D=t.connectComponents(P,M.eles,I),D.forEach(function(vt){var it=vt.boundingBox();rt.push({x:it.x1+it.w/2,y:it.y1+it.h/2})}),M.randomize&&D.forEach(function(vt){M.eles=vt,Y.push(o(M))}),M.quality=="default"||M.quality=="proof"){var w=P.collection();if(M.tile){var R=new Map,W=[],x=[],q=0,V={nodeIndexes:R,xCoords:W,yCoords:x},U=[];if(D.forEach(function(vt,it){vt.edges().length==0&&(vt.nodes().forEach(function(ut,Tt){w.merge(vt.nodes()[Tt]),ut.isParent()||(V.nodeIndexes.set(vt.nodes()[Tt].id(),q++),V.xCoords.push(vt.nodes()[0].position().x),V.yCoords.push(vt.nodes()[0].position().y))}),U.push(it))}),w.length>1){var et=w.boundingBox();rt.push({x:et.x1+et.w/2,y:et.y1+et.h/2}),D.push(w),Y.push(V);for(var z=U.length-1;z>=0;z--)D.splice(U[z],1),Y.splice(U[z],1),rt.splice(U[z],1)}}D.forEach(function(vt,it){M.eles=vt,k.push(h(M,Y[it])),t.relocateComponent(rt[it],k[it],M)})}else D.forEach(function(vt,it){t.relocateComponent(rt[it],Y[it],M)});var O=new Set;if(D.length>1){var H=[],B=K.filter(function(vt){return vt.css("display")=="none"});D.forEach(function(vt,it){var ut=void 0;if(M.quality=="draft"&&(ut=Y[it].nodeIndexes),vt.nodes().not(B).length>0){var Tt={};Tt.edges=[],Tt.nodes=[];var At=void 0;vt.nodes().not(B).forEach(function(Dt){if(M.quality=="draft")if(!Dt.isParent())At=ut.get(Dt.id()),Tt.nodes.push({x:Y[it].xCoords[At]-Dt.boundingbox().w/2,y:Y[it].yCoords[At]-Dt.boundingbox().h/2,width:Dt.boundingbox().w,height:Dt.boundingbox().h});else{var mt=t.calcBoundingBox(Dt,Y[it].xCoords,Y[it].yCoords,ut);Tt.nodes.push({x:mt.topLeftX,y:mt.topLeftY,width:mt.width,height:mt.height})}else k[it][Dt.id()]&&Tt.nodes.push({x:k[it][Dt.id()].getLeft(),y:k[it][Dt.id()].getTop(),width:k[it][Dt.id()].getWidth(),height:k[it][Dt.id()].getHeight()})}),vt.edges().forEach(function(Dt){var mt=Dt.source(),xt=Dt.target();if(mt.css("display")!="none"&&xt.css("display")!="none")if(M.quality=="draft"){var St=ut.get(mt.id()),Vt=ut.get(xt.id()),Xt=[],Ut=[];if(mt.isParent()){var bt=t.calcBoundingBox(mt,Y[it].xCoords,Y[it].yCoords,ut);Xt.push(bt.topLeftX+bt.width/2),Xt.push(bt.topLeftY+bt.height/2)}else Xt.push(Y[it].xCoords[St]),Xt.push(Y[it].yCoords[St]);if(xt.isParent()){var Ht=t.calcBoundingBox(xt,Y[it].xCoords,Y[it].yCoords,ut);Ut.push(Ht.topLeftX+Ht.width/2),Ut.push(Ht.topLeftY+Ht.height/2)}else Ut.push(Y[it].xCoords[Vt]),Ut.push(Y[it].yCoords[Vt]);Tt.edges.push({startX:Xt[0],startY:Xt[1],endX:Ut[0],endY:Ut[1]})}else k[it][mt.id()]&&k[it][xt.id()]&&Tt.edges.push({startX:k[it][mt.id()].getCenterX(),startY:k[it][mt.id()].getCenterY(),endX:k[it][xt.id()].getCenterX(),endY:k[it][xt.id()].getCenterY()})}),Tt.nodes.length>0&&(H.push(Tt),O.add(it))}});var _=m.packComponents(H,M.randomize).shifts;if(M.quality=="draft")Y.forEach(function(vt,it){var ut=vt.xCoords.map(function(At){return At+_[it].dx}),Tt=vt.yCoords.map(function(At){return At+_[it].dy});vt.xCoords=ut,vt.yCoords=Tt});else{var lt=0;O.forEach(function(vt){Object.keys(k[vt]).forEach(function(it){var ut=k[vt][it];ut.setCenter(ut.getCenterX()+_[lt].dx,ut.getCenterY()+_[lt].dy)}),lt++})}}}else{var E=M.eles.boundingBox();if(rt.push({x:E.x1+E.w/2,y:E.y1+E.h/2}),M.randomize){var y=o(M);Y.push(y)}M.quality=="default"||M.quality=="proof"?(k.push(h(M,Y[0])),t.relocateComponent(rt[0],k[0],M)):t.relocateComponent(rt[0],Y[0],M)}var J=function(it,ut){if(M.quality=="default"||M.quality=="proof"){typeof it=="number"&&(it=ut);var Tt=void 0,At=void 0,Dt=it.data("id");return k.forEach(function(xt){Dt in xt&&(Tt={x:xt[Dt].getRect().getCenterX(),y:xt[Dt].getRect().getCenterY()},At=xt[Dt])}),M.nodeDimensionsIncludeLabels&&(At.labelWidth&&(At.labelPosHorizontal=="left"?Tt.x+=At.labelWidth/2:At.labelPosHorizontal=="right"&&(Tt.x-=At.labelWidth/2)),At.labelHeight&&(At.labelPosVertical=="top"?Tt.y+=At.labelHeight/2:At.labelPosVertical=="bottom"&&(Tt.y-=At.labelHeight/2))),Tt==null&&(Tt={x:it.position("x"),y:it.position("y")}),{x:Tt.x,y:Tt.y}}else{var mt=void 0;return Y.forEach(function(xt){var St=xt.nodeIndexes.get(it.id());St!=null&&(mt={x:xt.xCoords[St],y:xt.yCoords[St]})}),mt==null&&(mt={x:it.position("x"),y:it.position("y")}),{x:mt.x,y:mt.y}}};if(M.quality=="default"||M.quality=="proof"||M.randomize){var Rt=t.calcParentsWithoutChildren(P,K),Lt=K.filter(function(vt){return vt.css("display")=="none"});M.eles=K.not(Lt),K.nodes().not(":parent").not(Lt).layoutPositions(S,M,J),Rt.length>0&&Rt.forEach(function(vt){vt.position(J(vt))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),d}();a.exports=v},657:(a,r,e)=>{var f=e(548),i=e(140).layoutBase.Matrix,u=e(140).layoutBase.SVD,t=function(o){var c=o.cy,h=o.eles,T=h.nodes(),v=h.nodes(":parent"),d=new Map,N=new Map,S=new Map,M=[],P=[],K=[],Y=[],k=[],D=[],rt=[],n=[],m=void 0,p=1e8,E=1e-9,y=o.piTol,I=o.samplingType,w=o.nodeSeparation,R=void 0,W=function(){for(var b=0,$=0,Q=!1;$<R;){b=Math.floor(Math.random()*m),Q=!1;for(var Z=0;Z<$;Z++)if(Y[Z]==b){Q=!0;break}if(!Q)Y[$]=b,$++;else continue}},x=function(b,$,Q){for(var Z=[],at=0,gt=0,ot=0,tt=void 0,j=[],dt=0,wt=1,yt=0;yt<m;yt++)j[yt]=p;for(Z[gt]=b,j[b]=0;gt>=at;){ot=Z[at++];for(var It=M[ot],ft=0;ft<It.length;ft++)tt=N.get(It[ft]),j[tt]==p&&(j[tt]=j[ot]+1,Z[++gt]=tt);D[ot][$]=j[ot]*w}if(Q){for(var st=0;st<m;st++)D[st][$]<k[st]&&(k[st]=D[st][$]);for(var Ct=0;Ct<m;Ct++)k[Ct]>dt&&(dt=k[Ct],wt=Ct)}return wt},q=function(b){var $=void 0;if(b){$=Math.floor(Math.random()*m);for(var Z=0;Z<m;Z++)k[Z]=p;for(var at=0;at<R;at++)Y[at]=$,$=x($,at,b)}else{W();for(var Q=0;Q<R;Q++)x(Y[Q],Q,b)}for(var gt=0;gt<m;gt++)for(var ot=0;ot<R;ot++)D[gt][ot]*=D[gt][ot];for(var tt=0;tt<R;tt++)rt[tt]=[];for(var j=0;j<R;j++)for(var dt=0;dt<R;dt++)rt[j][dt]=D[Y[dt]][j]},V=function(){for(var b=u.svd(rt),$=b.S,Q=b.U,Z=b.V,at=$[0]*$[0]*$[0],gt=[],ot=0;ot<R;ot++){gt[ot]=[];for(var tt=0;tt<R;tt++)gt[ot][tt]=0,ot==tt&&(gt[ot][tt]=$[ot]/($[ot]*$[ot]+at/($[ot]*$[ot])))}n=i.multMat(i.multMat(Z,gt),i.transpose(Q))},U=function(){for(var b=void 0,$=void 0,Q=[],Z=[],at=[],gt=[],ot=0;ot<m;ot++)Q[ot]=Math.random(),Z[ot]=Math.random();Q=i.normalize(Q),Z=i.normalize(Z);for(var tt=E,j=E,dt=void 0;;){for(var wt=0;wt<m;wt++)at[wt]=Q[wt];if(Q=i.multGamma(i.multL(i.multGamma(at),D,n)),b=i.dotProduct(at,Q),Q=i.normalize(Q),tt=i.dotProduct(at,Q),dt=Math.abs(tt/j),dt<=1+y&&dt>=1)break;j=tt}for(var yt=0;yt<m;yt++)at[yt]=Q[yt];for(j=E;;){for(var It=0;It<m;It++)gt[It]=Z[It];if(gt=i.minusOp(gt,i.multCons(at,i.dotProduct(at,gt))),Z=i.multGamma(i.multL(i.multGamma(gt),D,n)),$=i.dotProduct(gt,Z),Z=i.normalize(Z),tt=i.dotProduct(gt,Z),dt=Math.abs(tt/j),dt<=1+y&&dt>=1)break;j=tt}for(var ft=0;ft<m;ft++)gt[ft]=Z[ft];P=i.multCons(at,Math.sqrt(Math.abs(b))),K=i.multCons(gt,Math.sqrt(Math.abs($)))};f.connectComponents(c,h,f.getTopMostNodes(T),d),v.forEach(function(F){f.connectComponents(c,h,f.getTopMostNodes(F.descendants().intersection(h)),d)});for(var et=0,z=0;z<T.length;z++)T[z].isParent()||N.set(T[z].id(),et++);var O=!0,H=!1,B=void 0;try{for(var _=d.keys()[Symbol.iterator](),lt;!(O=(lt=_.next()).done);O=!0){var J=lt.value;N.set(J,et++)}}catch(F){H=!0,B=F}finally{try{!O&&_.return&&_.return()}finally{if(H)throw B}}for(var Rt=0;Rt<N.size;Rt++)M[Rt]=[];v.forEach(function(F){for(var b=F.children().intersection(h);b.nodes(":childless").length==0;)b=b.nodes()[0].children().intersection(h);var $=0,Q=b.nodes(":childless")[0].connectedEdges().length;b.nodes(":childless").forEach(function(Z,at){Z.connectedEdges().length<Q&&(Q=Z.connectedEdges().length,$=at)}),S.set(F.id(),b.nodes(":childless")[$].id())}),T.forEach(function(F){var b=void 0;F.isParent()?b=N.get(S.get(F.id())):b=N.get(F.id()),F.neighborhood().nodes().forEach(function($){h.intersection(F.edgesWith($)).length>0&&($.isParent()?M[b].push(S.get($.id())):M[b].push($.id()))})});var Lt=function(b){var $=N.get(b),Q=void 0;d.get(b).forEach(function(Z){c.getElementById(Z).isParent()?Q=S.get(Z):Q=Z,M[$].push(Q),M[N.get(Q)].push(b)})},vt=!0,it=!1,ut=void 0;try{for(var Tt=d.keys()[Symbol.iterator](),At;!(vt=(At=Tt.next()).done);vt=!0){var Dt=At.value;Lt(Dt)}}catch(F){it=!0,ut=F}finally{try{!vt&&Tt.return&&Tt.return()}finally{if(it)throw ut}}m=N.size;var mt=void 0;if(m>2){R=m<o.sampleSize?m:o.sampleSize;for(var xt=0;xt<m;xt++)D[xt]=[];for(var St=0;St<R;St++)n[St]=[];return o.quality=="draft"||o.step=="all"?(q(I),V(),U(),mt={nodeIndexes:N,xCoords:P,yCoords:K}):(N.forEach(function(F,b){P.push(c.getElementById(b).position("x")),K.push(c.getElementById(b).position("y"))}),mt={nodeIndexes:N,xCoords:P,yCoords:K}),mt}else{var Vt=N.keys(),Xt=c.getElementById(Vt.next().value),Ut=Xt.position(),bt=Xt.outerWidth();if(P.push(Ut.x),K.push(Ut.y),m==2){var Ht=c.getElementById(Vt.next().value),Bt=Ht.outerWidth();P.push(Ut.x+bt/2+Bt/2+o.idealEdgeLength),K.push(Ut.y)}return mt={nodeIndexes:N,xCoords:P,yCoords:K},mt}};a.exports={spectralLayout:t}},579:(a,r,e)=>{var f=e(212),i=function(t){t&&t("layout","fcose",f)};typeof cytoscape<"u"&&i(cytoscape),a.exports=i},140:a=>{a.exports=A}},L={};function g(a){var r=L[a];if(r!==void 0)return r.exports;var e=L[a]={exports:{}};return G[a](e,e.exports,g),e.exports}var l=g(579);return l})()})}(le)),le.exports}var Tr=mr();const Nr=Je(Tr);var Se={L:"left",R:"right",T:"top",B:"bottom"},Fe={L:nt(C=>`${C},${C/2} 0,${C} 0,0`,"L"),R:nt(C=>`0,${C/2} ${C},0 ${C},${C}`,"R"),T:nt(C=>`0,0 ${C},0 ${C/2},${C}`,"T"),B:nt(C=>`${C/2},0 ${C},${C} 0,${C}`,"B")},he={L:nt((C,X)=>C-X+2,"L"),R:nt((C,X)=>C-2,"R"),T:nt((C,X)=>C-X+2,"T"),B:nt((C,X)=>C-2,"B")},Lr=nt(function(C){return zt(C)?C==="L"?"R":"L":C==="T"?"B":"T"},"getOppositeArchitectureDirection"),be=nt(function(C){const X=C;return X==="L"||X==="R"||X==="T"||X==="B"},"isArchitectureDirection"),zt=nt(function(C){const X=C;return X==="L"||X==="R"},"isArchitectureDirectionX"),Qt=nt(function(C){const X=C;return X==="T"||X==="B"},"isArchitectureDirectionY"),Ce=nt(function(C,X){const A=zt(C)&&Qt(X),G=Qt(C)&&zt(X);return A||G},"isArchitectureDirectionXY"),Cr=nt(function(C){const X=C[0],A=C[1],G=zt(X)&&Qt(A),L=Qt(X)&&zt(A);return G||L},"isArchitecturePairXY"),Mr=nt(function(C){return C!=="LL"&&C!=="RR"&&C!=="TT"&&C!=="BB"},"isValidArchitectureDirectionPair"),me=nt(function(C,X){const A=`${C}${X}`;return Mr(A)?A:void 0},"getArchitectureDirectionPair"),Ar=nt(function([C,X],A){const G=A[0],L=A[1];return zt(G)?Qt(L)?[C+(G==="L"?-1:1),X+(L==="T"?1:-1)]:[C+(G==="L"?-1:1),X]:zt(L)?[C+(L==="L"?1:-1),X+(G==="T"?1:-1)]:[C,X+(G==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),wr=nt(function(C){return C==="LT"||C==="TL"?[1,1]:C==="BL"||C==="LB"?[1,-1]:C==="BR"||C==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),Or=nt(function(C,X){return Ce(C,X)?"bend":zt(C)?"horizontal":"vertical"},"getArchitectureDirectionAlignment"),Dr=nt(function(C){return C.type==="service"},"isArchitectureService"),xr=nt(function(C){return C.type==="junction"},"isArchitectureJunction"),Ue=nt(C=>C.data(),"edgeData"),ne=nt(C=>C.data(),"nodeData"),Ye=or.architecture,pt=new gr(()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:Ye,dataStructures:void 0,elements:{}})),Ir=nt(()=>{pt.reset(),sr()},"clear"),Rr=nt(function({id:C,icon:X,in:A,title:G,iconText:L}){if(pt.records.registeredIds[C]!==void 0)throw new Error(`The service id [${C}] is already in use by another ${pt.records.registeredIds[C]}`);if(A!==void 0){if(C===A)throw new Error(`The service [${C}] cannot be placed within itself`);if(pt.records.registeredIds[A]===void 0)throw new Error(`The service [${C}]'s parent does not exist. Please make sure the parent is created before this service`);if(pt.records.registeredIds[A]==="node")throw new Error(`The service [${C}]'s parent is not a group`)}pt.records.registeredIds[C]="node",pt.records.nodes[C]={id:C,type:"service",icon:X,iconText:L,title:G,edges:[],in:A}},"addService"),Sr=nt(()=>Object.values(pt.records.nodes).filter(Dr),"getServices"),Fr=nt(function({id:C,in:X}){pt.records.registeredIds[C]="node",pt.records.nodes[C]={id:C,type:"junction",edges:[],in:X}},"addJunction"),br=nt(()=>Object.values(pt.records.nodes).filter(xr),"getJunctions"),Pr=nt(()=>Object.values(pt.records.nodes),"getNodes"),Te=nt(C=>pt.records.nodes[C],"getNode"),Gr=nt(function({id:C,icon:X,in:A,title:G}){if(pt.records.registeredIds[C]!==void 0)throw new Error(`The group id [${C}] is already in use by another ${pt.records.registeredIds[C]}`);if(A!==void 0){if(C===A)throw new Error(`The group [${C}] cannot be placed within itself`);if(pt.records.registeredIds[A]===void 0)throw new Error(`The group [${C}]'s parent does not exist. Please make sure the parent is created before this group`);if(pt.records.registeredIds[A]==="node")throw new Error(`The group [${C}]'s parent is not a group`)}pt.records.registeredIds[C]="group",pt.records.groups[C]={id:C,icon:X,title:G,in:A}},"addGroup"),Ur=nt(()=>Object.values(pt.records.groups),"getGroups"),Yr=nt(function({lhsId:C,rhsId:X,lhsDir:A,rhsDir:G,lhsInto:L,rhsInto:g,lhsGroup:l,rhsGroup:a,title:r}){if(!be(A))throw new Error(`Invalid direction given for left hand side of edge ${C}--${X}. Expected (L,R,T,B) got ${A}`);if(!be(G))throw new Error(`Invalid direction given for right hand side of edge ${C}--${X}. Expected (L,R,T,B) got ${G}`);if(pt.records.nodes[C]===void 0&&pt.records.groups[C]===void 0)throw new Error(`The left-hand id [${C}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(pt.records.nodes[X]===void 0&&pt.records.groups[C]===void 0)throw new Error(`The right-hand id [${X}] does not yet exist. Please create the service/group before declaring an edge to it.`);const e=pt.records.nodes[C].in,f=pt.records.nodes[X].in;if(l&&e&&f&&e==f)throw new Error(`The left-hand id [${C}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(a&&e&&f&&e==f)throw new Error(`The right-hand id [${X}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);const i={lhsId:C,lhsDir:A,lhsInto:L,lhsGroup:l,rhsId:X,rhsDir:G,rhsInto:g,rhsGroup:a,title:r};pt.records.edges.push(i),pt.records.nodes[C]&&pt.records.nodes[X]&&(pt.records.nodes[C].edges.push(pt.records.edges[pt.records.edges.length-1]),pt.records.nodes[X].edges.push(pt.records.edges[pt.records.edges.length-1]))},"addEdge"),Xr=nt(()=>pt.records.edges,"getEdges"),Hr=nt(()=>{if(pt.records.dataStructures===void 0){const C={},X=Object.entries(pt.records.nodes).reduce((a,[r,e])=>(a[r]=e.edges.reduce((f,i)=>{var s,o;const u=(s=Te(i.lhsId))==null?void 0:s.in,t=(o=Te(i.rhsId))==null?void 0:o.in;if(u&&t&&u!==t){const c=Or(i.lhsDir,i.rhsDir);c!=="bend"&&(C[u]??(C[u]={}),C[u][t]=c,C[t]??(C[t]={}),C[t][u]=c)}if(i.lhsId===r){const c=me(i.lhsDir,i.rhsDir);c&&(f[c]=i.rhsId)}else{const c=me(i.rhsDir,i.lhsDir);c&&(f[c]=i.lhsId)}return f},{}),a),{}),A=Object.keys(X)[0],G={[A]:1},L=Object.keys(X).reduce((a,r)=>r===A?a:{...a,[r]:1},{}),g=nt(a=>{const r={[a]:[0,0]},e=[a];for(;e.length>0;){const f=e.shift();if(f){G[f]=1,delete L[f];const i=X[f],[u,t]=r[f];Object.entries(i).forEach(([s,o])=>{G[o]||(r[o]=Ar([u,t],s),e.push(o))})}}return r},"BFS"),l=[g(A)];for(;Object.keys(L).length>0;)l.push(g(Object.keys(L)[0]));pt.records.dataStructures={adjList:X,spatialMaps:l,groupAlignments:C}}return pt.records.dataStructures},"getDataStructures"),Wr=nt((C,X)=>{pt.records.elements[C]=X},"setElementForId"),Vr=nt(C=>pt.records.elements[C],"getElementById"),Xe=nt(()=>ar({...Ye,...nr().architecture}),"getConfig"),ge={clear:Ir,setDiagramTitle:er,getDiagramTitle:tr,setAccTitle:_e,getAccTitle:je,setAccDescription:Ke,getAccDescription:Qe,getConfig:Xe,addService:Rr,getServices:Sr,addJunction:Fr,getJunctions:br,getNodes:Pr,getNode:Te,addGroup:Gr,getGroups:Ur,addEdge:Yr,getEdges:Xr,setElementForId:Wr,getElementById:Vr,getDataStructures:Hr};function Pt(C){return Xe()[C]}nt(Pt,"getConfigField");var zr=nt((C,X)=>{cr(C,X),C.groups.map(X.addGroup),C.services.map(A=>X.addService({...A,type:"service"})),C.junctions.map(A=>X.addJunction({...A,type:"junction"})),C.edges.map(X.addEdge)},"populateDb"),Br={parse:nt(async C=>{const X=await ur("architecture",C);Pe.debug(X),zr(X,ge)},"parse")},$r=nt(C=>`
  .edge {
    stroke-width: ${C.archEdgeWidth};
    stroke: ${C.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${C.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${C.archGroupBorderColor};
    stroke-width: ${C.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),Zr=$r,ae=nt(C=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${C}</g>`,"wrapIcon"),oe={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:ae('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:ae('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:ae('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:ae('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:ae('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:fr,blank:{body:ae("")}}},kr=nt(async function(C,X){const A=Pt("padding"),G=Pt("iconSize"),L=G/2,g=G/6,l=g/2;await Promise.all(X.edges().map(async a=>{var P,K;const{source:r,sourceDir:e,sourceArrow:f,sourceGroup:i,target:u,targetDir:t,targetArrow:s,targetGroup:o,label:c}=Ue(a);let{x:h,y:T}=a[0].sourceEndpoint();const{x:v,y:d}=a[0].midpoint();let{x:N,y:S}=a[0].targetEndpoint();const M=A+4;if(i&&(zt(e)?h+=e==="L"?-M:M:T+=e==="T"?-M:M+18),o&&(zt(t)?N+=t==="L"?-M:M:S+=t==="T"?-M:M+18),!i&&((P=ge.getNode(r))==null?void 0:P.type)==="junction"&&(zt(e)?h+=e==="L"?L:-L:T+=e==="T"?L:-L),!o&&((K=ge.getNode(u))==null?void 0:K.type)==="junction"&&(zt(t)?N+=t==="L"?L:-L:S+=t==="T"?L:-L),a[0]._private.rscratch){const Y=C.insert("g");if(Y.insert("path").attr("d",`M ${h},${T} L ${v},${d} L${N},${S} `).attr("class","edge"),f){const k=zt(e)?he[e](h,g):h-l,D=Qt(e)?he[e](T,g):T-l;Y.insert("polygon").attr("points",Fe[e](g)).attr("transform",`translate(${k},${D})`).attr("class","arrow")}if(s){const k=zt(t)?he[t](N,g):N-l,D=Qt(t)?he[t](S,g):S-l;Y.insert("polygon").attr("points",Fe[t](g)).attr("transform",`translate(${k},${D})`).attr("class","arrow")}if(c){const k=Ce(e,t)?"XY":zt(e)?"X":"Y";let D=0;k==="X"?D=Math.abs(h-N):k==="Y"?D=Math.abs(T-S)/1.5:D=Math.abs(h-N)/2;const rt=Y.append("g");if(await Ne(rt,c,{useHtmlLabels:!1,width:D,classes:"architecture-service-label"},Le()),rt.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),k==="X")rt.attr("transform","translate("+v+", "+d+")");else if(k==="Y")rt.attr("transform","translate("+v+", "+d+") rotate(-90)");else if(k==="XY"){const n=me(e,t);if(n&&Cr(n)){const m=rt.node().getBoundingClientRect(),[p,E]=wr(n);rt.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*p*E*45})`);const y=rt.node().getBoundingClientRect();rt.attr("transform",`
                translate(${v}, ${d-m.height/2})
                translate(${p*y.width/2}, ${E*y.height/2})
                rotate(${-1*p*E*45}, 0, ${m.height/2})
              `)}}}}}))},"drawEdges"),qr=nt(async function(C,X){const G=Pt("padding")*.75,L=Pt("fontSize"),l=Pt("iconSize")/2;await Promise.all(X.nodes().map(async a=>{const r=ne(a);if(r.type==="group"){const{h:e,w:f,x1:i,y1:u}=a.boundingBox();C.append("rect").attr("x",i+l).attr("y",u+l).attr("width",f).attr("height",e).attr("class","node-bkg");const t=C.append("g");let s=i,o=u;if(r.icon){const c=t.append("g");c.html(`<g>${await Ee(r.icon,{height:G,width:G,fallbackPrefix:oe.prefix})}</g>`),c.attr("transform","translate("+(s+l+1)+", "+(o+l+1)+")"),s+=G,o+=L/2-1-2}if(r.label){const c=t.append("g");await Ne(c,r.label,{useHtmlLabels:!1,width:f,classes:"architecture-service-label"},Le()),c.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),c.attr("transform","translate("+(s+l+4)+", "+(o+l+2)+")")}}}))},"drawGroups"),Jr=nt(async function(C,X,A){for(const G of A){const L=X.append("g"),g=Pt("iconSize");if(G.title){const e=L.append("g");await Ne(e,G.title,{useHtmlLabels:!1,width:g*1.5,classes:"architecture-service-label"},Le()),e.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),e.attr("transform","translate("+g/2+", "+g+")")}const l=L.append("g");if(G.icon)l.html(`<g>${await Ee(G.icon,{height:g,width:g,fallbackPrefix:oe.prefix})}</g>`);else if(G.iconText){l.html(`<g>${await Ee("blank",{height:g,width:g,fallbackPrefix:oe.prefix})}</g>`);const i=l.append("g").append("foreignObject").attr("width",g).attr("height",g).append("div").attr("class","node-icon-text").attr("style",`height: ${g}px;`).append("div").html(G.iconText),u=parseInt(window.getComputedStyle(i.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;i.attr("style",`-webkit-line-clamp: ${Math.floor((g-2)/u)};`)}else l.append("path").attr("class","node-bkg").attr("id","node-"+G.id).attr("d",`M0 ${g} v${-g} q0,-5 5,-5 h${g} q5,0 5,5 v${g} H0 Z`);L.attr("class","architecture-service");const{width:a,height:r}=L._groups[0][0].getBBox();G.width=a,G.height=r,C.setElementForId(G.id,L)}return 0},"drawServices"),Qr=nt(function(C,X,A){A.forEach(G=>{const L=X.append("g"),g=Pt("iconSize");L.append("g").append("rect").attr("id","node-"+G.id).attr("fill-opacity","0").attr("width",g).attr("height",g),L.attr("class","architecture-junction");const{width:a,height:r}=L._groups[0][0].getBBox();L.width=a,L.height=r,C.setElementForId(G.id,L)})},"drawJunctions");lr([{name:oe.prefix,icons:oe}]);Ge.use(Nr);function He(C,X){C.forEach(A=>{X.add({group:"nodes",data:{type:"service",id:A.id,icon:A.icon,label:A.title,parent:A.in,width:Pt("iconSize"),height:Pt("iconSize")},classes:"node-service"})})}nt(He,"addServices");function We(C,X){C.forEach(A=>{X.add({group:"nodes",data:{type:"junction",id:A.id,parent:A.in,width:Pt("iconSize"),height:Pt("iconSize")},classes:"node-junction"})})}nt(We,"addJunctions");function Ve(C,X){X.nodes().map(A=>{const G=ne(A);if(G.type==="group")return;G.x=A.position().x,G.y=A.position().y,C.getElementById(G.id).attr("transform","translate("+(G.x||0)+","+(G.y||0)+")")})}nt(Ve,"positionNodes");function ze(C,X){C.forEach(A=>{X.add({group:"nodes",data:{type:"group",id:A.id,icon:A.icon,label:A.title,parent:A.in},classes:"node-group"})})}nt(ze,"addGroups");function Be(C,X){C.forEach(A=>{const{lhsId:G,rhsId:L,lhsInto:g,lhsGroup:l,rhsInto:a,lhsDir:r,rhsDir:e,rhsGroup:f,title:i}=A,u=Ce(A.lhsDir,A.rhsDir)?"segments":"straight",t={id:`${G}-${L}`,label:i,source:G,sourceDir:r,sourceArrow:g,sourceGroup:l,sourceEndpoint:r==="L"?"0 50%":r==="R"?"100% 50%":r==="T"?"50% 0":"50% 100%",target:L,targetDir:e,targetArrow:a,targetGroup:f,targetEndpoint:e==="L"?"0 50%":e==="R"?"100% 50%":e==="T"?"50% 0":"50% 100%"};X.add({group:"edges",data:t,classes:u})})}nt(Be,"addEdges");function $e(C,X,A){const G=nt((a,r)=>Object.entries(a).reduce((e,[f,i])=>{var s;let u=0;const t=Object.entries(i);if(t.length===1)return e[f]=t[0][1],e;for(let o=0;o<t.length-1;o++)for(let c=o+1;c<t.length;c++){const[h,T]=t[o],[v,d]=t[c];if(((s=A[h])==null?void 0:s[v])===r)e[f]??(e[f]=[]),e[f]=[...e[f],...T,...d];else if(h==="default"||v==="default")e[f]??(e[f]=[]),e[f]=[...e[f],...T,...d];else{const S=`${f}-${u++}`;e[S]=T;const M=`${f}-${u++}`;e[M]=d}}return e},{}),"flattenAlignments"),L=X.map(a=>{const r={},e={};return Object.entries(a).forEach(([f,[i,u]])=>{var s,o,c;const t=((s=C.getNode(f))==null?void 0:s.in)??"default";r[u]??(r[u]={}),(o=r[u])[t]??(o[t]=[]),r[u][t].push(f),e[i]??(e[i]={}),(c=e[i])[t]??(c[t]=[]),e[i][t].push(f)}),{horiz:Object.values(G(r,"horizontal")).filter(f=>f.length>1),vert:Object.values(G(e,"vertical")).filter(f=>f.length>1)}}),[g,l]=L.reduce(([a,r],{horiz:e,vert:f})=>[[...a,...e],[...r,...f]],[[],[]]);return{horizontal:g,vertical:l}}nt($e,"getAlignments");function Ze(C){const X=[],A=nt(L=>`${L[0]},${L[1]}`,"posToStr"),G=nt(L=>L.split(",").map(g=>parseInt(g)),"strToPos");return C.forEach(L=>{const g=Object.fromEntries(Object.entries(L).map(([e,f])=>[A(f),e])),l=[A([0,0])],a={},r={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;l.length>0;){const e=l.shift();if(e){a[e]=1;const f=g[e];if(f){const i=G(e);Object.entries(r).forEach(([u,t])=>{const s=A([i[0]+t[0],i[1]+t[1]]),o=g[s];o&&!a[s]&&(l.push(s),X.push({[Se[u]]:o,[Se[Lr(u)]]:f,gap:1.5*Pt("iconSize")}))})}}}}),X}nt(Ze,"getRelativeConstraints");function ke(C,X,A,G,L,{spatialMaps:g,groupAlignments:l}){return new Promise(a=>{const r=hr("body").append("div").attr("id","cy").attr("style","display:none"),e=Ge({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${Pt("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${Pt("padding")}px`}}]});r.remove(),ze(A,e),He(C,e),We(X,e),Be(G,e);const f=$e(L,g,l),i=Ze(g),u=e.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(t){const[s,o]=t.connectedNodes(),{parent:c}=ne(s),{parent:h}=ne(o);return c===h?1.5*Pt("iconSize"):.5*Pt("iconSize")},edgeElasticity(t){const[s,o]=t.connectedNodes(),{parent:c}=ne(s),{parent:h}=ne(o);return c===h?.45:.001},alignmentConstraint:f,relativePlacementConstraint:i});u.one("layoutstop",()=>{var s;function t(o,c,h,T){let v,d;const{x:N,y:S}=o,{x:M,y:P}=c;d=(T-S+(N-h)*(S-P)/(N-M))/Math.sqrt(1+Math.pow((S-P)/(N-M),2)),v=Math.sqrt(Math.pow(T-S,2)+Math.pow(h-N,2)-Math.pow(d,2));const K=Math.sqrt(Math.pow(M-N,2)+Math.pow(P-S,2));v=v/K;let Y=(M-N)*(T-S)-(P-S)*(h-N);switch(!0){case Y>=0:Y=1;break;case Y<0:Y=-1;break}let k=(M-N)*(h-N)+(P-S)*(T-S);switch(!0){case k>=0:k=1;break;case k<0:k=-1;break}return d=Math.abs(d)*Y,v=v*k,{distances:d,weights:v}}nt(t,"getSegmentWeights"),e.startBatch();for(const o of Object.values(e.edges()))if((s=o.data)!=null&&s.call(o)){const{x:c,y:h}=o.source().position(),{x:T,y:v}=o.target().position();if(c!==T&&h!==v){const d=o.sourceEndpoint(),N=o.targetEndpoint(),{sourceDir:S}=Ue(o),[M,P]=Qt(S)?[d.x,N.y]:[N.x,d.y],{weights:K,distances:Y}=t(d,N,M,P);o.style("segment-distances",Y),o.style("segment-weights",K)}}e.endBatch(),u.run()}),u.run(),e.ready(t=>{Pe.info("Ready",t),a(e)})})}nt(ke,"layoutArchitecture");var Kr=nt(async(C,X,A,G)=>{const L=G.db,g=L.getServices(),l=L.getJunctions(),a=L.getGroups(),r=L.getEdges(),e=L.getDataStructures(),f=rr(X),i=f.append("g");i.attr("class","architecture-edges");const u=f.append("g");u.attr("class","architecture-services");const t=f.append("g");t.attr("class","architecture-groups"),await Jr(L,u,g),Qr(L,u,l);const s=await ke(g,l,a,r,L,e);await kr(i,s),await qr(t,s),Ve(L,s),ir(void 0,f,Pt("padding"),Pt("useMaxWidth"))},"draw"),jr={draw:Kr},si={parser:Br,db:ge,renderer:jr,styles:Zr};export{si as diagram};
