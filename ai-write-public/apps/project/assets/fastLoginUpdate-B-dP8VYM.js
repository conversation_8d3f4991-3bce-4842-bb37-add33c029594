import{a as o}from"./index-7BgHCHvm.js";class r{static triggerImmediateUpdate(t){if(console.log("🚀 FastLoginUpdate: 立即触发用户信息更新"),!t){const e=o.get("userInfo");if(e)try{t=JSON.parse(e)}catch(i){console.error("解析用户信息失败:",i);return}else{console.warn("没有找到用户信息");return}}[new CustomEvent("userInfoUpdated",{detail:{userInfo:t,immediate:!0}}),new CustomEvent("loginStatusChanged",{detail:{type:"login",immediate:!0}}),new CustomEvent("fastLoginUpdate",{detail:{userInfo:t,timestamp:Date.now()}})].forEach(e=>{window.dispatchEvent(e)}),console.log("✅ FastLoginUpdate: 立即更新事件已触发")}static forceRefreshUserInfo(){console.log("🔄 FastLoginUpdate: 强制刷新用户信息");const t=o.get("userInfo"),n=o.get("medxyToken");if(t&&n)try{const e=JSON.parse(t);window.dispatchEvent(new CustomEvent("forceUserInfoRefresh",{detail:{userInfo:e,token:n,timestamp:Date.now()}})),this.triggerImmediateUpdate(e),console.log("✅ FastLoginUpdate: 强制刷新完成")}catch(e){console.error("强制刷新失败:",e)}else console.warn("没有找到完整的登录信息")}static checkAndFixDisplayDelay(){console.log("🔍 FastLoginUpdate: 检查显示延迟问题");const t=o.get("userInfo"),n=o.get("medxyToken");if(t&&n){const e=document.querySelectorAll('[data-testid="login-button"], button:contains("登录")'),i=document.querySelectorAll('[data-testid="user-avatar"], img[alt*="avatar"]');e.length>0&&i.length===0?(console.log("⚠️ 检测到显示延迟问题，执行修复"),this.forceRefreshUserInfo(),setTimeout(()=>{this.checkAndFixDisplayDelay()},500)):console.log("✅ 用户信息显示正常")}}static initFastUpdateListener(){console.log("🎧 FastLoginUpdate: 初始化快速更新监听器"),window.addEventListener("loginSuccess",s=>{var a;console.log("📢 收到登录成功事件，立即更新UI"),(a=s.detail)!=null&&a.userInfo?this.triggerImmediateUpdate(s.detail.userInfo):setTimeout(()=>{this.triggerImmediateUpdate()},5)});let t=o.get("userInfo"),n=o.get("medxyToken");const e=()=>{const s=o.get("userInfo"),a=o.get("medxyToken");(s!==t||a!==n)&&(console.log("📢 检测到Cookie变化，立即更新UI"),s&&a&&this.triggerImmediateUpdate(),t=s,n=a)},i=setInterval(e,100);setTimeout(()=>{clearInterval(i),setInterval(e,1e3)},5e3),console.log("✅ FastLoginUpdate: 监听器初始化完成")}static initDebugTools(){}}r.initFastUpdateListener();r.initDebugTools();export{r as FastLoginUpdate,r as default};
