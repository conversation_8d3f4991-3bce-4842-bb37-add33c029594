import{w as ln,c as N}from"./path-CbwjOpE9.js";import{aL as an,aM as W,aN as O,aO as rn,aP as y,Y as on,aQ as z,aR as _,aS as un,aT as t,aU as sn,aV as tn,aW as fn}from"./index-CaDTTii2.js";function cn(l){return l.innerRadius}function yn(l){return l.outerRadius}function gn(l){return l.startAngle}function mn(l){return l.endAngle}function pn(l){return l&&l.padAngle}function dn(l,h,D,S,v,R,Q,a){var E=D-l,i=S-h,n=Q-v,m=a-R,r=m*E-n*i;if(!(r*r<y))return r=(n*(h-R)-m*(l-v))/r,[l+r*E,h+r*i]}function J(l,h,D,S,v,R,Q){var a=l-D,E=h-S,i=(Q?R:-R)/z(a*a+E*E),n=i*E,m=-i*a,r=l+n,s=h+m,f=D+n,c=S+m,U=(r+f)/2,o=(s+c)/2,p=f-r,g=c-s,A=p*p+g*g,T=v-R,P=r*c-f*s,I=(g<0?-1:1)*z(fn(0,T*T*A-P*P)),L=(P*g-p*I)/A,M=(-P*p-g*I)/A,w=(P*g+p*I)/A,d=(-P*p+g*I)/A,x=L-U,e=M-o,u=w-U,V=d-o;return x*x+e*e>u*u+V*V&&(L=w,M=d),{cx:L,cy:M,x01:-n,y01:-m,x11:L*(v/T-1),y11:M*(v/T-1)}}function vn(){var l=cn,h=yn,D=N(0),S=null,v=gn,R=mn,Q=pn,a=null,E=ln(i);function i(){var n,m,r=+l.apply(this,arguments),s=+h.apply(this,arguments),f=v.apply(this,arguments)-rn,c=R.apply(this,arguments)-rn,U=un(c-f),o=c>f;if(a||(a=n=E()),s<r&&(m=s,s=r,r=m),!(s>y))a.moveTo(0,0);else if(U>on-y)a.moveTo(s*W(f),s*O(f)),a.arc(0,0,s,f,c,!o),r>y&&(a.moveTo(r*W(c),r*O(c)),a.arc(0,0,r,c,f,o));else{var p=f,g=c,A=f,T=c,P=U,I=U,L=Q.apply(this,arguments)/2,M=L>y&&(S?+S.apply(this,arguments):z(r*r+s*s)),w=_(un(s-r)/2,+D.apply(this,arguments)),d=w,x=w,e,u;if(M>y){var V=sn(M/r*O(L)),B=sn(M/s*O(L));(P-=V*2)>y?(V*=o?1:-1,A+=V,T-=V):(P=0,A=T=(f+c)/2),(I-=B*2)>y?(B*=o?1:-1,p+=B,g-=B):(I=0,p=g=(f+c)/2)}var Y=s*W(p),j=s*O(p),C=r*W(T),F=r*O(T);if(w>y){var G=s*W(g),H=s*O(g),K=r*W(A),X=r*O(A),q;if(U<an)if(q=dn(Y,j,K,X,G,H,C,F)){var Z=Y-q[0],$=j-q[1],k=G-q[0],b=H-q[1],nn=1/O(tn((Z*k+$*b)/(z(Z*Z+$*$)*z(k*k+b*b)))/2),en=z(q[0]*q[0]+q[1]*q[1]);d=_(w,(r-en)/(nn-1)),x=_(w,(s-en)/(nn+1))}else d=x=0}I>y?x>y?(e=J(K,X,Y,j,s,x,o),u=J(G,H,C,F,s,x,o),a.moveTo(e.cx+e.x01,e.cy+e.y01),x<w?a.arc(e.cx,e.cy,x,t(e.y01,e.x01),t(u.y01,u.x01),!o):(a.arc(e.cx,e.cy,x,t(e.y01,e.x01),t(e.y11,e.x11),!o),a.arc(0,0,s,t(e.cy+e.y11,e.cx+e.x11),t(u.cy+u.y11,u.cx+u.x11),!o),a.arc(u.cx,u.cy,x,t(u.y11,u.x11),t(u.y01,u.x01),!o))):(a.moveTo(Y,j),a.arc(0,0,s,p,g,!o)):a.moveTo(Y,j),!(r>y)||!(P>y)?a.lineTo(C,F):d>y?(e=J(C,F,G,H,r,-d,o),u=J(Y,j,K,X,r,-d,o),a.lineTo(e.cx+e.x01,e.cy+e.y01),d<w?a.arc(e.cx,e.cy,d,t(e.y01,e.x01),t(u.y01,u.x01),!o):(a.arc(e.cx,e.cy,d,t(e.y01,e.x01),t(e.y11,e.x11),!o),a.arc(0,0,r,t(e.cy+e.y11,e.cx+e.x11),t(u.cy+u.y11,u.cx+u.x11),o),a.arc(u.cx,u.cy,d,t(u.y11,u.x11),t(u.y01,u.x01),!o))):a.arc(0,0,r,T,A,o)}if(a.closePath(),n)return a=null,n+""||null}return i.centroid=function(){var n=(+l.apply(this,arguments)+ +h.apply(this,arguments))/2,m=(+v.apply(this,arguments)+ +R.apply(this,arguments))/2-an/2;return[W(m)*n,O(m)*n]},i.innerRadius=function(n){return arguments.length?(l=typeof n=="function"?n:N(+n),i):l},i.outerRadius=function(n){return arguments.length?(h=typeof n=="function"?n:N(+n),i):h},i.cornerRadius=function(n){return arguments.length?(D=typeof n=="function"?n:N(+n),i):D},i.padRadius=function(n){return arguments.length?(S=n==null?null:typeof n=="function"?n:N(+n),i):S},i.startAngle=function(n){return arguments.length?(v=typeof n=="function"?n:N(+n),i):v},i.endAngle=function(n){return arguments.length?(R=typeof n=="function"?n:N(+n),i):R},i.padAngle=function(n){return arguments.length?(Q=typeof n=="function"?n:N(+n),i):Q},i.context=function(n){return arguments.length?(a=n??null,i):a},i}export{vn as a};
