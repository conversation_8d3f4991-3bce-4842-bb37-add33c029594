package cn.iocoder.yudao.framework.common.util.spring;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.ResourceBundle;

@Component
public class MessageUtil {
    public static final String BASE_NAME = "i18n/messages";

    public static String getMessage(Integer code) {
        return getMessage(code, null);
    }

    public static String getMessage(Integer code, String defaultStr) {
        if (defaultStr!=null && defaultStr.startsWith("ERROR_")) {
            return getMessage(defaultStr, defaultStr);
        }
        return getMessage("ERROR_"+code, defaultStr);
    }

    public static String getMessage(String code) {
        return getMessage(code, null);
    }

    public static String getMessage(String code, String defaultStr) {
        try {
            Locale locale = LocaleContextHolder.getLocale();
            if (locale == null || "und".equals(locale.getLanguage())) {
                locale = Locale.SIMPLIFIED_CHINESE;
            }
            ResourceBundle resourceBundle = ResourceBundle.getBundle(BASE_NAME, locale);
            String messagePattern = resourceBundle.getString(code);
            if (defaultStr == null) {
                return messagePattern;
            }
            return MessageFormat.format(messagePattern, defaultStr);
        } catch (Exception e) {
//            e.printStackTrace();
            return (defaultStr==null||defaultStr=="")?code:defaultStr;
        }
    }
}
